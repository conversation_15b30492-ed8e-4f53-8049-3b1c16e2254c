import React, { createContext, useContext, useMemo } from 'react';
import { getStarCount, getClassificationName } from '../utils/hotelUtils';

const HotelContext = createContext();

export const HotelProvider = ({ children, hotelDetails, searchCriteria }) => {
  const processedHotelDetails = useMemo(() => ({
    ...hotelDetails,
    id: hotelDetails?.id || hotelDetails?.hotelId,
    name: hotelDetails?.name || hotelDetails?.hotelName || 'Unknown Hotel',
    city: hotelDetails?.city || hotelDetails?.cityData?.cityName || 'Unknown City',
    country: hotelDetails?.country || hotelDetails?.cityData?.countryData?.countryName || 'Unknown Country',
    address: hotelDetails?.address || hotelDetails?.hotelStreetAddress || 'Unknown Address',
    description: hotelDetails?.description || hotelDetails?.description1 || 'No description available.',
    description2: hotelDetails?.description2 || '',
    rating: hotelDetails?.rating || (hotelDetails?.ratingCode ? (hotelDetails.ratingCode / 100).toFixed(1) : 9.2),
    starCount: getStarCount(hotelDetails),
    classificationName: getClassificationName(hotelDetails),
    reviewCount: hotelDetails?.reviewCount || 9504,
    images: hotelDetails?.images?.length > 0 ? hotelDetails.images : defaultImages,
    facilities: hotelDetails?.facilities?.length > 0 ? hotelDetails.facilities : defaultFacilities,
    geoLat: parseFloat(hotelDetails?.geoLat),
    geoLong: parseFloat(hotelDetails?.geoLong),
    hotelPhone: hotelDetails?.hotelPhone || hotelDetails?.phone || hotelDetails?.phoneNumber || 'N/A',
    regionName: hotelDetails?.regionName || hotelDetails?.region || hotelDetails?.city || 'N/A',
    checkInTime: hotelDetails?.checkInTime,
    checkOutTime: hotelDetails?.checkOutTime,
    rooms: hotelDetails?.rooms || [],
  }), [hotelDetails]);

  const normalizedSearchCriteria = useMemo(() => ({
    adults: searchCriteria?.adults || 2,
    children: searchCriteria?.children || 0,
    rooms: searchCriteria?.rooms || 1,
    dateRange: searchCriteria?.dateRange || {
      startDate: new Date('2025-09-01'),
      endDate: new Date('2025-09-05'),
    },
    cityCode: searchCriteria?.cityCode || 7674,
    destination: searchCriteria?.destination || 'PARIS, FRANCE',
    flexibleDays: searchCriteria?.flexibleDays || 0,
    promoCode: searchCriteria?.promoCode || null,
  }), [searchCriteria]);

  return (
    <HotelContext.Provider value={{ hotelDetails: processedHotelDetails, searchCriteria: normalizedSearchCriteria }}>
      {children}
    </HotelContext.Provider>
  );
};

export const useHotelContext = () => useContext(HotelContext);