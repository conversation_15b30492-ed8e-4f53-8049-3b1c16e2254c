import React, { useEffect, useRef, useCallback, useMemo, useReducer, useState } from 'react';
import { ChevronDown } from 'lucide-react';
import { CountrySelect, StateSelect, CitySelect } from 'react-country-state-city';
import 'react-country-state-city/dist/react-country-state-city.css';
import './DetailsForm.css';
import DatePicker from 'react-date-picker';
import 'react-date-picker/dist/DatePicker.css';
import 'react-calendar/dist/Calendar.css';
import { Checkbox, FormControlLabel, Radio, RadioGroup, Grid } from '@mui/material';
import { debounce } from 'lodash';
import { getCountries, getCountryCallingCode } from 'libphonenumber-js';

// Reusable Components (unchanged)
const FormInput = ({ label, type = 'text', value, onChange, placeholder, className = '', inputClassName = '', prefix = null, error, touched, name, ...props }) => {
  const handleInputChange = (e) => {
    let newValue = e.target.value;
    if (name.includes('phoneNumber')) {
      newValue = newValue.replace(/[^0-9]/g, '');
      if (newValue.length > 15) newValue = newValue.slice(0, 15);
    }
    if (name.includes('email')) {
      newValue = newValue.replace(/[^a-zA-Z0-9._%+-@]/g, '');
    }
    const syntheticEvent = { ...e, target: { ...e.target, value: newValue, name: e.target.name } };
    onChange(syntheticEvent);
  };
  return (
    <div className={`border border-darkBlue rounded-2xl px-4 py-2 ${className}`}>
      <label className="text-xs text-black font-light">{label}</label>
      <div className="flex items-center mt-1">
        {prefix && <div className="flex items-center text-black mr-2">{prefix}</div>}
        <input
          type={type}
          value={value || ''}
          onChange={handleInputChange}
          className={`w-full text-black text-base focus:outline-none ${inputClassName}`}
          placeholder={placeholder}
          name={name}
          inputMode={name.includes('phoneNumber') ? 'numeric' : name.includes('email') ? 'text' : 'text'}
          pattern={name.includes('phoneNumber') ? '^[0-9]{7,15}$' : name.includes('email') ? '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$' : undefined}
          {...props}
        />
      </div>
      {touched && error && <div className="text-red text-sm mt-1">{error}</div>}
    </div>
  );
};

const PhoneCountryCodeSelect = ({ value, onChange, className = '' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const countries = useMemo(
    () => getCountries().map((country) => ({
      code: country,
      callingCode: getCountryCallingCode(country),
      name: new Intl.DisplayNames(['en'], { type: 'region' }).of(country),
    })),
    []
  );
  const filteredCountries = useMemo(
    () => countries.filter((country) => country.name.toLowerCase().includes(searchTerm.toLowerCase()) || country.callingCode.includes(searchTerm)),
    [countries, searchTerm]
  );
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) setIsOpen(false);
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);
  return (
    <div ref={dropdownRef} className={`phone-code-container relative ${className}`}>
      <div className="flex items-center cursor-pointer border-r border-darkBlue pr-2" onClick={() => setIsOpen(!isOpen)}>
        <span className="text-black">+{value || 'Select'}</span>
        <ChevronDown className="w-4 h-4 text-black ml-1" />
      </div>
      {isOpen && (
        <div className="absolute bg-white border border-darkBlue rounded-xl shadow-lg z-10 md:w-[265px] w-full mt-1 -ml-4 max-h-60 overflow-y-auto scrollbar-hide">
          <input
            type="text"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search country or code"
            className="w-full p-2 text-sm text-black focus:outline-none border-b border-darkBlue"
          />
          {filteredCountries.length > 0 ? (
            filteredCountries.map((country) => (
              <div
                key={country.code}
                className={`p-2 cursor-pointer hover:bg-slate-200 ${value === country.callingCode ? 'selected bg-darkBlue text-white' : ''}`}
                onClick={() => {
                  onChange(country.callingCode);
                  setIsOpen(false);
                  setSearchTerm('');
                }}
              >
                <span className="text-sm">{`${country.name} (+${country.callingCode})`}</span>
              </div>
            ))
          ) : (
            <div className="p-2 text-sm">No countries found</div>
          )}
        </div>
      )}
    </div>
  );
};

const FormDropdown = ({
  label,
  value,
  onChange,
  placeholder,
  options = [],
  searchTerm,
  onSearchChange,
  isOpen,
  onToggle,
  dropdownRef,
  renderOption,
  emptyMessage = 'No options found',
  className = '',
  disabled = false,
}) => (
  <div ref={dropdownRef} className={`relative border border-darkBlue rounded-xl px-2 py-2 ${className}`}>
    <label className="text-xs text-black font-light">{label}</label>
    <div
      className={`flex justify-between items-center mt-1 ${disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
      onClick={disabled ? undefined : onToggle}
    >
      <input
        type="text"
        value={searchTerm}
        onChange={onSearchChange}
        className="w-full text-black text-base focus:outline-none"
        placeholder={placeholder}
        onClick={(e) => e.stopPropagation()}
        disabled={disabled}
      />
      <ChevronDown className="w-4 h-4 text-black" />
    </div>
    {isOpen && !disabled && (
      <div className="absolute border border-darkBlue bg-white -ml-6 rounded-2xl shadow-lg z-10 w-full md:w-28 mt-1 max-h-60 overflow-y-auto scrollbar-hide">
        {options.length > 0 ? (
          options.map((option, index) => (
            <div
              key={index}
              className={`p-1 cursor-pointer hover:bg-slate-200 ${value === option.salutationName ? 'selected bg-darkBlue text-white' : ''}`}
              onClick={() => onChange(option)}
            >
              {renderOption ? renderOption(option) : <div className="text-sm font-medium">{option.salutationName || option}</div>}
            </div>
          ))
        ) : (
          <div className="p-2 text-sm">{emptyMessage}</div>
        )}
      </div>
    )}
  </div>
);

const FormCheckbox = ({ checked, onChange, label, className = '' }) => (
  <FormControlLabel
    control={<Checkbox checked={checked} onChange={onChange} sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }} />}
    label={label}
    className={`text-smokyGray text-sm font-light mb-4 ${className}`}
  />
);

const FormRadioGroup = ({
  label,
  value,
  onChange,
  options,
  name,
  row = false,
  className = '',
  optional = false,
}) => (
  <div className={`w-full py-2 space-y-2 ${className}`}>
    <h2 className="text-base font-medium text-smokyGray">
      {label} {optional && <span className="font-normal text-xs">(optional)</span>}
    </h2>
    <RadioGroup row={row} name={name} value={value} onChange={onChange} className={row ? 'flex gap-8' : 'space-y-2'}>
      {options.map(({ label: optionLabel, value: optionValue }) => (
        <FormControlLabel
          key={optionValue}
          value={optionValue}
          control={<Radio sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }} />}
          label={<span className="text-sm font-light text-smokyGray">{optionLabel}</span>}
        />
      ))}
    </RadioGroup>
  </div>
);

const FormDatePicker = ({ label, value, onChange, className = '' }) => (
  <div className={`border border-darkBlue rounded-2xl px-4 py-2 ${className}`}>
    <label className="text-xs text-black font-light">{label}</label>
    <DatePicker value={value} onChange={onChange} className="w-full text-black text-base focus:outline-none mt-1" />
  </div>
);

const CollapsibleSection = ({ isOpen, children, className = '' }) => (
  <div className={`overflow-hidden transition-all duration-700 ease-in-out ${isOpen ? 'max-h-[1000px] mt-4 opacity-100' : 'max-h-0 mt-0 opacity-0'}`}>
    {children}
  </div>
);

// Dropdown State Reducer
const dropdownReducer = (state, action) => {
  switch (action.type) {
    case 'TOGGLE':
      return { ...state, [action.payload]: !state[action.payload] };
    default:
      return state;
  }
};

const DetailsForm = ({
  onChange,
  showFields = {},
  defaultValues = {},
  title = 'DetailsForm',
  showCompanyToggle = false,
  showTermsCheckbox = false,
  showBookingFor = false,
  showTravelPurpose = false,
  formikProps = {},
}) => {
  const { values, errors, touched, handleChange, setFieldValue, passengerIndex } = formikProps;
  const [dropdownStates, dispatchDropdown] = useReducer(dropdownReducer, {
    showTitleDropdown: false,
    showGenderDropdown: false,
  });
  const [showCompanyFields, setShowCompanyFields] = useState(defaultValues.showCompanyFields || false);
  const dropdownRefs = useRef({ title: null, gender: null }).current;

  // Debounced search for title dropdown
  const debouncedSetTitleSearch = useCallback(
    debounce((value) => setFieldValue(`passengers[${passengerIndex}].title`, value), 300),
    [passengerIndex, setFieldValue]
  );

  // Handle click outside to close dropdowns
  useEffect(() => {
    const handleClickOutside = (event) => {
      Object.entries(dropdownRefs).forEach(([key, ref]) => {
        if (ref && !ref.contains(event.target)) {
          dispatchDropdown({ type: 'TOGGLE', payload: `show${key.charAt(0).toUpperCase() + key.slice(1)}Dropdown` });
        }
      });
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [dropdownRefs]);

  // Handle title selection
  const handleTitleSelect = useCallback(
    (newTitle) => {
      setFieldValue(`passengers[${passengerIndex}].title`, newTitle.salutationName);
      dispatchDropdown({ type: 'TOGGLE', payload: 'showTitleDropdown' });
    },
    [passengerIndex, setFieldValue]
  );

  // Sync nationality with country when sameNationality is checked
  useEffect(() => {
    if (values.sameNationality && values.country && values.countryCode) {
      setFieldValue(`passengers[${passengerIndex}].nationality`, values.country);
      setFieldValue(`passengers[${passengerIndex}].nationalityCode`, values.countryCode);
    } else if (!values.sameNationality) {
      setFieldValue(`passengers[${passengerIndex}].nationality`, '');
      setFieldValue(`passengers[${passengerIndex}].nationalityCode`, '');
    }
  }, [values.sameNationality, values.country, values.countryCode, passengerIndex, setFieldValue]);

  // Memoized options
  const titleOptions = useMemo(
    () => [
      { salutationId: '147', salutationName: 'Mr.' },
      { salutationId: '148', salutationName: 'Ms.' },
      { salutationId: '149', salutationName: 'Mrs.' },
      { salutationId: '15134', salutationName: 'Miss' },
      { salutationId: '558', salutationName: 'Dr.' },
      { salutationId: '1671', salutationName: 'Madame' },
      { salutationId: '1328', salutationName: 'Sir' },
      { salutationId: '3801', salutationName: 'Sir/Madam' },
      { salutationId: '14632', salutationName: 'Child' },
      { salutationId: '9234', salutationName: 'Messrs.' },
      { salutationId: '74185', salutationName: 'Monsieur' },
      { salutationId: '74195', salutationName: 'Mademoiselle' },
    ],
    []
  );

  const bookingForOptions = useMemo(
    () => [
      { label: 'I am the main guest', value: 'self' },
      { label: 'Booking is for someone else', value: 'other' },
    ],
    []
  );

  const travelPurposeOptions = useMemo(
    () => [
      { label: 'Yes', value: 'Yes' },
      { label: 'No', value: 'No' },
    ],
    []
  );

  // Render option for title dropdown
  const renderSimpleOption = useCallback(
    (option) => (
      <div className="p-3 cursor-pointer hover:bg-gray-100 flex items-center">
        <div className="text-sm font-medium">{option.salutationName}</div>
      </div>
    ),
    []
  );

  // Helper functions to render field sections
  const renderCompanySection = () => (
    <>
      {showFields.companyToggle && (
        <FormCheckbox
          checked={showCompanyFields}
          onChange={(e) => setShowCompanyFields(e.target.checked)}
          label="Need an invoice for your company"
        />
      )}
      <CollapsibleSection isOpen={showCompanyFields}>
        {showFields.company && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <FormInput
              label="Company"
              value={values.company || ''}
              onChange={handleChange}
              placeholder="Company"
              name={`passengers[${passengerIndex}].company`}
            />
            {showFields.vatRegNo && (
              <FormInput
                label="VAT Reg No"
                value={values.vatRegNo || ''}
                onChange={handleChange}
                placeholder="VAT Registration Number"
                name={`passengers[${passengerIndex}].vatRegNo`}
              />
            )}
          </div>
        )}
      </CollapsibleSection>
    </>
  );

  const renderNameSection = () => (
    <Grid container columns={12} columnSpacing={2} rowSpacing={2}>
      {showFields.title && (
        <Grid width={{ xs: '100%', lg: '16%' }}>
          <FormDropdown
            label="Title"
            searchTerm={values.title || 'Select here'}
            onSearchChange={(e) => debouncedSetTitleSearch(e.target.value)}
            placeholder="Select title"
            options={titleOptions}
            isOpen={dropdownStates.showTitleDropdown}
            onToggle={() => dispatchDropdown({ type: 'TOGGLE', payload: 'showTitleDropdown' })}
            onChange={handleTitleSelect}
            dropdownRef={dropdownRefs.title}
            renderOption={renderSimpleOption}
            emptyMessage="No titles found"
            value={values.title}
            className="no-border"
          />
        </Grid>
      )}
      {showFields.firstName && (
        <Grid width={{ xs: '100%', lg: '38.5%' }}>
          <FormInput
            label="First Name"
            value={values.firstName}
            onChange={handleChange}
            placeholder="First name"
            error={errors.firstName}
            touched={touched.firstName}
            name={`passengers[${passengerIndex}].firstName`}
          />
        </Grid>
      )}
      {showFields.lastName && (
        <Grid width={{ xs: '100%', lg: '38.5%' }}>
          <FormInput
            label="Last Name"
            value={values.lastName}
            onChange={handleChange}
            placeholder="Last name"
            error={errors.lastName}
            touched={touched.lastName}
            name={`passengers[${passengerIndex}].lastName`}
          />
        </Grid>
      )}
    </Grid>
  );

  const renderAddressSection = () => (
    (showFields.streetName || showFields.houseNumber) && (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
        {showFields.houseNumber && (
          <FormInput
            label="House Number"
            value={values.houseNumber}
            onChange={handleChange}
            placeholder="House number"
            error={errors.houseNumber}
            touched={touched.houseNumber}
            name={`passengers[${passengerIndex}].houseNumber`}
          />
        )}
        {showFields.streetName && (
          <FormInput
            label="Street Name"
            value={values.streetName}
            onChange={handleChange}
            placeholder="Street name"
            error={errors.streetName}
            touched={touched.streetName}
            name={`passengers[${passengerIndex}].streetName`}
          />
        )}
      </div>
    )
  );

  const renderPostalCountrySection = () => (
    (showFields.postalCode || showFields.country) && (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
        {showFields.postalCode && (
          <FormInput
            label="Postal Code"
            value={values.postalCode}
            onChange={handleChange}
            placeholder="Postal code"
            error={errors.postalCode}
            touched={touched.postalCode}
            name={`passengers[${passengerIndex}].postalCode`}
          />
        )}
        {showFields.country && (
          <div className="border border-darkBlue rounded-2xl px-4 py-2">
            <label className="text-xs text-black font-light">Country</label>
            <CountrySelect
              className="w-full text-black text-base mt-1"
              placeHolder="Search country"
              showFlag={true}
              value={values.country ? { name: values.country, id: values.countryCode } : null}
              onChange={(country) => {
                try {
                  const phoneCode = getCountryCallingCode(country.iso2);
                  setFieldValue(`passengers[${passengerIndex}].country`, country.name);
                  setFieldValue(`passengers[${passengerIndex}].countryCode`, country.id);
                  setFieldValue(`passengers[${passengerIndex}].state`, '');
                  setFieldValue(`passengers[${passengerIndex}].stateCode`, '');
                  setFieldValue(`passengers[${passengerIndex}].city`, '');
                  setFieldValue(`passengers[${passengerIndex}].phoneCode`, phoneCode);
                  if (values.sameNationality) {
                    setFieldValue(`passengers[${passengerIndex}].nationality`, country.name);
                    setFieldValue(`passengers[${passengerIndex}].nationalityCode`, country.id);
                  }
                } catch (error) {
                  setFieldValue(`passengers[${passengerIndex}].phoneCode`, '');
                }
              }}
            />
          </div>
        )}
      </div>
    )
  );

  const renderStateCitySection = () => (
    (showFields.state || showFields.city) && (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
        {showFields.state && (
          <div className="border border-darkBlue rounded-2xl px-4 py-2">
            <label className="text-xs text-black font-light">State/Province</label>
            <StateSelect
              className="w-full text-black text-base mt-1"
              countryid={values.countryCode || null}
              placeHolder="Search state"
              value={values.state ? { name: values.state, id: values.stateCode } : null}
              onChange={(state) => {
                setFieldValue(`passengers[${passengerIndex}].state`, state.name);
                setFieldValue(`passengers[${passengerIndex}].stateCode`, state.id);
                setFieldValue(`passengers[${passengerIndex}].city`, '');
              }}
              disabled={!values.countryCode}
            />
          </div>
        )}
        {showFields.city && (
          <div className="border border-darkBlue rounded-2xl px-4 py-2">
            <label className="text-xs text-black font-light">City</label>
            <CitySelect
              className="w-full text-black text-base mt-1"
              countryid={values.countryCode || null}
              stateid={values.stateCode || null}
              placeHolder="Search city"
              value={values.city ? { name: values.city, id: values.city } : null}
              onChange={(city) => {
                setFieldValue(`passengers[${passengerIndex}].city`, city.name);
              }}
              disabled={!values.stateCode}
            />
          </div>
        )}
      </div>
    )
  );

  const renderContactSection = () => (
    (showFields.phoneNumber || showFields.email) && (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {showFields.phoneNumber && (
          <div className="border border-darkBlue rounded-2xl px-4 py-1">
            <label className="text-xs text-black font-light">Phone Number</label>
            <div className="flex items-center">
              <PhoneCountryCodeSelect
                value={values.phoneCode}
                onChange={(phoneCode) => setFieldValue(`passengers[${passengerIndex}].phoneCode`, phoneCode)}
                className="mr-2 phone-code-container"
              />
              <FormInput
                type="tel"
                inputMode="numeric"
                pattern="^[0-9]{7,15}$"
                value={values.phoneNumber}
                onChange={handleChange}
                placeholder="Phone number"
                error={errors.phoneNumber}
                touched={touched.phoneNumber}
                name={`passengers[${passengerIndex}].phoneNumber`}
                className="flex-1 no-border"
              />
            </div>
          </div>
        )}
        {showFields.email && (
          <FormInput
            label="Email Address"
            type="email"
            inputMode="email"
            pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$"
            value={values.email}
            onChange={handleChange}
            placeholder="Email"
            error={errors.email}
            touched={touched.email}
            name={`passengers[${passengerIndex}].email`}
          />
        )}
      </div>
    )
  );

  const renderNationalitySection = () => (
    showFields.nationality && (
      <div className="border border-darkBlue rounded-2xl px-4 py-2 mt-4">
        <label className="text-xs text-black font-light">Nationality</label>
        <CountrySelect
          className={`w-full text-black text-base mt-1 ${values.sameNationality ? 'opacity-50' : ''}`}
          placeHolder={values.sameNationality ? `${values.country}` : 'Search nationality'}
          showFlag={true}
          value={values.nationality && values.nationalityCode ? { name: values.nationality, id: values.nationalityCode } : null}
          onChange={(country) => {
            setFieldValue(`passengers[${passengerIndex}].nationality`, country.name);
            setFieldValue(`passengers[${passengerIndex}].nationalityCode`, country.id);
          }}
          disabled={values.sameNationality}
        />
        {/* {values.sameNationality && values.country && (
          <p className="text-xs text-smokyGray mt-1">Nationality set to {values.country}</p>
        )} */}
      </div>
    )
  );

  const renderAdditionalOptions = () => (
    <>
      {showFields.nationality && (
        <FormCheckbox
          checked={values.sameNationality || false}
          onChange={(e) => {
            setFieldValue(`passengers[${passengerIndex}].sameNationality`, e.target.checked);
            if (!e.target.checked) {
              setFieldValue(`passengers[${passengerIndex}].nationality`, '');
              setFieldValue(`passengers[${passengerIndex}].nationalityCode`, '');
            }
          }}
          label="Country and nationality are the same"
          className="mt-4"
        />
      )}
      {showFields.termsChecked && (
        <FormCheckbox
          checked={values.termsChecked || false}
          onChange={(e) => setFieldValue(`passengers[${passengerIndex}].termsChecked`, e.target.checked)}
          label="Yes, I'd like free paperless confirmation (recommended)"
          className="mt-4"
        />
      )}
      {showFields.bookingFor && (
        <FormRadioGroup
          label="Who are you booking for?"
          name={`passengers[${passengerIndex}].bookingFor`}
          value={values.bookingFor || ''}
          onChange={handleChange}
          options={bookingForOptions}
          optional={true}
          className="border-t border-darkBlue mt-4"
        />
      )}
      {showFields.travelPurpose && (
        <FormRadioGroup
          label="Are you travelling for work?"
          name={`passengers[${passengerIndex}].travelPurpose`}
          value={values.travelPurpose || ''}
          onChange={handleChange}
          options={travelPurposeOptions}
          row={true}
          optional={true}
        />
      )}
    </>
  );

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-2xl border border-darkBlue font-inter space-y-4">
      <h2 className="font-medium text-lg text-black">{title}:</h2>
      {renderCompanySection()}
      {renderNameSection()}
      {renderAddressSection()}
      {renderPostalCountrySection()}
      {renderStateCitySection()}
      {renderContactSection()}
      {renderNationalitySection()}
      {renderAdditionalOptions()}
    </div>
  );
};

export default DetailsForm;