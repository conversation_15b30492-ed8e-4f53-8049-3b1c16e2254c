
import React from 'react'
import { useState } from "react";
import { Formik, Field, Form, ErrorMessage } from "formik";
import * as Yup from "yup";
import { ChevronDown, ChevronUp, Clock, Bookmark, Moon, Star, ArrowLeftRight } from "lucide-react";
import OutboundFlight from "../assets/view/OutboundFlight.svg";
import ReturnFlight from "../assets/view/ReturnFlight.svg";
import Line from "../assets/view/Arrow.svg";
import QatarAirwaysLogo from "../assets/view/Qatar_Airways_Logo.svg";
import EyLogo from "../assets/view/EY.png";
import BaLogo from "../assets/view/Ba.png";
import EkLogo from "../assets/view/Ek.png";
import UaLogo from "../assets/view/Ua.png";
import UlLogo from "../assets/view/UL.png";
import NoFlightLogo from "../assets/view/NoFlight.png";
import { useSelector } from "react-redux";
import axios from 'axios'
import useSwR from "swr"



const api = axios.create({
  baseURL: "https://backend.graycorp.io:9100/eflyer-bookings/api/v1"
})

const dep_date = "2025-03-01"
const des_date = "2025-03-31"
const dep_apt = "CMB"
const des_apt = "LHR"

const fetcher = (url, params) => api.get(url, { params })
  .then((res) => Object.create(res.data.contents.flight_Availability_Details))

const FlightAvailabilityChecking = () => {

  const { data, error, isLoading } = useSwR(["/getFlights", {
    "dep_date": dep_date,
    "des_date": des_date,
    "dep_apt": dep_apt,
    "des_apt": des_apt
  }], ([url, params]) => fetcher(url, params));

  const [airways, setAirways] = useState("BA");

  const [flightNumber, setFlightNumber] = useState(null);
  const [expandedFlight, setExpandedFlight] = useState(false);

  const toggleFlightDetails = (flightId) => {
    setFlightNumber((prev) => (prev === flightId ? null : flightId));

  };

  const airline_codes = [QatarAirwaysLogo, EyLogo, BaLogo, EkLogo, UaLogo, UlLogo, NoFlightLogo];

  const renderExpandedFlightDetails = (legs, airline_index) => {
    return (
      <div>
        {legs.map((leg) => (

          <div key={leg.legId} className="p-6 w-full bg-white text-sm border 
              border-border rounded-3xl font-inter">
            <div className="relative flex flex-col w-full max-w-3xl mx-auto">
              {/* Travel Time Label at Top */}
              <div className="flex items-center justify-start gap-2 mb-4 ml-20">
                <Clock size={14} className="text-smokyGray" />
                <span className="text-smokyGray text-base">Travel Time {leg.duration}</span>
              </div>

              {/* Main Flight Journey Container */}
              <div className="flex relative">
                <div className="w-24 text-end text-sm flex flex-col">
                  {/* Origin details */}
                  <div className="mb-40 mt-2">
                    <div className="text-smokyGray">Monday</div>
                    <div className="text-smokyGray">{leg.departure_date}</div>
                    <div className="text-smokyGray font-semibold">{leg.departure_time}</div>
                    <div className="text-smokyGray mt-1">{leg.fno}</div>
                  </div>
                  {/* Destination details */}
                  <div>
                    <div className="text-smokyGray">Monday</div>
                    <div className="text-smokyGray">{leg.arrival_date}</div>
                    <div className="text-smokyGray font-semibold">{leg.arrival_time}</div>
                  </div>
                </div>

                {/*Connection Points and Line */}
                <div className="relative w-12 mx-1">
                  {/* Airline Logo */}
                  <div className="absolute top-32 left-10 z-10">
                    <div className="lg:w-16 lg:h-16 w-14 h-14 lg:mt-4 mt-12 rounded-full border border-border flex items-center justify-center bg-white">
                      <img
                        src={airline_codes[airline_index]}
                        alt="Airline Logo"
                        className="w-11 h-11 object-contain"
                      />
                    </div>
                  </div>

                  {/* Connection Line, Origin Point (Filled), Destination Point (Outlined) */}
                  <div className="absolute top-2 left-1/2 w-0.5 h-64 bg-border transform -translate-x-1/2"></div>
                  <div className="absolute top-2 left-1/2 w-4 h-4 rounded-full bg-darkBlue transform -translate-x-1/2"></div>
                  <div className="absolute bottom-12 left-1/2 w-4 h-4 rounded-full border border-darkBlue bg-white transform -translate-x-1/2"></div>
                </div>

                {/* Airport Names and Flight Details */}
                <div className="flex-1 flex flex-col text-sm">
                  {/* Origin Airport */}
                  <div className="mb-2 mt-2">
                    <span className="text-smokyGray font-medium">{leg.departure_airport_code || 'Data Not Found'}</span>
                  </div>

                  {/* Flight Details Grid */}
                  <div className="lg:grid lg:grid-cols-3 gap-x-4 gap-y-2 lg:my-6 text-sm">
                    <div className="flex items-center font-light">
                      <Clock size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray ">Duration: {leg.duration}</span>
                    </div>
                    <div className="flex items-center font-light">
                      <Bookmark size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">Lowcost: {leg.cost}</span>
                    </div>
                    <div className="flex items-center font-light">
                      <Star size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">Class: {leg.flight_class || 'Data Not Found'}</span>
                    </div>
                    <div className="flex items-center font-light">
                      <ArrowLeftRight size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">Distance: {leg.distance_km} km</span>
                    </div>
                    <div className="flex items-center font-light">
                      <Moon size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">{leg.overnight ? 'Overnight' : 'Daytime'}</span>
                    </div>
                  </div>

                  {/* Destination Airport */}
                  <div className="mt-auto mb-12">
                    <span className="text-smokyGray font-medium">{leg.destination_airport_code || 'Data Not Found'}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };

  const outboundFlightDetails = (fare) => {

    {
      var airline_index = 6;
      if (fare.airline === "QR") airline_index = 0;
      if (fare.airline === "EY") airline_index = 1;
      if (fare.airline === "BA") airline_index = 2;
      if (fare.airline === "EK") airline_index = 3;
      if (fare.airline === "UA") airline_index = 4;
      if (fare.airline === "UL") airline_index = 5;
    }

    return (
      <div>
        <div className="flex flex-row justify-between items-center p-2 md:p-4 md:pr-10 bg-[#FAFAFA] rounded-2xl">
          <div className="flex items-center space-x-4">
            <img src={OutboundFlight} alt="Outbound Flight" />
            <div className="">Outbound</div>
          </div>
          <div className="w-[70px] h-[70px] border border-border 
              rounded-full p-2 flex items-center justify-center">
            <img
              src={airline_codes[airline_index]}
              alt="Airline Logo"
              className="w-full h-full object-contain"
            />
          </div>
        </div>
        <div className="flex flex-col space-y-10">
          {fare.flightDTOS.map((flightDTO) => (
            <div className="w-full" key={flightDTO.fareId}>
              <div className="flex flex-row justify-between items-center md:pl-4 space-x-4 mb-2">
                <label className="flex items-center justify-between cursor-pointer w-full">
                  <div className="flex flex-row justify-between items-center w-full">
                    <Field
                      type="radio"
                      name="outboundFlight"
                      value=""
                      className="w-5 h-5 accent-darkBlue"
                    />
                    <div className="flex flex-col items-start ml-4">
                      <span className="text-sm text-smokyGray">{flightDTO.first_departure_date}</span>
                      <span className="font-medium text-smokyGray">{flightDTO.first_departure_time}</span>
                      <span className="text-sm text-smokyGray"></span>
                    </div>
                    <div className="flex flex-col items-center mx-4">
                      <span className="text-xs text-smokyGray">{flightDTO.total_distance_km}</span>
                      <img src={Line} alt="Line" className="w-32" />
                      <span className="text-xs text-smokyGray">{flightDTO.stops}</span>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className="text-sm text-smokyGray">{flightDTO.final_destination_date}</span>
                      <span className="font-medium text-smokyGray">{flightDTO.final_destination_time}</span>
                    </div>
                  </div>
                </label>
                <div
                  onClick={() => toggleFlightDetails(flightDTO.flightId)}
                  className="cursor-pointer bg-gray-100 p-2 rounded-full"
                >
                  {flightNumber === flightDTO.flightId ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                </div>
              </div>
              {flightNumber === flightDTO.flightId ? renderExpandedFlightDetails(flightDTO.legDTOS, airline_index) : console.log(flightDTO.flightId)}
            </div>
          ))}
        </div>
        <ErrorMessage name="outboundFlight" component="div" className="text-red text-sm" />
      </div>

    );
  }

  const returnFlightDetails = (fare) => {

    {
      var airline_index = 6;
      if (fare.airline === "QR") airline_index = 0;
      if (fare.airline === "EY") airline_index = 1;
      if (fare.airline === "BA") airline_index = 2;
      if (fare.airline === "EK") airline_index = 3;
      if (fare.airline === "UA") airline_index = 4;
      if (fare.airline === "UL") airline_index = 5;
    }

    return (
      <div>

        <div className="flex flex-row justify-between items-center p-2 md:p-4 md:pr-10 bg-[#FAFAFA] rounded-2xl">
          <div className="flex items-center space-x-4">
            <img src={ReturnFlight} alt="Return Flight" />
            <div className="">Return Flight</div>
          </div>
          <div className="w-[70px] h-[70px] border border-border rounded-full p-2 flex items-center justify-center">
            <img src={airline_codes[airline_index]} alt="Airline Logo" className="w-full h-full object-contain" />
          </div>
        </div>
        <div className="flex flex-col space-y-10">
          {fare.flightDTOS.map((flightDTO) => (
            <div className="w-full" key={flightDTO.fareId}>
              <div className="flex flex-row justify-between items-center md:pl-4 space-x-4 mb-2">
                <label className="flex items-center justify-between cursor-pointer w-full">
                  <div className="flex flex-row justify-between items-center w-full">
                    <Field
                      type="radio"
                      name="returnFlight"
                      value=""
                      className="w-5 h-5 accent-darkBlue"
                    />
                    <div className="flex flex-col items-start ml-4">
                      <span className="text-sm text-smokyGray">{flightDTO.first_departure_date}</span>
                      <span className="font-medium text-smokyGray">{flightDTO.first_departure_time}</span>
                      <span className="text-sm text-smokyGray"></span>
                    </div>
                    <div className="flex flex-col items-center mx-4">
                      <span className="text-xs text-smokyGray">{flightDTO.total_distance_km}</span>
                      <img src={Line} alt="Line" className="w-32" />
                      <span className="text-xs text-smokyGray">{flightDTO.stops}</span>
                    </div>
                    <div className="flex flex-col items-end">
                      <span className="text-sm text-smokyGray">{flightDTO.final_destination_date}</span>
                      <span className="font-medium text-smokyGray">{flightDTO.final_destination_time}</span>
                    </div>
                  </div>
                </label>
                <div
                  onClick={() => toggleFlightDetails(flightDTO.flightId)}
                  className="cursor-pointer bg-gray-100 p-2 rounded-full"
                >
                  {flightNumber === flightDTO.flightId ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                </div>
              </div>
              {flightNumber === flightDTO.flightId ? renderExpandedFlightDetails(flightDTO.legDTOS) : console.log(flightDTO.flightId)}
            </div>
          ))}
        </div>
        <ErrorMessage name="returnFlight" component="div" className="text-red text-sm" />
      </div>

    );
  }

  if (isLoading) return <p>Loading...</p>;

  else if (error) return <p>Error: {error.message}</p>;

  else return (
    <div className="flex flex-col space-y-10 w-full">
      <div className="overflow-x-scroll md:overflow-visible scrollbar-hide">
        <div className="rounded-lg md:rounded-3xl border border-border text-smokyGray 
        p-2 md:p-4 lg:p-8 w-fit md:w-full">
          <Formik>
            <Form className="space-y-8">
              {data.map((tarif, index) => (

                <div key={tarif.tarifId}>
                  {/* Outbound Flight */}
                  {outboundFlightDetails(tarif.fareDTOList[0])}

                  {/* Return Flight */}
                  {returnFlightDetails(tarif.fareDTOList[1])}

                </div>
              ))}

              <div className="flex flex-col md:flex-row justify-between items-center">
                <div className="flex flex-col md:flex-row md:items-center md:space-x-4 mb-4 md:mb-0">
                  <span className="text-smokyGray text-xl md:text-3xl font-medium text-nowrap">
                    LKR 160000
                  </span>
                  <div className="flex flex-col">
                    <span className="text-sm">Price Per Person</span>
                    <div className="">
                      Total price 2 x Adult
                      <span className="font-medium ml-2">LKR 320000</span>
                    </div>
                  </div>
                </div>
                <button type="submit" disabled={false}
                  className="bg-darkBlue text-2xl hover:bg-blue-600 text-white font-medium 
                    py-4 px-6 rounded-2xl w-full md:w-auto">
                  Book
                </button>
              </div>

            </Form>
          </Formik>
        </div>
      </div>
    </div>

  );
}

export default FlightAvailabilityChecking