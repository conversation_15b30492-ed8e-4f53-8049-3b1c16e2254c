import { configureStore } from '@reduxjs/toolkit';
import { persistStore, persistReducer } from 'redux-persist';
import storage from 'redux-persist/lib/storage';
import hotelReducer from './hotelSlice';

const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['hotels'], 
};

const persistedReducer = persistReducer(persistConfig, hotelReducer);

export const store = configureStore({
  reducer: {
    hotels: persistedReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],
      },
    }),
});

export const persistor = persistStore(store);