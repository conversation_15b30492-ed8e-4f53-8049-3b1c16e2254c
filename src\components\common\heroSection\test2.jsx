import React from "react";

const headerText = [
  { text: "Explore", url: "/explore" },
  { text: "Offers", url: "/offers" },
  { text: "Destinations", url: "/destinations" },
  { text: "Flight", url: "/contact" },
];

const Test2 = () => {
  const handleNavigation = (url) => {
    window.location.href = url;
  };

  return (
    <div className="flex flex-row items-center space-x-12 p-4 bg-darkBlue shadow-md text-white">
      <div className="hidden lg:flex space-x-8 text-base font-inter">
        {headerText.map((item, index) => (
          <span
            key={index}
            className="cursor-pointer transition-colors" style={{fontWeight: "400",fontStyle: "normal"}}
            onClick={() => handleNavigation(item?.url)}
          >
            {item.text} hjhj
          </span>
        ))}
      </div>
    </div>
  );
};

export default Test2;
