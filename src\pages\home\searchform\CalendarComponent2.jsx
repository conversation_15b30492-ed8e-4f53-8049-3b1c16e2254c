import React from "react";
import { useFormik } from "formik";
import * as Yup from "yup";
import { Calendar } from "react-d-calendar";
import "react-d-calendar/dist/styles.css";

const today = new Date();
today.setHours(0, 0, 0, 0);

const formatDate = (d) => {
  if (!d || !(d instanceof Date) || isNaN(d.getTime())) return "";
  const year = d.getFullYear();
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const day = d.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const CalendarComponent2 = ({
  type,
  values,
  setFieldValue,
  flexibleDays,
  setFlexibleDays,
  setRangeSelection,
  handleCheckInConfirm,
  showCalendar,
}) => {
  return (
    <div className="absolute w-full md:w-[365px] z-20 bg-white rounded-[8px] flex flex-col space-y-0 mt-2">
      {type === "checkIn" ? (
        <Calendar
          value={values.checkInDate ? new Date(values.checkInDate) : undefined}
          onChange={(date) => {
            if (date && !isNaN(new Date(date).getTime())) {
              const formattedDate = formatDate(date);
              setFieldValue("checkInDate", formattedDate);
              setFieldValue("checkOutDate", "");
              setRangeSelection([new Date(formattedDate)]);
              handleCheckInConfirm();
            }
          }}
          disableBeforeToday={true}
          tileDisabled={({ date }) => date < today}
          locale="en-US"
          minDate={today}
          activeStartDate={
            values.checkInDate
              ? new Date(values.checkInDate)
              : new Date(today.getFullYear(), today.getMonth(), today.getDate())
          }
          showDoubleView={false}
          weekStartDay={1}
          weekdayFormat="minimal"
          maxDetail="month"
          minDetail="year"
        />
      ) : (
        values.checkInDate &&
        !isNaN(new Date(values.checkInDate).getTime()) && (
          <Calendar
            selectionMode="range"
            rangeStart={new Date(values.checkInDate)}
            value={
              values.checkOutDate && !isNaN(new Date(values.checkOutDate).getTime())
                ? [new Date(values.checkInDate), new Date(values.checkOutDate)]
                : [new Date(values.checkInDate)]
            }
            onChange={(value) => {
              if (Array.isArray(value) && value.length === 2 && value[0] && value[1]) {
                const [start, end] = value;
                if (end && !isNaN(end.getTime()) && end > start) {
                  setFieldValue("checkOutDate", formatDate(end));
                  setRangeSelection([start, end]);
                  showCalendar(false);
                }
              }
            }}
            onRangeHover={(range) => {
              if (range && range.end && !isNaN(range.end.getTime()) && range.end > range.start) {
                setRangeSelection([range.start, range.end]);
              } else {
                setRangeSelection([new Date(values.checkInDate), null]);
              }
            }}
            disableBeforeToday={true}
            tileDisabled={({ date }) => date < new Date(values.checkInDate) || date < today}
            locale="en-US"
            minDate={new Date(values.checkInDate)}
            activeStartDate={
              values.checkInDate
                ? new Date(values.checkInDate)
                : new Date(today.getFullYear(), today.getMonth(), today.getDate())
            }
            showDoubleView={false}
            weekStartDay={1}
            weekdayFormat="minimal"
            dateFormat="mm/dd/yyyy"
            rangeLimit={30}
            maxDetail="month"
            minDetail="year"
          />
        )
      )}
      <div className="flex flex-col px-2 pb-4 pt-2 rounded-[2px] space-y-3">
        <div className="flex space-x-3 w-full justify-center">
          {[1, 2, 3].map((days) => (
            <label
              key={days}
              onClick={() => {
                const newValue = flexibleDays === days ? 0 : days;
                setFlexibleDays(newValue);
                setFieldValue("flexibleDays", newValue);
              }}
              className={`cursor-pointer flex items-center space-x-1 px-4 py-1 rounded-[2px] 
                shadow-[0_0_0_0.5px_#024575] 
                ${flexibleDays === days ? "bg-darkBlue" : "bg-white text-smokyGray"}`}
            >
              <span className={`text-[10px] ${flexibleDays === days ? "text-white" : "text-smokyGray"}`}>
                {days === 0 ? "0 day" : `± ${days} ${days === 1 ? "day" : "days"}`}
              </span>
            </label>
          ))}
        </div>
        <span className="text-sm font-semibold text-center text-darkBlue">Flexible Dates</span>
      </div>
    </div>
  );
};
export default CalendarComponent2;

