import React, { useState } from "react";
import { ChevronDown } from "lucide-react";

const ShortBy = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedOption, setSelectedOption] = useState("Most Popular");
  
  const options = [
    "Most Popular",
    "Price: Low to High",
    "Price: High to Low",
    "Top Rated"
  ];
  
  const handleOptionClick = (option) => {
    setSelectedOption(option);
    setIsOpen(false);
  };
  
  return (
    <div className="relative">
      <label htmlFor="sort" className="text-darkBlue text-sm font-medium mb-1 block">
        Sort By
      </label>
      <div 
        className="flex items-center justify-between w-64 border border-border hover:border-blue-500 rounded-[2px] px-4 py-2 bg-white cursor-pointer transition-all duration-200"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="text-sm">{selectedOption}</span>
        <ChevronDown 
          size={16} 
          className={` transition-transform duration-200 ${isOpen ? "rotate-180" : ""}`}
        />
      </div>
      
      {isOpen && (
        <div className="absolute z-10 mt-1 w-64 bg-white border border-gray-200 rounded-[2px] shadow-lg">
          {options.map((option) => (
            <div
              key={option}
              className={`px-4 py-2 hover:bg-blue-50 text-sm cursor-pointer transition-colors duration-150 ${
                selectedOption === option ? "bg-blue-100 text-darkBlue font-medium" : "text-black"
              }`}
              onClick={() => handleOptionClick(option)}
            >
              {option}
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default ShortBy;