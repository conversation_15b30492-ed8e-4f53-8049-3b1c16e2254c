@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Inter', sans-serif;
}

.font-thin {
  font-weight: 100; /* Thin */
}

.font-extralight {
  font-weight: 200; /* Extra Light */
}

.font-light {
  font-weight: 300; /* Light */
}

.font-regular {
  font-weight: 400; /* Regular */
}

.font-medium {
  font-weight: 500; /* Medium */
}

.font-semibold {
  font-weight: 600; /* Semi Bold */
}

.font-bold {
  font-weight: 700; /* Bold */
}

.font-extrabold {
  font-weight: 800; /* Extra Bold */
}

.font-black {
  font-weight: 900; /* Black */
}

@layer utilities {
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .custom-slider .rc-slider-track {
    background-color: #024575; /* Dark Blue Color */
  }

  .custom-slider .rc-slider-handle {
    border-color: #024575; /* Dark Blue Color */
    box-shadow: none;
  }

  .custom-slider .rc-slider-handle:active {
    border-color: #024575;
  }

  .custom-slider .rc-slider-rail {
    background-color: #e5e7eb; /* Light Gray for the rail */
  }

  .custom-slider .rc-slider-mark-text {
    white-space: nowrap;
  }

  /* Hide scrollbar for Chrome, Safari, and Edge */
.no-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.no-scrollbar {
  scrollbar-width: none;
}

/* Hide scrollbar for IE and Edge */
.no-scrollbar {
  -ms-overflow-style: none;
}
.fixed-offset {
  scroll-margin-top: 60px; /* Adjust this value to match your nav bar's height */
}
  
}

/* Hide scrollbar visually */
.chart-container::-webkit-scrollbar {
  display: none;
}

/* Ensure compatibility across browsers */
.chart-container {
  -ms-overflow-style: none; /* IE/Edge */
  scrollbar-width: none; /* Firefox */
}
