.calendar {
  max-width: 100%;
  border-radius: 8px;
  background: none;
  font-family: 'Arial', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  overflow: hidden;
  padding: 0;
  margin: auto;
  box-shadow: none;
}

/* Focus state */
.calendar:focus {
  box-shadow: none;
  outline: none;
}

/* ========== Header ========== */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: none;
  padding: 10px 16px;
  border-bottom: none;
  flex-wrap: nowrap;
  gap: 12px;
}

.header-month-section {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 200px;
}

.header-month-label {
  font-size: 16px;
  font-weight: 400;
  color: #2b2a2a;
  text-transform: capitalize;
  letter-spacing: none;
  flex: 0 1 auto;
  text-align: left;
  width: 130px;
  text-align: center;
}

.header-month-nav {
  display: flex;
  align-items: center;
  gap: 0;
  flex-shrink: 0;
}

.nav-button {
  width: 32px;
  height: 32px;
  font-size: 16px;
  color: #2b2a2a;
  background: none;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease-in-out;
  border: none;
  flex-shrink: 0;
}

.nav-button:hover {
  background: #E6EDF2;
  color: #024575;
  transform: scale(1.1);
}

.nav-button:disabled {
  background: #e6edf2;
  color: #6b6868;
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* ========== Year Dropdown ========== */
.header-year-section {
  position: relative;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.year-select-wrapper {
  padding: 6px 24px 6px 10px;
  font-size: 16px;
  font-family: 'Arial', sans-serif;
  border: none;
  border-radius: 8px;
  background: none;
  color: #2b2a2a;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
  display: flex;
  align-items: center;
  position: relative;
  min-width: 80px;
}

.year-select-wrapper:hover,
.year-select-wrapper:focus,
.year-select-wrapper[aria-expanded='true'] {
  border-color: none;
  box-shadow: none;
}

.year-select-display {
  font-weight: 400;
}

.chevron-down {
  position: absolute;
  right: 8px;
  font-size: 14px;
  color: #2b2a2a;
  pointer-events: none;
  transition: transform 0.3s ease-in-out;
}

.year-select-wrapper[aria-expanded='true'] .chevron-down {
  transform: rotate(180deg);
}

.year-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 150px;
  max-height: 200px;
  background: transparent;
  border: 1px solid #e6edf2;
  border-radius: 8px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideIn 0.3s ease-in-out;
  overflow: hidden;
}

@keyframes slideIn {
  from {
    transform: translateY(-20px);
    opacity: 0;
  }

  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.year-search {
  width: 100%;
  padding: 10px 12px;
  border: none;
  border-bottom: 2px solid #e6edf2;
  font-size: 14px;
  color: #2b2a2a;
  background: transparent;
  outline: none;
  transition: all 0.3s ease-in-out;
}

.year-search:focus {
  border-bottom-color: #aeaeae;
}

.year-list {
  max-height: 150px;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #4b6cb7 #e6edf2;
  padding: 0.5rem 0;
}

.year-list::-webkit-scrollbar {
  width: 6px;
}

.year-list::-webkit-scrollbar-track {
  background: #e6edf2;
  border-radius: 3px;
}

.year-list::-webkit-scrollbar-thumb {
  background: #024575;
  border-radius: 3px;
}

.year-option {
  display: block;
  width: 100%;
  padding: 5px 16px;
  border: none;
  background: transparent;
  font-size: 14px;
  color: #2b2a2a;
  text-align: left;
  cursor: pointer;
  transition: all 0.3s ease-in-out;
}

.year-option:hover,
.year-option.focused {
  background: none;
  color: #030b31;
  transform: none;
}

.year-option.current-year {
  background: #024575;
  color: #ffffff;
  font-weight: 400;
  border-radius: 6px;
}

.year-option:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(2, 69, 117, 0.2);
}

.year-option:disabled {
  background: none;
  color: #6b6868;
  cursor: not-allowed;
  opacity: 0.6;
}

.year-option:disabled:hover,
.year-option:disabled.focused {
  background: none;
  color: #6b6868;
  transform: none;
}

/* ========== Weekday Row ========== */
.calendar-weekdays {
  display: flex;
  padding: 0 18px;
  border-bottom: none;
  background: #ffffff;
  width: 100%;
  justify-content: space-between;
  align-items: center;
}

.weekday {
  text-align: center;
  font-weight: 400;
  color: #6b6868;
  padding: 8px;
  font-size: 12px;
  text-transform: uppercase;
  transition: all 0.3s ease-in-out;
}

.weekday.saturday,
.weekday.sunday {
  color: #dc3545;
}

.weekday:hover {
  color: #2b2a2a;
  background: #e6edf2;
  border-radius: 6px;
}

/* ========== Calendar Grid ========== */
.calendar-days {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  padding: 0px 10px;
  background: none;
  gap: 6px 0;
}

/* ========== Base Day Style ========== */
.calendar-day {
  width: 48px;
  height: 30px;
  min-height: 30px;
  border: none;
  cursor: pointer;
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  color: #2b2a2a;
  transition: all 0.3s ease-in-out;
  position: relative;
  margin: 0;
  padding: 8px;
}

.calendar-day:hover {
  background: #e6edf2;
  color: #024575;
  transform: scale(1.05);
}

/* ========== Today Highlight ========== */
.calendar-day:disabled.today,
.calendar-day.today {
  background: none !important;
  color: #000000;
  border-radius: 8px;
  box-shadow: none;
  font-weight: 400;
  opacity: 1 !important;
}

.calendar-day.today::after {
  content: "";
  position: absolute;
  bottom: 6px;
  /* distance from text */
  left: 30%;
  /* start 25% into the tile */
  width: 40%;
  /* 50% width underline */
  height: 1px;
  /* thickness of underline */
  background-color: #024575;
  /* underline color */
  border-radius: 2px;
}

.calendar-day.today:hover {
  background: #2085ad;
  transform: scale(1.1);
}

/* ========== Start/End Dates ========== */
.calendar-day.selected,
.calendar-day.selected-start,
.calendar-day.selected-end {
  background: #024575 !important;
  color: #fff !important;
  border-radius: 5px;
  font-weight: 400;
  z-index: 3;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 1px solid #4b6cb7;
}

.calendar-day.selected-start:hover,
.calendar-day.selected-end:hover {
  background: #01324f;
  transform: scale(1.1);
}

.calendar-day.selected-end.in-range,
.calendar-day.selected-start.in-range {
  background: #024575;
  border-radius: 5px;
  z-index: 3;
}

/* ========== Start/End Circle Marker ========== */
.calendar-day.selected-start::after,
.calendar-day.selected-end::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: none;
  border-radius: 50%;
  border: none;
  top: 50%;
  transform: translateY(-50%);
}

.calendar-day.selected-start::after {
  left: 4px;
}

.calendar-day.selected-end::after {
  right: 4px;
}

.calendar-day.in-range {
  background: rgba(79, 181, 249, 0.2);
  color: #2b2a2a;
  border-radius: 0;
  transition: all 0.3s ease-in-out;
  border-left: 1px solid #e2e8f0;
  border-right: 1px solid #e2e8f0;
  position: relative;
  z-index: 1;
}

.calendar-day.in-range+.calendar-day.in-range {
  border-left: none;
}

.calendar-day.in-range:first-child {
  border-left: 1px solid #e2e8f0;
}

.calendar-day.in-range:last-child {
  border-right: 1px solid #e2e8f0;
}

.calendar-day.in-range:hover {
  background: rgba(2, 69, 117, 0.3);
}

/* ========== Hovered Future Range Preview ========== */
.calendar-day.range-preview {
  background: rgba(3, 113, 192, 0.3);
  color: #2b2a2a;
  border-radius: 0;
  z-index: 1;
  transition: all 0.3s ease-in-out;
}

.calendar-day.range-preview:hover {
  background: rgba(2, 69, 117, 0.4);
}

/* ========== Hover Range End ========== */
.calendar-day.hover-range-end {
  background: rgba(2, 69, 117, 0.6);
  color: #ffffff;
  border-radius: 8px;
  z-index: 2;
  transform: scale(1.05);
}

/* ========== Hover Range General ========== */
.calendar-day.hover-range {
  background: rgba(12, 132, 218, 0.4);
  color: #ffffff;
  border-radius: 8px;
  z-index: 2;
  transform: scale(1.05);
}

/* ========== Disabled Dates ========== */
.calendar-day:disabled {
  background: #ffffff;
  color: #6b6868;
  cursor: not-allowed;
  opacity: 0.6 !important;
}

.calendar-day:disabled.selected-start,
.calendar-day:disabled.selected-end {
  opacity: 1 !important;
  color: #ffffff;
}

.calendar-day:disabled:hover {
  background: #ffffff;
  color: #6b6868;
  transform: none;
}

/* ========== Adjacent Month Dates ========== */
.calendar-day.adjacent-month {
  color: #6b6868;
  font-weight: 400;
}

.calendar-day.adjacent-month:hover {
  color: #2b2a2a;
}

/* ========== Animation ========== */
.calendar-container {
  display: flex;
  gap: 12px;
  padding: 0;
  margin: 4px;
  justify-content: space-between;
  animation: fadeIn 0.4s ease-in-out;
  background: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========== Responsive ========== */
@media (max-width: 475px) {
  .calendar {
    padding: 0 8px;
  }

  .calendar-day {
    width: 28px;
    height: 28px;
    font-size: 10px;
    padding: 4px;
  }

  .header-month-label,
  .year-select-wrapper,
  .weekday {
    font-size: 11px;
  }
}

@media (max-width: 640px) {
  .calendar-day {
    width: 30px;
    height: 30px;
    font-size: 11px;
  }

  .header-month-label,
  .year-select-wrapper,
  .weekday {
    font-size: 12px;
  }

  .header {
    padding: 6px 10px;
  }
}

@media (max-width: 768px) {
  .calendar-day {
    width: 34px;
    height: 34px;
    font-size: 12px;
  }

  .header-month-label,
  .year-select-wrapper {
    font-size: 14px;
  }
}

@media (max-width: 1024px) {
  .calendar-day {
    width: 42px;
    height: 36px;
    font-size: 13px;
  }
}

@media (max-width: 1280px) {
  .calendar-day {
    width: 46px;
    height: 38px;
    font-size: 14px;
  }
}