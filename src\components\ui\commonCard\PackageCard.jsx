import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { BedDouble, Heart, Star, ThumbsUp } from 'lucide-react';
import { Toolt<PERSON> } from 'react-tooltip';
import Icon1 from '../../../assets/packages/icon_1.svg';
import Icon2 from '../../../assets/packages/icon_2.svg';
import Icon3 from '../../../assets/packages/icon_3.svg';
import Icon4 from '../../../assets/packages/icon_4.svg';
import Icon5 from '../../../assets/packages/icon_5.svg';
import Icon6 from '../../../assets/packages/icon_6.svg';
import Icon7 from '../../../assets/packages/icon_7.svg';
import Icon8 from '../../../assets/packages/icon_8.svg';
import Icon9 from '../../../assets/packages/icon_9.svg';
import Icon10 from '../../../assets/packages/icon_10.svg';

const iconGroup1 = [Icon1, Icon2, Icon3, Icon4, Icon5];
const iconGroup2 = [Icon6, Icon7, Icon8, Icon9, Icon10];

const PackageCard = ({ adventure, small }) => {
  console.log(small);

  return (
    <div
      key={adventure?.id}
      className="relative w-full max-w-4xl overflow-hidden rounded-xl bg-white shadow-lg group"
    >
      <div className="relative h-[250px]">
        {/* Background Image */}
        <div className="w-full ">
      <img
          src={adventure?.image}
          alt={adventure?.title}
          className="h-full w-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
        />
            </div>

        {/* Overlay Content */}
        <div className="absolute inset-0  group-hover:bg-black group-hover:bg-opacity-20">
          {/* Top Bar */}
          <div className="flex items-center justify-between px-1 py-2 lg:px-4 lg:py-4">
            <div className="flex gap-2 items-center">
              <div className="rounded-full bg-black/30 px-3 py-1 text-white text-sm font-medium flex flex-row gap-x-4">
                <BedDouble />
                Hotel
              </div>
              {adventure?.isTop && (
                <span className="rounded-full bg-orange px-3 py-1 text-sm font-medium text-white flex flex-row gap-x-2">
                  <ThumbsUp />
                  Top
                </span>
              )}
              {adventure?.discount && (
                <span className="rounded-full bg-red px-3 py-1 text-sm font-medium text-white flex flex-row gap-x-2">
                  {adventure.discount}% Off
                </span>
              )}
              <Heart
                className="h-6 w-6 text-white cursor-pointer"
                data-tooltip-id="watchlist-tooltip"
                data-tooltip-content="Add to Watch List"
              />
            </div>

            <div className="flex">
              {[...Array(adventure?.rating || 3)].map((_, index) => (
                <Star key={index} className="h-5 w-5 fill-white text-white" />
              ))}
            </div>
          </div>

          {/* Bottom Content */}
          <div className="relative -bottom-11 w-full px-6 pb-4 text-white bg-black bg-opacity-10 transform translate-y-20 opacity-80 group-hover:translate-y-0 group-hover:opacity-100 transition-all duration-700 ease-in-out">
            <h2 className="text-xl font-bold border-b-2 border-white border-opacity-20">
              {adventure?.title || 'Unknown Hotel'}
            </h2>
            <p className="text-xl">{adventure?.location || 'Unknown Location'}</p>
            <div className="flex flex-wrap items-center justify-between">
              {/* Left Section */}
              <div className="space-y-0 w-1/2">
                <p className="text-xs text-nowrap">
                  {adventure?.dates || 'N/A'} <br /> {adventure?.duration || 'N/A'}
                </p>
                <p className="text-xs">{adventure?.package || 'Standard Package'}</p>
                <p className="text-xl font-semibold text-nowrap mt-2">
                  {adventure?.discountedPrice || 'Price Unavailable'}
                </p>
              </div>

              {/* Right Section */}
              <div className="w-1/2 text-right">
                <div className="block lg:hidden">
                  <p className="text-sm">Per person from</p>
                </div>
                {adventure?.originalPrice && (
                  <div>
                    <span className="text-lg text-right line-through">
                      {adventure.originalPrice}
                    </span>
                  </div>
                )}
                <div className="flex justify-end items-center space-x-4">
                  <p className="text-sm hidden lg:block">Per person from</p>
                  <p className="text-xl font-semibold text-nowrap mt-2">
                    {adventure?.discountedPrice || 'Price Unavailable'}
                  </p>
                </div>
              </div>
            </div>

            {/* Action Buttons - Shown on Hover */}
            <div className="my-2 space-y-2 hidden duration-500 group-hover:block">
              <div className="w-full flex justify-center">
                <button className="w-3/4 rounded-3xl bg-[#024577] py-3 text-center font-semibold text-white transition-colors bg-opacity-40 hover:scale-105">
                  Book Now
                </button>
              </div>
              <Link to={`/hotel-and-flight/${adventure?.id}`}>
                <button className="w-full text-center font-semibold text-white underline hover:scale-105">
                  Discover
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Icons Section */}
      <div className="p-4">
        <div className="flex justify-center gap-4 mb-4">
          {adventure?.icon?.slice(0, 5).map((icon, index) => (
            <img key={index} src={icon} alt={`Icon ${index + 1}`} className="w-10 h-10" />
          ))}
        </div>
        {adventure?.icon?.length > 5 && (
          <div className="flex justify-center gap-4">
            {adventure.icon.slice(5).map((icon, index) => (
              <img key={index} src={icon} alt={`Icon ${index + 6}`} className="w-10 h-10" />
            ))}
          </div>
        )}
      </div>

      {/* Tooltip */}
      <Tooltip
        id="watchlist-tooltip"
        place="top"
        variant="light"
        className="bg-black text-white rounded p-1 text-9xl"
      />
    </div>
  );
};

export default PackageCard;