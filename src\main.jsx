import React from "react";
import { createRoot } from "react-dom/client";
import { Provider } from "react-redux";
import { PersistGate } from "redux-persist/integration/react";
import { store, persistor } from "./store/Store.js";
import "./index.css";
import App from "./App.jsx";
import "react-date-range/dist/styles.css"; 
import "react-date-range/dist/theme/default.css"; 
import "rc-slider/assets/index.css"; 
import "leaflet/dist/leaflet.css";

createRoot(document.getElementById("root")).render(
  <Provider store={store}>
    <PersistGate loading={null} persistor={persistor}>
      <App />
    </PersistGate>
  </Provider>
);
