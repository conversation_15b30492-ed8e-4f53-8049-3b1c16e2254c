import React from 'react';
import { ChevronRight } from 'lucide-react';
import { FaWhatsapp } from 'react-icons/fa6';
import { MdOutlineMailOutline, MdOutlinePhone } from 'react-icons/md';
import { FormControlLabel, Checkbox } from '@mui/material';
import { Table, TableBody, TableCell, TableHead, TableRow, Paper } from '@mui/material';

const ConfirmationCard = ({
  title = 'Thank You for booking!',
  reservationData = {},
  invoiceAddress = [],
  priceDetails = [],
  agencyInfo = {},
  bookingCompletedAt = 'N/A',
  specificDetails = null,
  voucherDetails = {},
  onChangeAgency = () => { },
  showChangeCheckbox = false,
}) => {
  return (
    <div className="w-full space-x-10 flex justify-center">
      <div className="flex flex-col w-full md:w-2/3 max-w-[750px] p-6 space-y-4 md:ml-6">
        <h1 className="text-lg font-medium text-black md:ml-6">{title}</h1>
        <p className="text-black font-light text-sm md:ml-6">
          We are pleased to inform you that your booking was successfully completed at {bookingCompletedAt}
        </p>
        <div className="flex flex-col w-full space-y-4">
          {/* Reservation Code Section */}
          <div className="border border-darkBlue rounded-2xl p-6 space-y-2">
            <div className="flex flex-row items-center gap-8 md:ml-6">
              <label className="w-60 text-base font-normal text-smokyGray">Reservation Code</label>
              <div className="box-border flex flex-row items-center px-4 scrollbar-hide  w-64 h-10 border-[0.5px] border-border rounded-xl overflow-x-auto">
                <input
                  type="text"
                  value={reservationData.code || 'N/A'}
                  readOnly
                  className="text-sm font-light text-smokyGray whitespace-nowrap"
                />
              </div>
            </div>
            <div>
              <label className="text-smokyGray md:text-sm text-xs font-light md:ml-6">
                Ticket Must be issued by {reservationData.issueDeadline || 'N/A'}
              </label>
            </div>
          </div>

          {/* Invoice Address Section */}
          {/* <div className="border border-darkBlue rounded-2xl p-6 ">
            <h2 className="text-xl font-normal text-smokyGray mb-6 md:ml-6">Invoice Address</h2>
            <div className="space-y-6">
              {invoiceAddress.map((item, index) => (
                <div key={index} className="flex flex-row items-center gap-8 md:ml-6">
                  <label className="w-60 text-base font-normal text-smokyGray">{item.label}</label>
                  <div className="box-border flex flex-row items-center px-4 scrollbar-hide  w-64 h-10 border-[0.5px] border-border rounded-xl overflow-x-auto">
                    <span className="text-sm font-light text-smokyGray whitespace-nowrap">{item.value}</span>
                  </div>
                </div>
              ))}
            </div>
          </div> */}

          {/* Voucher Details Section */}
          {/* <div className="border border-darkBlue rounded-2xl p-6">
            <h2 className="text-xl font-normal text-smokyGray m-6">Voucher Info</h2>
            <div className="space-y-6">
              {voucherDetails.voucherInfo?.map((item, index) => (
                <div key={index} className="flex flex-row items-center gap-8 md:ml-6">
                  <label className="w-60 text-base font-normal text-smokyGray">{item.label}</label>
                  <div className="box-border flex flex-row items-center px-4 py-2 w-64 border-[0.5px] border-border rounded-xl">
                    <span className="text-sm font-light text-smokyGray whitespace-nowrap">{item.value}</span>
                  </div>
                </div>
              ))}
            </div>
          </div> */}
          {/* Service Provider Info */}
          <div className="border border-darkBlue rounded-2xl p-6">
            <h2 className="text-xl font-normal text-smokyGray m-6">Service Provider Details</h2>
            <div className="space-y-6">
              {voucherDetails.serviceProviderInfo?.map((item, index) => (
                <div key={index} className="flex flex-row items-center gap-8 md:ml-6">
                  <label className="w-60 text-base font-normal text-smokyGray">{item.label}</label>
                  <div className="box-border flex flex-row items-center px-4 py-2 w-64 border-[0.5px] border-border rounded-xl">
                    <span className="text-sm font-light text-smokyGray whitespace-pre-wrap">{item.value}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Passenger Info */}
          <div className="border border-darkBlue rounded-2xl p-6">
            <h2 className="text-xl font-normal text-smokyGray m-6">Passenger Details</h2>
            <div className="space-y-6">
              {voucherDetails.passengerInfo?.map((item, index) => (
                <div key={index} className="flex flex-row items-center gap-8 md:ml-6">
                  <label className="w-60 text-base font-normal text-smokyGray">{item.label}</label>
                  <div className="box-border flex flex-row items-center px-4 py-2 w-64 border-[0.5px] border-border rounded-xl ">
                    <span className="text-sm font-light text-smokyGray whitespace-pre-wrap">{item.value}</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Daily Rates Section */}
          <div className="border border-darkBlue rounded-2xl p-6">
            <h2 className="text-xl font-normal text-smokyGray m-6">Daily Rates</h2>
            <div className="space-y-4 md:ml-6">
              <Paper elevation={0} className="">
                <Table>
                  <TableHead>
                    <TableRow sx={{ backgroundColor: '#024575' }}>
                      <TableCell sx={{ color: '#fff', fontWeight: 'medium', fontSize: '0.875rem' }}>Date</TableCell>
                      <TableCell sx={{ color: '#fff', fontWeight: 'medium', fontSize: '0.875rem' }}>Payable Rate</TableCell>
                      <TableCell sx={{ color: '#fff', fontWeight: 'medium', fontSize: '0.875rem' }}>Rate Basis</TableCell>
                      <TableCell sx={{ color: '#fff', fontWeight: 'medium', fontSize: '0.875rem' }}>Rate Market</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {voucherDetails.dailyRates?.map((rate, index) => (
                      <React.Fragment key={index}>
                        <TableRow>
                          <TableCell sx={{ fontSize: '0.875rem', color: '#4B4B4B' }}>{rate.date}</TableCell>
                          <TableCell sx={{ fontSize: '0.875rem', color: '#4B4B4B' }}>{rate.payableRate}</TableCell>
                          <TableCell sx={{ fontSize: '0.875rem', color: '#4B4B4B' }}>{rate.rateBasis}</TableCell>
                          <TableCell sx={{ fontSize: '0.875rem', color: '#4B4B4B' }}>{rate.rateMarket}</TableCell>
                        </TableRow>
                        {index < voucherDetails.dailyRates.length - 1 && (
                          <TableRow>
                            <TableCell colSpan={4} sx={{ padding: '4px' }}>
                              <hr className="border-t border-dotted border-border" />
                            </TableCell>
                          </TableRow>
                        )}
                      </React.Fragment>
                    ))}
                    <TableRow>
                      <TableCell colSpan={4} sx={{ fontSize: '0.875rem', color: '#4B4B4B', padding: '10px' }}>
                        Rates including all Room taxes and Service charges and may not include City Taxes.
                        <br />
                        Please note that the rates shown above could be average daily rates and they might not reflect the actual daily rates for the room.
                      </TableCell>
                    </TableRow>
                  </TableBody>
                </Table>
              </Paper>
              <p className="text-sm font-medium text-smokyGray ">
                Total Payable for this Booking: {voucherDetails.totalPayable || 'N/A'}
              </p>
              <hr className="border-t border-dotted border-border " />
              {/* Cancellation Policy */}
              <div>
                <h3 className="text-sm font-medium text-smokyGray">CANCELLATION POLICY</h3>
                <ul className="text-sm text-smokyGray list-disc ml-14">
                  {voucherDetails.cancellationRules?.map((rule, index) => (
                    <li key={index}>
                      {rule.text}
                      {rule.subText && <span className="block pl-4">{rule.subText}</span>}
                    </li>
                  ))}
                </ul>
              </div>
              <hr className="border-t border-dotted border-border" />
              {/* Tariff Notes */}
              <div>
                <h3 className="text-sm font-medium text-smokyGray">TARIFF NOTES</h3>
                <p className="text-sm text-smokyGray ">{voucherDetails.tariffNotes || 'N/A'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Specific Details */}
        {specificDetails && <div>{specificDetails}</div>}

        {/* Price Details Section */}
        <div className="border border-darkBlue rounded-2xl p-6 ">
          <h2 className="text-[20px] font-normal text-smokyGray m-6">Price Details</h2>
          <div className="space-y-6">
            {priceDetails.map((item, index) => (
              <div key={index} className="flex flex-row items-center gap-8 md:ml-6">
                <label className="w-60 text-base font-normal text-smokyGray">{item.label}</label>
                <div className="box-border flex flex-row items-center px-4 py-2 gap-3 w-64 border-[0.5px] border-border rounded-xl">
                  <span className="text-sm font-light text-smokyGray">{item.value}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Agency Info Section */}
      <div className="w-full md:w-1/3 mx-auto hidden lg:block lg:order-2 space-y-4 sticky top-12 max-h-[calc(100vh-10px)] overflow-y-auto no-scrollbar mt-28">
        <div className="w-full border border-darkBlue rounded-2xl h-auto">
          <div className="w-full h-24 bg-offWhite rounded-t-2xl p-0">
            <h3 className="text-sm font-medium text-darkBlue md:ml-6 pt-4">Helpdesk</h3>
            <p className="text-xs text-darkBlue font-light mt-2 md:ml-6">
              If you have any question regarding booking process and your reservation, please feel free to call us.
            </p>
          </div>
          <div className="px-6 py-4">
            <h2 className="text-base font-normal mb-4 mt-4 md:ml-6">Your Responsible Travel Agency</h2>
            <div className="text-sm font-light text-smokyGray md:ml-6">
              <p>{agencyInfo.name || 'N/A'}</p>
              <p>{agencyInfo.address || 'N/A'}</p>
              <p className="mb-2">{agencyInfo.city || 'N/A'}</p>
              <div className="mb-4 mt-4">
                <p className="flex items-center">
                  <MdOutlinePhone className="mr-2" />
                  <a href={`tel:${agencyInfo.phone}`} className="text-sm font-medium text-darkBlue">
                    {agencyInfo.phone || 'N/A'}
                  </a>
                </p>
                <p className="flex items-center">
                  <FaWhatsapp className="mr-2" />
                  <a href={`tel:${agencyInfo.phone}`} className="text-sm font-medium text-darkBlue">
                    {agencyInfo.phone || 'N/A'}
                  </a>
                </p>
                <p className="flex items-center">
                  <MdOutlineMailOutline className="mr-2" />
                  <a href={`mailto:${agencyInfo.email}`} className="text-sm font-medium text-darkBlue">
                    {agencyInfo.email || 'N/A'}
                  </a>
                </p>
              </div>
              <p className="font-normal text-black">{agencyInfo.description || 'N/A'}</p>
              <p className="mb-4">{agencyInfo.subDescription || 'N/A'}</p>
              <p className="font-normal text-sm text-black mb-2">Opening Hours</p>
              <p className="hover:text-green">{agencyInfo.openingHours?.current || 'N/A'}</p>
              <p className="hover:text-green">{agencyInfo.openingHours?.closing || 'N/A'}</p>
              <p className="hover:text-green">{agencyInfo.openingHours?.nextDay || 'N/A'}</p>
            </div>
            <div className="flex items-center justify-end mt-4 mb-4 mr-6">
              <button className="flex items-center text-darkBlue text-base font-extralight" onClick={onChangeAgency}>
                <span>Change</span>
                <ChevronRight className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationCard;