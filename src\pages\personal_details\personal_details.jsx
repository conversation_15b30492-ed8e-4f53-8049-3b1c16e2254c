import React, { useState } from "react";
import { Formik, Form } from "formik";
import { ChevronDown, Calendar, Clipboard, Loader2 } from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import ContactDetails from "../booking/ContactDetails";
import TravelInsuranceForm from "../booking/TravelInsurance";
import InvoiceDetailsComponent from "../booking/invoice_details";
import ProgressTracker from "./progresstracker";
import axios from "axios";
import FlightBookingCard from './FlightBookingCard';
import SignInPrompt from "../booking/SignInPrompt";
import FlightPayment from "../booking/FlightPayment";

const api = axios.create({
  baseURL: "https://backend.graycorp.io:9100/eflyer-bookings/api/v1",
});

const Personaldetails = ({ onNext }) => {
  const [termsChecked, setTermsChecked] = useState(false);
  const [documentsChecked, setDocumentsChecked] = useState(false);
  const [newsletterChecked, setNewsletterChecked] = useState(false);
  const location = useLocation();
  const bookingDetails = location.state?.bookingDetails || {};
  const Navigate = useNavigate();
  const [contactDetails, setContactDetails] = useState({});
  const [InvoiceDetails, setInvoiceDetails] = useState({});
  const [loading, setLoading] = useState(false); // Loading 
  const [errors, setErrors] = useState({
    travelers: [],
    contactDetails: [],
    invoiceDetails: [],
    payment: [],
    checklist: [],
  });

  const countryCodeMap = {
    'USA': 'US',
    'SriLanka': 'LK',
    'UK': 'GB',
  };

  const formatDate = (dateStr) => {
    if (!dateStr) return "";
    const [day, month, year] = dateStr.split('/');
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  };

  if (!bookingDetails) {
    return <div>Error: Booking details not provided.</div>;
  }

  const generateTravelers = () => {
    const { adults = 0, children = 0, babies = 0 } = bookingDetails;
    const travelers = [];
    let id = 1;
    for (let i = 0; i < adults; i++) {
      travelers.push({ id: id++, type: "Adult", gender: "", firstName: "", lastName: "", dateOfBirth: "" });
    }
    for (let i = 0; i < children; i++) {
      travelers.push({ id: id++, type: "Child", gender: "", firstName: "", lastName: "", dateOfBirth: "" });
    }
    for (let i = 0; i < babies; i++) {
      travelers.push({ id: id++, type: "Baby", gender: "", firstName: "", lastName: "", dateOfBirth: "" });
    }
    return travelers;
  };

  const [genderDropdown, setGenderDropdown] = useState({ 1: false, 2: false });
  const [travelers, setTravelers] = useState(generateTravelers());

  const handleInputChange = (id, field, value) => {
    setTravelers(travelers.map((traveler) => (traveler.id === id ? { ...traveler, [field]: value } : traveler)));
  };

  const toggleDropdown = (id) => {
    setGenderDropdown({ ...genderDropdown, [id]: !genderDropdown[id] });
  };

  const renderTraveler = (traveler) => (
    <div key={traveler.id} className="mt-4">
      <div className="mb-4 pb-2">
        <h3 className="text-sm font-medium text-black">
          {/* {traveler.id}<sup>{["st", "nd", "rd"][traveler.id - 1] || "th"}</sup> Passenger: {traveler.type} */}
          Passenger: {traveler.id} ({traveler.type})
        </h3>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <div className="relative">
          <div className="border border-darkBlue rounded-2xl px-4 py-3 cursor-pointer" onClick={() => toggleDropdown(traveler.id)}>
            <p className="text-xs text-black font-light">Gender</p>
            <div className="flex justify-between items-center">
              <p className="text-smokyGray text-base">{traveler.gender || "Select here"}</p>
              <ChevronDown className="w-4 h-4 text-smokyGray" />
            </div>
          </div>
          {genderDropdown[traveler.id] && (
            <div className="absolute w-full bg-white border border-darkBlue rounded-lg mt-1 shadow-lg z-10">
              <div className="p-2 cursor-pointer" onClick={() => { handleInputChange(traveler.id, "gender", "Male"); toggleDropdown(traveler.id); }}>
                Male
              </div>
              <div className="p-2 cursor-pointer" onClick={() => { handleInputChange(traveler.id, "gender", "Female"); toggleDropdown(traveler.id); }}>
                Female
              </div>
            </div>
          )}
        </div>
        <div className="border border-darkBlue rounded-2xl px-4 py-3">
          <div className="flex items-center gap-4">
            <Calendar className="w-5 h-5 text-smokyGray" />
            <div className="flex-1">
              <p className="text-xs text-black font-light">Date of Birth</p>
              <input
                type="text"
                placeholder="YYYY-MM-DD"
                value={traveler.dateOfBirth}
                onChange={(e) => handleInputChange(traveler.id, "dateOfBirth", e.target.value)}
                className="w-full text-smokyGray text-base focus:outline-none"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-4">
        <div className="border border-darkBlue rounded-2xl px-4 py-3">
          <p className="text-xs text-black font-light">First Name</p>
          <input
            type="text"
            value={traveler.firstName}
            onChange={(e) => handleInputChange(traveler.id, "firstName", e.target.value)}
            className="w-full text-smokyGray text-base focus:outline-none"
            placeholder="First name"
          />
        </div>
        <div className="border border-darkBlue rounded-2xl px-4 py-3">
          <p className="text-xs text-black font-light">Last Name</p>
          <input
            type="text"
            value={traveler.lastName}
            onChange={(e) => handleInputChange(traveler.id, "lastName", e.target.value)}
            className="w-full text-smokyGray text-base focus:outline-none"
            placeholder="Last name"
          />
        </div>
      </div>
    </div>
  );


  const BookingDetailsSummary = ({ bookingDetails }) => {
    const parseXmlResponse = (xmlString) => {
      const parser = new DOMParser();
      const xmlDoc = parser.parseFromString(xmlString, "text/xml");
      const errorResponse = xmlDoc.getElementsByTagName("errorResponse")[0];
      if (errorResponse) {
        const errorNo = errorResponse.getAttribute("no");
        const errorMessage = errorResponse.textContent;
        return { errorNo, errorMessage };
      }
      return null;
    };

    const apiResponse = bookingDetails.apiResponse || {};
    const xmlError = apiResponse.response ? parseXmlResponse(apiResponse.response) : null;

    return (
      <div className="w-full p-4 mb-6 bg-slate-50 rounded-xl border border-darkBlue">
        <h2 className="text-lg font-medium text-darkBlue mb-3">Booking Details Summary</h2>
        <p className="text-sm font-medium">tarifId: {bookingDetails.tarifId || "N/A"}</p>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p className="text-sm font-medium">Outbound Flight: {bookingDetails.outboundFlightId || "N/A"}</p>
            <p className="text-sm text-smokyGray">
              {bookingDetails.departureDate} {bookingDetails.departureTime} → {bookingDetails.arrivalDate} {bookingDetails.arrivalTime}
            </p>
            <p className="text-xs text-smokyGray">Airline: {bookingDetails.outboundAirlineCode || "N/A"}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Return Flight: {bookingDetails.returnFlightId || "N/A"}</p>
            <p className="text-sm text-smokyGray">
              {bookingDetails.returnDepartureDate} {bookingDetails.returnDepartureTime} → {bookingDetails.returnArrivalDate} {bookingDetails.returnArrivalTime}
            </p>
            <p className="text-xs text-smokyGray">Airline: {bookingDetails.returnAirlineCode || "N/A"}</p>
          </div>
          <div>
            <p className="text-sm font-medium">Passengers:</p>
            <p className="text-sm text-smokyGray">
              Adults: {bookingDetails.adults || 0}, Children: {bookingDetails.children || 0}, Babies: {bookingDetails.babies || 0}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Price Summary:</p>
            <p className="text-sm font-semibold text-darkBlue">Total Price: CHF {bookingDetails.totalPrice || "N/A"}</p>
          </div>
        </div>
        <p className="text-xs text-smokyGray mt-3">Session ID: {bookingDetails.sessionId || "N/A"}</p>
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">API Response</h3>
          {apiResponse.Session && (
            <p className="text-sm text-smokyGray">
              <span className="font-medium">Session ID:</span> {apiResponse.Session}
            </p>
          )}
          {xmlError ? (
            <p className="text-sm text-red-600">
              <span className="font-medium">Error {xmlError.errorNo}:</span> {xmlError.errorMessage}
            </p>
          ) : (
            <p className="text-sm text-green-600">
              <span className="font-medium">No errors detected</span>
            </p>
          )}
        </div>
      </div>
    );
  };

  const DetailItem = ({ label, value }) => (
    <div className="flex items-center justify-between">
      <span className="text-sm text-smokyGray font-medium w-full">{label}</span>
      <div className="bg-white border border-darkBlue rounded-lg w-full px-4 py-2 text-sm text-smokyGray">{value}</div>
    </div>
  );

  const bookFlight = async (payload) => {
    try {
      const headers = {
        "Content-Type": "application/json",
        session_id: bookingDetails.sessionId || "",
      };
      const response = await api.post("/bookFlight", payload, { headers });
      console.log("API Response:", response.data);
      return response.data;
    } catch (error) {
      console.error("Booking error:", error.response?.data || error.message);
      throw error;
    }
  };

  const sanitizePhoneNumber = (phone) => {
    if (!phone) return '0';
    return phone.replace(/\D/g, '');
  };

  return (
    <Formik
      initialValues={{}}
      onSubmit={async () => {
        setLoading(true); // Set loading to true
        const billingPassenger = {
          firstName: InvoiceDetails.firstName || "",
          lastName: InvoiceDetails.lastName || "",
          dob: formatDate(InvoiceDetails.dateOfBirth),
          gender: InvoiceDetails.gender === "Male" ? "M" : "F",
          phone: sanitizePhoneNumber(InvoiceDetails.phoneNumber),
          fax: sanitizePhoneNumber(InvoiceDetails.fax),
          businessPhone: sanitizePhoneNumber(InvoiceDetails.phoneNumber),
          cellPhone: sanitizePhoneNumber(contactDetails.emergencyPhoneNumber),
          email: InvoiceDetails.email || "",
          company: InvoiceDetails.company || "",
          houseNumber: InvoiceDetails.houseNumber || "",
          street: InvoiceDetails.streetName || "",
          zipCode: InvoiceDetails.postalCode || "",
          location: InvoiceDetails.city || "",
          country: InvoiceDetails.country || "",
          countryCode: countryCodeMap[InvoiceDetails.country] || "Unknown",
          creditCardDetails: {
            ccNo: cardNumber.replace(/\s/g, ""),
            ccCvcCode: cvv,
            ccOwner: `${InvoiceDetails.firstName || ""} ${InvoiceDetails.lastName || ""}`,
            ccVlDate: expirationDate,
            ccType: selectedPayment !== null ? FlightPayment[selectedPayment].alt : "Visa",
          },
        };

        const coPassengers = travelers.slice(1).map((traveler) => ({
          firstName: traveler.firstName || "",
          lastName: traveler.lastName || "",
          dob: formatDate(traveler.dateOfBirth),
          gender: traveler.gender === "Male" ? "M" : "F",
          countryCode: "Unknown", // Nationality is commented out, default to "Unknown"
        }));

        const bookingPayload = {
          billing_passenger: billingPassenger,
          co_passengers: coPassengers,
          deliveryType: "E-Ticket",
          flightIds: [bookingDetails.outboundFlightId, bookingDetails.returnFlightId].filter(Boolean),
          tarifId: bookingDetails.tarifId,
        };

        try {
          const bookingResponse = await bookFlight(bookingPayload);
          const values = {
            bookingDetails,
            travelers,
            contactDetails,
            InvoiceDetails,
            bookingResponse,
          };
          Navigate("/PaymentComponent", { state: { values } });
        } catch (error) {
          if (error.response) {
            alert(`Booking failed: ${error.response.data.message || "Unknown error"}`);
            console.error("Booking failed:", error.response.data);
          } else if (error.request) {
            alert("Network error. Please check your internet connection.");
          } else {
            alert("An unknown error occurred.");
          }
        }
      }}
    >
      <Form>
        <ProgressTracker currentStep={3} />
        <div className="w-full flex justify-center relative mt-8 px-4 md:px-8">
          <div className="flex flex-row w-full max-w-[80%] p-4 space-x-8 font-inter">
            <div className="lg:w-1/2 w-full relative space-y-3">
              <div className="w-full mx-auto rounded-3xl">
                <div className="space-y-6">
                  <SignInPrompt />
                  <div className="w-full mx-auto p-6 bg-white rounded-3xl border border-darkBlue">
                    <h2 className="text-2xl text-darkBlue font-semibold">Passengers</h2>
                    {travelers.map(renderTraveler)}
                    <hr className="border-border mt-10" />
                    <ContactDetails onChange={setContactDetails} />
                  </div>
                  <InvoiceDetailsComponent onChange={setInvoiceDetails} />
                  <TravelInsuranceForm />
                  <div className="w-full mx-auto p-6 bg-white rounded-3xl border border-darkBlue">
                    <h2 className="text-2xl text-darkBlue font-semibold  mb-6">Terms & Conditions</h2>
                    <div className="flex ml-6 items-start gap-3 mb-4">
                      <input
                        type="checkbox"
                        checked={termsChecked}
                        onChange={() => setTermsChecked(!termsChecked)}
                        className="w-4 h-4 text-darkBlue border-darkBlue rounded"
                      />
                      <div className="text-sm font-light text-smokyGray">
                        <p>
                          I have read <a href="#" className="underline">MTCH AG's General Terms and Conditions</a> and{" "}
                          <a href="#" className="underline">Privacy Policy</a> as well as the important information! I have read and accept them for all participants.
                        </p>
                      </div>
                    </div>
                    <div className="flex ml-6 items-start gap-3 mb-4">
                      <input
                        type="checkbox"
                        checked={documentsChecked}
                        onChange={() => setDocumentsChecked(!documentsChecked)}
                        className="w-4 h-4 text-darkBlue border-border rounded"
                      />
                      <div className="text-sm font-light text-smokyGray">
                        <p>
                          I confirm that all participants will be in possession of the{" "}
                          <a href="#" className="underline">necessary travel documents</a>{" "}
                          for the chosen destination at the time of departure.
                        </p>
                      </div>
                    </div>
                    <div className="flex ml-6 items-start gap-3 mb-6">
                      <input
                        type="checkbox"
                        checked={newsletterChecked}
                        onChange={() => setNewsletterChecked(!newsletterChecked)}
                        className="w-4 h-4 text-darkBlue border-darkBlue rounded"
                      />
                      <div className="text-sm font-light text-smokyGray">
                        <p>I want to be informed about the latest holiday offers and subscribe to the free newsletter.</p>
                      </div>
                    </div>
                  </div>
                  <FlightPayment/>
                  <div className="mt-8 mb-4 flex justify-end">
                    <button
                      type="submit"
                      disabled={loading} 
                      className="bg-darkBlue text-white rounded-3xl px-5 py-[35px] h-[57px] w-[330px] flex items-center justify-center text-2xl font-medium"
                    >
                      {loading ? (
                        <>
                          <Loader2 className="animate-spin mr-2" /> Processing...
                        </>
                      ) : (
                        "Book and Pay"
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div className="lg:w-1/2 w-full space-y-4">
              <div className="sticky top-16 max-h-[calc(100vh-40px)] overflow-y-auto">
                <FlightBookingCard bookingDetails={bookingDetails} />
              </div>
            </div>
          </div>
        </div>
      </Form>
    </Formik>
  );
};

export default Personaldetails;