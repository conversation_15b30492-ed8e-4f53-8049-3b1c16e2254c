// import React from 'react';,

// const airlines = [,
//   { code: 'AA', name: 'American Airlines' },,
//   { code: 'AC', name: 'Air Canada' },,
//   { code: 'AF', name: 'Air France' },,
//   { code: 'AI', name: 'Air India' },,
//   { code: 'AM', name: 'Aeroméxico' },,
//   { code: 'BA', name: 'British Airways' },,
//   { code: 'BR', name: 'EVA Air' },,
//   { code: 'CA', name: 'Air China' },,
//   { code: 'CI', name: 'China Airlines' },,
//   { code: 'CX', name: 'Cathay Pacific Airways' },,
//   { code: 'DL', name: 'Delta Air Lines' },,
//   { code: 'EK', name: 'Emirates' },,
//   { code: 'ET', name: 'Ethiopian Airlines' },,
//   { code: 'EY', name: 'Etihad Airways' },,
//   { code: 'GA', name: 'Garuda Indonesia' },,
//   { code: 'J<PERSON>', name: 'Japan Airlines' },,
//   { code: 'KLM', name: 'KLM Royal Dutch Airlines' },,
//   { code: 'K<PERSON>', name: 'Korean Air' },,
//   { code: 'L<PERSON>', name: 'Lufthansa' },,
//   { code: 'MH', name: 'Malaysia Airlines' },,
//   { code: 'NZ', name: 'Air New Zealand' },,
//   { code: 'PR', name: 'Philippine Airlines' },,
//   { code: 'QR', name: 'Qatar Airways' },,
//   { code: 'QF', name: 'Qantas Airways' },,
//   { code: 'SQ', name: 'Singapore Airlines' },,
//   { code: 'SU', name: 'Aeroflot' },,
//   { code: 'SV', name: 'Saudia' },,
//   { code: 'TK', name: 'Turkish Airlines' },,
//   { code: 'UA', name: 'United Airlines' },,
//   { code: 'VS', name: 'Virgin Atlantic' },,
//   { code: 'WN', name: 'Southwest Airlines' },,
// ];,

// const regionalAirlines = [,
//   { code: 'A3', name: 'Aegean Airlines' },,
//   { code: 'AD', name: 'Azul Airlines' },,
//   { code: 'AK', name: 'AirAsia Malaysia' },,
//   { code: 'AS', name: 'Alaska Airlines' },,
//   { code: 'B6', name: 'JetBlue Airways' },,
//   { code: 'DN', name: 'Norwegian Air' },,
//   { code: 'FZ', name: 'FlyDubai' },,
//   { code: 'G3', name: 'Gol Linhas Aéreas' },,
//   { code: 'HA', name: 'Hawaiian Airlines' },,
//   { code: 'IB', name: 'Iberia' },,
//   { code: 'LS', name: 'Jet2.com' },,
//   { code: 'MS', name: 'EgyptAir' },,
//   { code: 'NK', name: 'Spirit Airlines' },,
//   { code: 'PC', name: 'Pegasus Airlines' },,
//   { code: 'PE', name: 'People’s Viennaline' },,
//   { code: 'PW', name: 'Precision Air' },,
//   { code: 'SN', name: 'Brussels Airlines' },,
//   { code: 'TZ', name: 'Scoot' },,
//   { code: 'U2', name: 'easyJet' },,
//   { code: 'VY', name: 'Vueling Airlines' },,
//   { code: 'W6', name: 'Wizz Air' },,
// ];,

// export default function AirlinesList() {,
//   return (,
//     <div className="p-4 bg-gray-100 min-h-screen">,
//       <h1 className="text-2xl font-bold mb-4">Airlines List</h1>,
//       <div className="mb-6">,
//         <h2 className="text-xl font-semibold mb-2">Major Airlines</h2>,
//         <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">,
//           {airlines.map((airline) => (,
//             <li key={airline.code} className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition">,
//               <span className="font-semibold">{airline.code}</span> - {airline.name},
//             </li>,
//           ))},
//         </ul>,
//       </div>,
//       <div>,
//         <h2 className="text-xl font-semibold mb-2">Regional & Low-Cost Airlines</h2>,
//         <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">,
//           {regionalAirlines.map((airline) => (,
//             <li key={airline.code} className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition">,
//               <span className="font-semibold">{airline.code}</span> - {airline.name},
//             </li>,
//           ))},
//         </ul>,
//       </div>,
//     </div>,
//   );,
// },

import React from 'react';

const airlines = [
  { code: 'VX', name: 'Aces' },
  { code: 'XQ', name: 'Action Airlines' },
  { code: 'WZ', name: 'Acvilla Air' },
  { code: 'ZY', name: 'ADA Air' },
  { code: 'JP', name: 'Adria Airways' },
  { code: '7X', name: 'Aea International Pte' },
  { code: 'EI', name: 'AER Lingus Limited' },
  { code: 'E4', name: 'Aero Asia International' },
  { code: 'JR', name: 'Aero California' },
  { code: 'N6', name: 'Aero Continente' },
  { code: 'ML', name: 'Aero Costa Rica Acori' },
  { code: 'P4', name: 'Aero Lineas Sosa' },
  { code: 'YP', name: 'Aero Lloyd Flugreisen' },
  { code: '5R', name: 'Aero Service' },
  { code: 'W4', name: 'Aero Services Executive' },
  { code: 'Z9', name: 'Aero Zambia' },
  { code: 'QA', name: 'Aerocaribe' },
  { code: '2B', name: 'Aerocondor Trans Aereos' },
  { code: 'SX', name: 'Aeroelectric SA de CV' },
  { code: 'SU', name: 'Aeroflot Russian' },
  { code: 'FP', name: 'Aeroleasing SA' },
  { code: '7E', name: 'Aeroline Gmbh' },
  { code: 'AR', name: 'Aerolineas Argentinas' },
  { code: 'YU', name: 'Aerolineas Dominicanas' },
  { code: 'N2', name: 'Aerolineas Internacional' },
  { code: 'A8', name: 'Aerolineas Paraguayas' },
  { code: 'EX', name: 'Aerolineas Santo Domingo' },
  { code: 'VW', name: 'Aeromar Airlines' },
  { code: 'AM', name: 'Aeromexico' },
  { code: 'QO', name: 'Aeromexpress' },
  { code: 'RE', name: 'Aeronautica de Cancun' },
  { code: 'WL', name: 'Aeroperlas' },
  { code: 'PL', name: 'Aeroperu' },
  { code: 'VH', name: 'Aeropostal Alas' },
  { code: 'P5', name: 'Aerorepublica' },
  { code: 'UJ', name: 'Aerosanta Airlines' },
  { code: 'R7', name: 'Aeroservicios Carabobo' },
  { code: '5L', name: 'Aerosur' },
  { code: 'VV', name: 'Aerosweet' },
  { code: '5N', name: 'Aerotour Dominicano Airline' },
  { code: 'X8', name: 'Aerovias Dap SA' },
  { code: 'VE', name: 'Aerovias Venezolanas' },
  { code: 'OY', name: 'African Intercontinental' },
  { code: 'ZI', name: 'Aigle Azur' },
  { code: '6R', name: 'Air Affaires Afrique' },
  { code: 'RK', name: 'Air Afrique' },
  { code: 'AH', name: 'Air Algerie' },
  { code: '3J', name: 'Air Alliance' },
  { code: 'CQ', name: 'Air Alpha' },
  { code: 'A6', name: 'Air Alps Aviation' },
  { code: 'FQ', name: 'Air Aruba' },
  { code: '9A', name: 'Air Atlantic' },
  { code: 'LU', name: 'Air Atlantic Dominicana' },
  { code: 'UU', name: 'Air Austral' },
  { code: 'ZX', name: 'Air B C' },
  { code: 'BT', name: 'Air Baltic Corp' },
  { code: 'AJ', name: 'Air Belgium' },
  { code: 'AB', name: 'Air Berlin' },
  { code: 'JA', name: 'Air Bosna' },
  { code: 'KF', name: 'Air Botnia' },
  { code: 'BP', name: 'Air Botswana Corp' },
  { code: '7L', name: 'Air Bristol Dba Ab Airline' },
  { code: '2J', name: 'Air Burkina' },
  { code: 'PB', name: 'Air Burundi' },
  { code: 'TY', name: 'Air Caledonie' },
  { code: 'SB', name: 'Air Calédonie International' },
  { code: 'AC', name: 'Air Canada' },
  { code: 'WS', name: 'Air Caraibes' },
  { code: 'C2', name: 'Air Caribbean Ltd' },
  { code: 'R9', name: 'Air Charter' },
  { code: 'UT', name: 'Air Charter Asia' },
  { code: 'CV', name: 'Air Chathams' },
  { code: 'CA', name: 'Air China International' },
  { code: '4F', name: 'Air City' },
  { code: '5D', name: 'Air Company Donbass Airline' },
  { code: 'E6', name: 'Air Company Elf Air' },
  { code: 'K3', name: 'Air Company Kraiaero' },
  { code: 'YN', name: 'Air Creebec' },
  { code: 'DY', name: 'Air Djibouti' },
  { code: 'EN', name: 'Air Dolomiti Spa' },
  { code: 'ER', name: 'Air East' },
  { code: 'A5', name: 'Air East Limited' },
  { code: 'RQ', name: 'Air Engiadiana' },
  { code: 'UX', name: 'Air Europa' },
  { code: 'PE', name: 'Air Europe Spa' },
  { code: 'XV', name: 'Air Express' },
  { code: 'FZ', name: 'Air Facilities' },
  { code: 'PC', name: 'Air Fiji Ltd' },
  { code: 'AF', name: 'Air France' },
  { code: 'GN', name: 'Air Gabon' },
  { code: 'DA', name: 'Air Georgia' },
  { code: 'GB', name: 'Air Glaciers SA' },
  { code: 'JG', name: 'Air Greece' },
  { code: 'NY', name: 'Air Iceland' },
  { code: 'AI', name: 'Air India' },
  { code: 'IT', name: 'Air Inter Europe' },
  { code: '3H', name: 'Air Inuit Ltd' },
  { code: 'VU', name: 'Air Ivoire' },
  { code: 'JM', name: 'Air Jamaica' },
  { code: 'BC', name: 'Air Jet' },
  { code: '9Y', name: 'Air Kazakstan' },
  { code: 'QP', name: 'Air Kenya Aviation' },
  { code: '9R', name: 'Air Kilroe Limited' },
  { code: 'JS', name: 'Air Koryo' },
  { code: 'U', name: 'Air Liberté' },
  { code: 'GW', name: 'Air Lines of Kuban' },
  { code: 'DR', name: 'Air Link Pty' },
  { code: 'TT', name: 'Air Lithuania' },
  { code: 'FU', name: 'Air Littoral' },
  { code: 'NX', name: 'Air Macau Company Ltd' },
  { code: 'MD', name: 'Air Madagascar' },
  { code: 'QM', name: 'Air Malawi Limited' },
  { code: 'L6', name: 'Air Maldives Ltd' },
  { code: 'L9', name: 'Air Mail SA' },
  { code: 'KM', name: 'Air Malta' },
  { code: '6T', name: 'Air Mandalay Ltd' },
  { code: '7N', name: 'Air Manitoba' },
  { code: 'CW', name: 'Air Marshall Islands Inc' },
  { code: 'MR', name: 'Air Mauritanie' },
  { code: 'MK', name: 'Air Mauritius' },
  { code: 'ZV', name: 'Air Midwest' },
  { code: 'MC', name: 'Air Mobility Command' },
  { code: '9U', name: 'Air Moldova' },
  { code: 'RM', name: 'Air Moldova International' },
  { code: 'SW', name: 'Air Namibia' },
  { code: 'ON', name: 'Air Nauru' },
  { code: 'LW', name: 'Air Nevada' },
  { code: 'NZ', name: 'Air New Zealand Ltd' },
  { code: 'EL', name: 'Air Nippon Co Ltd' },
  { code: 'PX', name: 'Air Niugini' },
  { code: 'ID', name: 'Air Normandie' },
  { code: '4N', name: 'Air North' },
  { code: 'YW', name: 'Air Nostrum' },
  { code: 'QK', name: 'Air Nova' },
  { code: 'YH', name: 'Air Nunavut' },
  { code: 'AP', name: 'Air One' },
  { code: '8K', name: 'Air Ostrava Ltd' },
  { code: 'FJ', name: 'Air Pacific Ltd' },
  { code: '2P', name: 'Air Philippines Corp' },
  { code: 'P3', name: 'Air Provence' },
  { code: 'GZ', name: 'Air Rarotonga' },
  { code: 'RY', name: 'Air Rwanda' },
  { code: '7W', name: 'Air Sask Aviation' },
  { code: 'DS', name: 'Air Senegal' },
  { code: 'HM', name: 'Air Seychelles' },
  { code: 'BM', name: 'Air Sicilia' },
  { code: '4D', name: 'Air Sinai' },
  { code: 'GM', name: 'Air Slovakia BWJ' },
  { code: 'WV', name: 'Air South Airlines Inc' },
  { code: '8H', name: 'Air South West' },
  { code: 'OJ', name: 'Air St Barthelemy' },
  { code: 'S6', name: 'Air St Martin' },
  { code: 'PJ', name: 'Air St Pierre' },
  { code: 'ZP', name: 'Air St Thomas' },
  { code: 'GO', name: 'Air Stord' },
  { code: 'IU', name: 'Air Straubing' },
  { code: 'YI', name: 'Air Sunshine' },
  { code: 'VT', name: 'Air Tahiti' },
  { code: 'TN', name: 'Air Tahiti Nui' },
  { code: 'TC', name: 'Air Tanzania Corp' },
  { code: '8T', name: 'Air Tindi Ltd' },
  { code: 'SH', name: 'Air Toulouse' },
  { code: 'T7', name: 'Air Trans Ireland Services' },
  { code: 'TF', name: 'Air Transport Pyrenees' },
  { code: 'ZH', name: 'Air Truck' },
  { code: 'VK', name: 'Air Tungaru' },
  { code: 'DP', name: 'Air Two Thousand Ltd' },
  { code: 'UK', name: 'Air UK Ltd' },
  { code: '6U', name: 'Air Ukraine' },
  { code: '3N', name: 'Air Urga' },
  { code: 'NF', name: 'Air Vanuata' },
  { code: '6V', name: 'Air Vegas' },
  { code: 'ZW', name: 'Air Wisconsin Airlines' },
  { code: 'OC', name: 'Air Zaire' },
  { code: 'ZT', name: 'Air Zambezi' },
  { code: 'UM', name: 'Air Zimbabwe' },
  { code: 'AK', name: 'Airasia Sdn Bhd' },
  { code: 'ZF', name: 'Airborne of Sweden AB' },
  { code: 'A9', name: 'Airompany Aizrena' },
  { code: '4C', name: 'Aires' },
  { code: 'UL', name: 'Airlanka Ltd' },
  { code: 'C4', name: 'Airlines Of Carriacou' },
  { code: 'IP', name: 'Airlines of Tasmania' },
  { code: '5T', name: 'Airlink Airline Ltd' },
  { code: 'ND', name: 'Airlink Pty Ltd' },
  { code: 'TL', name: 'Airport Regional' },
  { code: 'LQ', name: 'Airpac Airlines' },
  { code: 'VZ', name: 'Airtours Intl Airways' },
  { code: 'FL', name: 'Airtran Airways' },
  { code: 'HO', name: 'Airways International' },
  { code: 'HT', name: 'Airwork' },
  { code: 'E9', name: 'Ajt Air International' },
  { code: '6L', name: 'Aklak Inc' },
  { code: 'AS', name: 'Alaska Airlines' },
  { code: 'KO', name: 'Alaska Central Express' },
  { code: '6D', name: 'Alaska Island Air Inc' },
  { code: 'UI', name: 'Alaska Seaplane Service' },
  { code: 'LV', name: 'Albanian Airlines Mak' },
  { code: 'AZ', name: 'Alitalia' },
  { code: 'XM', name: 'Alitalia Express' },
  { code: 'RD', name: 'Alitalia Team' },
  { code: 'NH', name: 'All Nippon Airways' },
  { code: 'Y2', name: 'Alliance' },
  { code: 'CD', name: 'Alliance Air' },
  { code: 'EO', name: 'Alliance Airlines' },
  { code: 'AQ', name: 'Aloha Airlines' },
  { code: 'WP', name: 'Aloha Islandair' },
  { code: 'E8', name: 'Alpi Eagles Spa' },
  { code: '5A', name: 'Alpine Aviation' },
  { code: 'HP', name: 'America West Airlines' },
  { code: 'AA', name: 'American Airlines' },
  { code: 'TZ', name: 'American Trans Air' },
  { code: '2V', name: 'Amtrak' },
  { code: 'C3', name: 'Angola Air Charter' },
  { code: 'AN', name: 'Ansett' },
  { code: 'ZQ', name: 'Ansett New Zealand' },
  { code: 'HU', name: 'Antigua Paradise Airways' },
  { code: 'LM', name: 'Antillean Airlines' },
  { code: 'IW', name: 'AOM French Airlines' },
  { code: '7P', name: 'Apa International Air' },
  { code: 'K6', name: 'Appolo Airlines S A' },
  { code: 'Y5', name: 'Arax Airways' },
  { code: 'F5', name: 'Archana Airways Ltd' },
  { code: '8A', name: 'Arctic Air As' },
  { code: '7S', name: 'Arctic Transportation' },
  { code: 'FG', name: 'Ariana Afghan Airlines' },
  { code: 'D4', name: 'Aries del Sur' },
  { code: 'IZ', name: 'Arkia Israeli Airlines' },
  { code: 'R3', name: 'Armenian Airlines' },
  { code: 'Y3', name: 'Asia Service Airlines' },
  { code: 'OZ', name: 'Asiana Airlines' },
  { code: 'AD', name: 'Aspen Mountain Air' },
  { code: 'OI', name: 'Aspiring Air Services' },
  { code: 'A7', name: 'Astanair' },
  { code: 'S3', name: 'Astoria' },
  { code: 'AL', name: 'Astral aviation' },
  { code: '9T', name: 'Athabaska Airways' },
  { code: 'L4', name: 'Atlant Sv' },
  { code: 'RC', name: 'Atlantic Airways' },
  { code: 'EV', name: 'Atlantic Southeast Airlines' },
  { code: 'IQ', name: 'Augsburg Airways' },
  { code: 'HB', name: 'Augusta Airways' },
  { code: 'GR', name: 'Aurigny Air Services' },
  { code: 'NO', name: 'AUS Air' },
  { code: '7V', name: 'Austin Express' },
  { code: 'AU', name: 'Austral Lineas Aereas' },
  { code: 'KC', name: 'Australian Commuter Air' },
  { code: 'OS', name: 'Austrian Airlines' },
  { code: 'OG', name: 'Austrian Airtransport' },
  { code: 'OT', name: 'Avant Airlines' },
  { code: 'VE', name: 'Avensa' },
  { code: '3R', name: 'Avia Air Nv' },
  { code: 'OC', name: 'Aviation del Noroeste SA' },
  { code: 'AO', name: 'Aviaco' },
  { code: 'T5', name: 'Aviacompany Turkmenistan' },
  { code: '6A', name: 'Aviaosa' },
  { code: 'AV', name: 'Avianca' },
  { code: '7U', name: 'Avianergo' },
  { code: 'J5', name: 'Aviaprima Airlines' },
  { code: 'GU', name: 'Aviateca' },
  { code: 'BJ', name: 'Aviation Commercial' },
  { code: 'Q6', name: 'Aviation Mineralnye Vody' },
  { code: '3B', name: 'Avior' },
  { code: 'DF', name: 'Aviosarda' },
  { code: 'J2', name: 'Azerbaijan Hava Yollari' },
  { code: 'ZS', name: 'Azzurra Air Spa' },
  { code: 'UP', name: 'Bahamasair' },
  { code: 'X3', name: 'Baikal Airlines' },
  { code: '8Q', name: 'Baker Aviation' },
  { code: 'LZ', name: 'Balkan' },
  { code: 'TI', name: 'Baltic Intl Airlines' },
  { code: 'PG', name: 'Bangkok Airways' },
  { code: '5E', name: 'Base Regional Airlines' },
  { code: 'V9', name: 'Bashkir Airlines' },
  { code: '6B', name: 'Baxter Aviation' },
  { code: 'JV', name: 'Bearskin Airlines' },
  { code: 'B2', name: 'Belavia' },
  { code: '7G', name: 'Bellair Inc' },
  { code: 'B3', name: 'Bellview Airlines Ltd' },
  { code: 'CH', name: 'Bemidji Airlines' },
  { code: '8E', name: 'Bering Air' },
  { code: 'J8', name: 'Berjaya Air' },
  { code: 'B4', name: 'Bhoja Airlines Ltd' },
  { code: 'GQ', name: 'Big Sky Airlines' },
  { code: 'BG', name: 'Biman Bangladesh Airline' },
  { code: 'NT', name: 'Binter Canarias' },
  { code: 'AX', name: 'Binter Mediterranean' },
  { code: 'H9', name: 'Blade Helicopters' },
  { code: 'YM', name: 'Blue Sky Carrier' },
  { code: '5F', name: 'Bonaire Airways' },
  { code: 'BO', name: 'Bouraq Airlines' },
  { code: 'BU', name: 'Braathens' },
  { code: 'C6', name: 'Bright Air' },
  { code: 'DB', name: 'Brit Air' },
  { code: 'BY', name: 'Britannia Airways' },
  { code: 'BA', name: 'British Airways' },
  { code: 'BS', name: 'British International' },
  { code: 'KJ', name: 'British Mediterranean' },
  { code: 'BD', name: 'British Midland Airways' },
  { code: 'J4', name: 'Buffalo Airways Ltd' },
  { code: 'II', name: 'Business Air Ltd' },
  { code: 'HQ', name: 'Business Express' },
  { code: 'BW', name: 'Bwia International' },
  { code: 'MO', name: 'Calm Air International' },
  { code: 'XE', name: 'Cambodia Intl Airlines' },
  { code: '2T', name: 'Cameroon Airlines' },
  { code: 'CP', name: 'Canada Three Thousand' },
  { code: 'AC', name: 'Canadian Airlines' },
  { code: '9K', name: 'Canarias Regional Air' },
  { code: '9K', name: 'Cape Air' },
  { code: '6C', name: 'Cape Smythe Air Service' },
  { code: 'NS', name: 'Cape York Air' },
  { code: 'IM', name: 'Cardinal Airlines Ltd' },
  { code: '9G', name: 'Carib Express Ltd' },
  { code: 'B9', name: 'Caribbean Airlines Inc' },
  { code: 'KW', name: 'Carnival Airlines' },
  { code: 'XP', name: 'Carribean Winds Airlines' },
  { code: 'CX', name: 'Casino Express Airlines' },
  { code: 'CX', name: 'Cathay Pacific' },
  { code: 'KX', name: 'Cayman Airways Ltd' },
  { code: 'ED', name: 'CCalr' },
  { code: '5J', name: 'Cebu Pacific Air' },
  { code: '9M', name: 'Centennial Airlines SA' },
  { code: '9M', name: 'Central Mountain Air' },
  { code: '22', name: 'Chalair' },
  { code: 'C8', name: 'Changan Airlines' },
  { code: 'C1', name: 'Chicago Express Airlines' },
  { code: 'CI', name: 'China Airlines' },
  { code: 'MU', name: 'China Eastern Airlines' },
  { code: 'GP', name: 'China General Aviation' },
  { code: 'F6', name: 'China National Aviation' },
  { code: 'CJ', name: 'China Northern Airlines' },
  { code: 'WH', name: 'China Northwest Airlines' },
  { code: 'CZ', name: 'China Southern Airlines' },
  { code: 'SZ', name: 'China Southwest Airlines' },
  { code: 'X2', name: 'China Xinhua Airlines' },
  { code: '3Q', name: 'China Yunnan Airlines' },
  { code: 'QI', name: 'Cimber Air' },
  { code: 'FD', name: 'City Bird' },
  { code: 'WX', name: 'Cityflyer Express' },
  { code: 'C9', name: 'Cityjet' },
  { code: 'BX', name: 'Co Aéronautique Européen' },
  { code: 'DQ', name: 'Coastal Air Transport' },
  { code: '9L', name: 'Colgan Air' },
  { code: '7C', name: 'Color Air' },
  { code: 'OH', name: 'Columbia Pacific Airline' },
  { code: 'MN', name: 'Comair' },
  { code: 'DO', name: 'Commercial Airways' },
  { code: 'DE', name: 'Condor Flugdienst' },
  { code: '5C', name: 'Conquest Airlines' },
  { code: 'V8', name: 'Contact Air' },
  { code: 'CO', name: 'Continental Airlines' },
  { code: 'CS', name: 'Continental Micro' },
  { code: 'CM', name: 'Copa Compania Panamena' },
  { code: 'KN', name: 'Coral International Air' },
  { code: 'UH', name: 'Corp Airlines Canberra' },
  { code: '3C', name: 'Corporate Express Airline' },
  { code: 'SS', name: 'Corse Air International' },
  { code: 'XK', name: 'Corse Mediterranean' },
  { code: 'ZE', name: 'Cosmos Air' },
  { code: 'XL', name: 'Country Connection Airline' },
  { code: 'C5', name: 'Cretan Airlines SA' },
  { code: 'OR', name: 'Crimea Air' },
  { code: 'OU', name: 'Croatia Airlines' },
  { code: 'X5', name: 'Cronus Airlines' },
  { code: 'LX', name: 'Crossair' },
  { code: 'QE', name: 'Crossair Europe' },
  { code: 'CU', name: 'Cubana de Aviación' },
  { code: 'R6', name: 'Cypress Airlines' },
  { code: 'CY', name: 'Cyprus Airways' },
  { code: 'OK', name: 'Czech Airlines' },
  { code: 'D3', name: 'Daallo Airlines' },
  { code: '6P', name: 'Dac Air' },
  { code: 'DX', name: 'Danish Air Transport' },
  { code: '2G', name: 'Debonair Airways' },
  { code: 'DL', name: 'Delta Air Lines' },
  { code: '2D', name: 'Denim Air' },
  { code: 'DI', name: 'Deutsche BA' },
  { code: '2A', name: 'Deutsche Bahn AG' },
  { code: 'D8', name: 'Diamond Sakha Airlines' },
  { code: 'D7', name: 'Dinar Lineas Aereas SA' },
  { code: 'MG', name: 'Djibouti Airlines' },
  { code: 'Z6', name: 'Dnieproavia State' },
  { code: '8U', name: 'Dolphin Express Airlines' },
  { code: 'E3', name: 'Domodedovo Airlines' },
  { code: 'Q8', name: 'Donetsk Aviation' },
  { code: '4U', name: 'Dorado Air SA' },
  { code: 'E7', name: 'Downeast Express' },
  { code: 'KB', name: 'Druk Air' },
  { code: 'ZN', name: 'Eagle Airlines' },
  { code: 'Y4', name: 'Eagle Aviation Ltd' },
  { code: 'FE', name: 'Eagle Canyon Airlines' },
  { code: 'EA', name: 'Eas Europe Airlines' },
  { code: '4S', name: 'East West Airline' },
  { code: 'XZ', name: 'Eastair' },
  { code: 'T3', name: 'Eastern Airways Ltd' },
  { code: 'DK', name: 'Eastland Air' },
  { code: 'W9', name: 'Eastwind Capital Partner' },
  { code: 'U2', name: 'Easyjet Airline Company Ltd' },
  { code: '8Y', name: 'Ecuato Guineana De Aviac' },
  { code: 'EU', name: 'Ecuatoriana De Aviación' },
  { code: 'MS', name: 'Egyptair' },
  { code: 'LY', name: 'El Al Israel Airlines' },
  { code: 'G3', name: 'Emerald Airways' },
  { code: 'YE', name: 'Emerald European Airways' },
  { code: 'EK', name: 'Emirates' },
  { code: '7H', name: 'Era Aviation' },
  { code: 'OV', name: 'Estonian Air' },
  { code: 'S8', name: 'Estonian Aviation' },
  { code: 'ET', name: 'Ethiopian Airlines' },
  { code: 'RN', name: 'Euralair International' },
  { code: 'F4', name: 'Eureca SRL' },
  { code: 'TH', name: 'Euroair' },
  { code: 'HZ', name: 'Euroflight Sweden AB' },
  { code: 'GJ', name: 'Eurofly Spa' },
  { code: 'Y6', name: 'Europe Elite' },
  { code: 'M3', name: 'European Air Express' },
  { code: 'QY', name: 'European Air Transport' },
  { code: '2H', name: 'European Airlines' },
  { code: 'L8', name: 'European Airways Ltd' },
  { code: 'MY', name: 'Euroscot Airways Ltd' },
  { code: 'JO', name: 'Eurosky Airlines' },
  { code: 'EW', name: 'Eurowings' },
  { code: 'BR', name: 'Eva Airways Corp' },
  { code: 'E2', name: 'Everest Air Ltd' },
  { code: 'NA', name: 'Executive Airlines' },
  { code: '5W', name: 'Executive Express Ltd.' },
  { code: 'FO', name: 'Expedition Airways' },
  { code: '9E', name: 'Express Airlines' },
  { code: 'N8', name: 'Expresso Aero' },
  { code: 'FV', name: 'F Airlines B V' },
  { code: 'LK', name: 'Fairlines' },
  { code: '4X', name: 'Fairlines B.V.' },
  { code: 'IH', name: 'Falcon Aviation AB' },
  { code: 'EF', name: 'Far Eastern Air Transport' },
  { code: 'CF', name: 'Faucett' },
  { code: 'FX', name: 'Fedex' },
  { code: 'AY', name: 'Finnair' },
  { code: 'FA', name: 'Finnaviation' },
  { code: '7F', name: 'First Air' },
  { code: '8N', name: 'Flagship Airlines' },
  { code: 'FK', name: 'Flamenco Airways' },
  { code: 'IX', name: 'Flandre Air' },
  { code: 'YC', name: 'Flight West Airlines' },
  { code: 'MT', name: 'Flying Colours Airlines' },
  { code: 'F3', name: 'Flying Enterprise' },
  { code: 'VY', name: 'Formosa Airlines Corp.' },
  { code: 'Q5', name: 'Forty Mile Air' },
  { code: 'ZU', name: 'Freedom Air' },
  { code: 'F9', name: 'Frontier Airlines' },
  { code: '2F', name: 'Frontier Flying Service' },
  { code: 'IV', name: 'Fujian Airlines' },
  { code: 'FH', name: 'Futura International' },
  { code: 'G7', name: 'Gandalf Airlines Spa' },
  { code: 'GA', name: 'Garuda Indonesia' },
  { code: 'GT', name: 'GB Airways' },
  { code: '3P', name: 'Georgian Airlines' },
  { code: 'GH', name: 'Ghana Airways' },
  { code: '9C', name: 'Gill Aviation Ltd' },
  { code: 'GK', name: 'Go One Airways' },
  { code: 'DC', name: 'Golden Air Flyg Ab' },
  { code: 'YD', name: 'Gomelavia' },
  { code: '5Z', name: 'Gonini Air Servic' },
  { code: '8L', name: 'Grand International Airway' },
  { code: 'G9', name: 'Grant Aviation' },
  { code: 'MV', name: 'Great American Airways' },
  { code: 'IF', name: 'Great China Airlines' },
  { code: 'ZK', name: 'Great Lakes Aviation Ltd' },
  { code: 'GL', name: 'Greenlandair' },
  { code: 'FB', name: 'Guard Air' },
  { code: 'G4', name: 'Guizhou Airlines' },
  { code: 'G8', name: 'Gujarat Airways Ltd' },
  { code: 'GF', name: 'Gulf Air' },
  { code: '3M', name: 'Gulfstream International' },
  { code: 'GY', name: 'Guyana Airways' },
  { code: 'H6', name: 'Hageland Aviation' },
  { code: 'HR', name: 'Hahn Air' },
  { code: 'FY', name: 'Hahnair Friedrichshafen' },
  { code: 'H4', name: 'Hainan Airlines' },
  { code: '7A', name: 'Haines Airways' },
  { code: '2N', name: 'Haiti Aviation' },
  { code: 'X4', name: 'Haiti Trans Air' },
  { code: 'WD', name: 'Halisa Air' },
  { code: '8F', name: 'Hanair Haiti Ntl Air' },
  { code: 'HF', name: 'Hapag Lloyd' },
  { code: 'HG', name: 'Harbor Airlines' },
  { code: 'H3', name: 'Harbour Air Ltd' },
  { code: 'HA', name: 'Hawaiian Airlines' },
  { code: 'ZL', name: 'Hazelton Airlines' },
  { code: '2Y', name: 'Helenair Corporation Ltd' },
  { code: 'LE', name: 'Helgoland Airlines' },
  { code: 'YO', name: 'Heli Air Monaco' },
  { code: 'EC', name: 'Heli Inter Riviera' },
  { code: '5H', name: 'Heli Trans' },
  { code: 'ES', name: 'Helicopteros Del Cusco' },
  { code: 'UV', name: 'Helicopteros del Sureste' },
  { code: 'JB', name: 'Helijet Airways' },
  { code: 'YQ', name: 'Helikopterservice' },
  { code: 'DU', name: 'Hemus Air' },
  { code: 'UD', name: 'HEX Air' },
  { code: 'HS', name: 'Highland Air AB' },
  { code: 'KA', name: 'Hong Kong Dragon Airline' },
  { code: 'QX', name: 'Horizon Air' },
  { code: 'IB', name: 'IBERIA' },
  { code: 'FI', name: 'Icelandair' },
  { code: 'LS', name: 'Illamna Air Taxi Inc' },
  { code: '7I', name: 'Imperial Air' },
  { code: 'VQ', name: 'Impulse Airlines Ltd' },
  { code: 'IC', name: 'Indian Airlines Ltd' },
  { code: 'D6', name: 'Inter Air' },
  { code: 'JC', name: 'Inter Air Direct' },
  { code: 'Q9', name: 'Interbrasil Star S.A.' },
  { code: 'RS', name: 'Intercont de Aviación' },
  { code: 'M4', name: 'Interimpex Avioimpex' },
  { code: 'YK', name: 'International Aviation' },
  { code: 'U5', name: 'International Business Air' },
  { code: 'A2', name: 'Intersonal' },
  { code: 'IR', name: 'Iran Air' },
  { code: 'Y7', name: 'Iran Asseman Airlines' },
  { code: 'IA', name: 'Iraqi Airways' },
  { code: '2E', name: 'Ireland Airways' },
  { code: 'F7', name: 'Iron Dragon Fly Air Co' },
  { code: 'IS', name: 'Island Airlines' },
  { code: 'G5', name: 'Island Air Ltd' },
  { code: 'DG', name: 'Island Airlines Pty' },
  { code: '2S', name: 'Island Express' },
  { code: 'CN', name: 'Island Nationalr' },
  { code: 'HH', name: 'Islandsflug' },
  { code: 'WC', name: 'Islena de Inversiones SA' },
  { code: '5Y', name: 'Isles Of Scilly Skybus' },
  { code: 'IL', name: 'Istanbul Airlines' },
  { code: 'B8', name: 'Italair' },
  { code: 'K9', name: 'Itapemirim Transportes' },
  { code: 'JL', name: 'JAL Japan Airlines' },
  { code: '3X', name: 'Japan Air Commuter' },
  { code: 'JD', name: 'Japan Air System Company' },
  { code: 'EG', name: 'Japan Asia Airways' },
  { code: 'NU', name: 'Japan Transocean Air Co' },
  { code: 'JT', name: 'Jaro Intl Airlines' },
  { code: 'JY', name: 'Jersey European' },
  { code: 'QJ', name: 'Jet Airways Inc' },
  { code: '9W', name: 'Jet Airways Ltd' },
  { code: 'J9', name: 'Jet Aspen' },
  { code: 'PP', name: 'Jet Aviation Business AG' },
  { code: 'LF', name: 'Jet Train Corp' },
  { code: '8J', name: 'Jetail' },
  { code: 'D9', name: 'Joint Stock Aviation Co' },
  { code: 'P7', name: 'Joint Stock Co East Line' },
  { code: 'X7', name: 'Joint Stock Company' },
  { code: '5M', name: 'Joint Stock Company Siat' },
  { code: 'XC', name: 'K D Air Corporation' },
  { code: 'L3', name: 'Kalken Lineas Aereas' },
  { code: 'K8', name: 'Kaliningrad Airlines' },
  { code: 'KT', name: 'Kampuchea Airlines' },
  { code: 'KR', name: 'Kar Air' },
  { code: '2L', name: 'Kariog Air' },
  { code: 'K4', name: 'Kazakhstan Airlines' },
  { code: 'KD', name: 'Kendell Airlines' },
  { code: 'M5', name: 'Kenmore Air' },
  { code: '4K', name: 'Kenn Borek Air' },
  { code: 'KQ', name: 'Kenya Airways' },
  { code: 'BZ', name: 'Keystone Air Service' },
  { code: 'H8', name: 'Khabarovsk Aviation' },
  { code: 'X6', name: 'Khors Aircompany Ltd' },
  { code: '2K', name: 'Kitty Hawk Airlines Inc' },
  { code: 'KP', name: 'Kiwi International Air' },
  { code: 'KL', name: 'KLM' },
  { code: 'WA', name: 'KLM City Hopper' },
  { code: 'XT', name: 'KLM Exel' },
  { code: 'NE', name: 'Knight Air' },
  { code: 'KE', name: 'Korean Air' },
  { code: '6K', name: 'Korsar' },
  { code: '7B', name: 'Krasnoyarsk Airlines' },
  { code: 'KU', name: 'Kuwait Airways' },
  { code: 'K2', name: 'Kyrghyzstan Airlines' },
  { code: 'KH', name: 'Kymair' },
  { code: 'JF', name: 'L A B Flying Service' },
  { code: '7Z', name: 'L.B.Limited' },
  { code: 'WJ', name: 'Labrador Airways Ltd' },
  { code: 'LR', name: 'Lacsa Airlines' },
  { code: 'UC', name: 'Ladeco Airlines' },
  { code: '6F', name: 'Laker Airways Inc' },
  { code: 'TM', name: 'LAM Mozambique' },
  { code: 'LA', name: 'Lan Chile' },
  { code: 'LP', name: 'Lan Peru' },
  { code: 'BN', name: 'Landair Intl Airlines' },
  { code: 'QV', name: 'Lao Aviation' },
  { code: 'MJ', name: 'Lapa' },
  { code: '7K', name: 'Larrys Flying Service' },
  { code: '6G', name: 'Las Vegas Airlines' },
  { code: 'NG', name: 'Lauda Air' },
  { code: 'QL', name: 'Lesotho Airways' },
  { code: 'HE', name: 'Low Luftfahrtges Walter' },
  { code: 'LI', name: 'Liat Caribbean Airline' },
  { code: 'LN', name: 'Libyan Airlines' },
  { code: 'GC', name: 'Lina Congo' },
  { code: 'RT', name: 'Lincoln Airlines' },
  { code: 'KZ', name: 'Linea Aerea Ejecutivo' },
  { code: 'KG', name: 'Linea Aerea IAACA' },
  { code: 'LL', name: 'Lineas Aereas Allegro' },
  { code: '5U', name: 'Lineas Aereas del Estado' },
  { code: 'XU', name: 'Link Airways' },
  { code: 'TE', name: 'Lithuanian Airlines' },
  { code: 'LC', name: 'Loganair' },
  { code: 'YL', name: 'Long Island Airlines Ltd' },
  { code: 'LO', name: 'LOT Polish Airlines' },
  { code: '4J', name: 'Love Air' },
  { code: 'LT', name: 'LTU' },
  { code: 'LH', name: 'Lufthansa' },
  { code: 'CL', name: 'Lufthansa Citylin' },
  { code: 'DV', name: 'LUFTTAXI FLUGGESELLSCHAFT' },
  { code: 'L5', name: 'Lufttransport' },
  { code: 'LG', name: 'Luxair' },
  { code: 'L7', name: 'Lviv Airlines' },
  { code: 'IK', name: 'Lynden Air Cargo' },
  { code: 'CC', name: 'Macair Airlines' },
  { code: 'IN', name: 'Macedonian Airlines' },
  { code: 'DM', name: 'Maersk Air' },
  { code: 'VB', name: 'Maersk Air Ltd' },
  { code: '3A', name: 'Mafira Air' },
  { code: 'H5', name: 'Magadan Airlines' },
  { code: 'M2', name: 'Mahfooz Aviation G Ltd' },
  { code: '9F', name: 'Majestic Airways' },
  { code: 'MH', name: 'Malaysia Airlines' },
  { code: 'MA', name: 'Malev Hungarian Airlines' },
  { code: '6E', name: 'Malmo Aviation' },
  { code: 'R5', name: 'Malta Air Charter' },
  { code: 'AE', name: 'Mandarin Airlines' },
  { code: 'JE', name: 'Manx Airlines' },
  { code: 'BF', name: 'Markair' },
  { code: 'MP', name: 'Martinair Holland' },
  { code: 'T9', name: 'Master Aviation' },
  { code: '6M', name: 'Maverick Airways Corp' },
  { code: '8M', name: 'Maxair' },
  { code: 'MW', name: 'Maya Airways' },
  { code: 'EY', name: 'Mayan World Airlines' },
  { code: 'IG', name: 'Meridiana Spa' },
  { code: 'MZ', name: 'Merpati' },
  { code: 'YV', name: 'Mesa Airlines' },
  { code: 'XJ', name: 'Mesaba Airlines' },
  { code: 'MX', name: 'Mexicana' },
  { code: 'OM', name: 'Miat Mongolian Airlines' },
  { code: 'ME', name: 'Middle East Airlines' },
  { code: 'JI', name: 'Midway Airlines' },
  { code: 'YX', name: 'Midwest Express Airlines' },
  { code: '2Q', name: 'Millenium Air Corp' },
  { code: 'CG', name: 'Milne Bay Air Pty' },
  { code: 'Q2', name: 'Minerva Airlines' },
  { code: '4M', name: 'Minskavia' },
  { code: 'FS', name: 'Missionary Aviation' },
  { code: 'M9', name: 'Modiluit Ltd' },
  { code: '2M', name: 'Moldavian Airlines' },
  { code: 'ZB', name: 'Monarch Airlines' },
  { code: 'M8', name: 'Moscow Airways' },
  { code: 'NM', name: 'Mount Cook Airline' },
  { code: 'ZR', name: 'MUK Air' },
  { code: 'Q4', name: 'Mustique Airways' },
  { code: 'UB', name: 'Myanmar Airways Intl' },
  { code: '3W', name: 'Nanjing Airlines' },
  { code: 'HC', name: 'Naske Air' },
  { code: 'K7', name: 'National Air Sakha Aviation' },
  { code: 'YJ', name: 'National Airlines' },
  { code: 'N4', name: 'National Airlines Chile' },
  { code: 'N7', name: 'National Airlines Inc.' },
  { code: 'NC', name: 'National Jet Systems Pty' },
  { code: 'CE', name: 'Nationwide Air' },
  { code: '3Z', name: 'Necon Air' },
  { code: 'YA', name: 'Nego Airline One' },
  { code: 'D5', name: 'Nepc Airlines' },
  { code: 'EJ', name: 'New England Airlines' },
  { code: 'HD', name: 'New York Helicopter Corp' },
  { code: 'Z5', name: 'Newwest Airlines Inc' },
  { code: '6Y', name: 'Nica' },
  { code: 'JX', name: 'Nice Helicoptères' },
  { code: 'WT', name: 'Nigeria Airways' },
  { code: 'U4', name: 'Noman' },
  { code: 'JH', name: 'Nordeste Linhas Aereas' },
  { code: 'DJ', name: 'Nordic European Airlines' },
  { code: 'NR', name: 'Norontair' },
  { code: 'N9', name: 'North Coast Aviation Pty' },
  { code: 'VL', name: 'North Vancouver Airlines' },
  { code: 'HW', name: 'North Wright Air' },
  { code: 'QB', name: 'Northern Airlines Sanya' },
  { code: 'UO', name: 'Northern Star Airlines' },
  { code: 'NW', name: 'Northwest Airlines' },
  { code: '3E', name: 'Northwestern Air Lease' },
  { code: 'NV', name: 'Nwt Air' },
  { code: 'UQ', name: 'O Connor Mt Gamblers' },
  { code: '4B', name: 'Olson Air Service' },
  { code: 'OL', name: 'OLT Ostfriesische Lufttransport' },
  { code: 'OA', name: 'Olympic Airways' },
  { code: 'WY', name: 'Oman Air' },
  { code: 'OW', name: 'OneWorld Alliance' },
  { code: 'NQ', name: 'Orbi Georgian Air' },
  { code: 'RF', name: 'Ord Air Charter PTY LTD' },
  { code: 'V6', name: 'Orient Avia' },
  { code: 'OX', name: 'Orient Express Air' },
  { code: 'R1', name: 'P T Mandala Airlines' },
  { code: 'AW', name: 'P.T. Dirgantara Air Service' },
  { code: 'BL', name: 'Pacific Airlines' },
  { code: 'GX', name: 'Pacific Airways Corp' },
  { code: '8P', name: 'Pacific Coastal Airlines' },
  { code: 'JW', name: 'Pacific Eagle Airlines' },
  { code: '9J', name: 'Pacific Island Aviation' },
  { code: '2W', name: 'Pacific Midland Airlines' },
  { code: 'PK', name: 'Pakistan International' },
  { code: '3D', name: 'Palair Macedonian Air' },
  { code: 'PF', name: 'Palestinian Airlines' },
  { code: 'OP', name: 'Pan Am Air Bridge' },
  { code: 'PA', name: 'Pan American' },
  { code: 'P8', name: 'Pantanal Linhas Aereas' },
  { code: 'HI', name: 'Papillon Airways' },
  { code: 'BK', name: 'Paradise Island' },
  { code: 'Y8', name: 'Passadero Trans Aereos' },
  { code: 'PV', name: 'Pauknair' },
  { code: '9P', name: 'Pelangi Air' },
  { code: 'EP', name: 'Pelita Air Service' },
  { code: 'PD', name: 'Pemair' },
  { code: 'KS', name: 'Penair' },
  { code: 'UW', name: 'Perimeter Airlines' },
  { code: 'PR', name: 'Philippine Airlines' },
  { code: 'PW', name: 'Pine State Airlines' },
  { code: 'PU', name: 'Pluna' },
  { code: 'PH', name: 'Polynesian Ltd' },
  { code: 'NI', name: 'Portugalia' },
  { code: 'QG', name: 'Prima Air' },
  { code: 'Z3', name: 'Promech' },
  { code: 'YS', name: 'Proteus' },
  { code: '6S', name: 'Proteus Helicopters' },
  { code: 'AG', name: 'Provincial Airlines' },
  { code: '5P', name: 'Ptarmigan Airways' },
  { code: 'Z8', name: 'Pulkovo Airlines' },
  { code: 'QF', name: 'Qantas Airways Ltd' },
  { code: 'QR', name: 'Qatar Airways' },
  { code: 'QH', name: 'Qwestair' },
  { code: 'RW', name: 'Ras Fluggesellschaft MBH' },
  { code: '7R', name: 'Redwing Airways' },
  { code: 'RX', name: 'Redwing Airways Inc' },
  { code: 'RV', name: 'Reeve Aleutian Airways' },
  { code: 'RL', name: 'Region Air Caribbean Ltd' },
  { code: 'FN', name: 'Regional Air Lines' },
  { code: 'VM', name: 'Regional Airlines' },
  { code: 'XG', name: 'Regional Lineas Aereas' },
  { code: 'R8', name: 'Reguljair' },
  { code: 'QQ', name: 'Reno Air Inc' },
  { code: 'WE', name: 'Rheintalflug Seewald' },
  { code: 'JN', name: 'Rich International Air' },
  { code: 'GV', name: 'Riga Airlines' },
  { code: 'SL', name: 'Rio Sul' },
  { code: '8R', name: 'Rock Air' },
  { code: 'WQ', name: 'Romavia' },
  { code: 'DW', name: 'Rottnest Airlines' },
  { code: 'VJ', name: 'Royal Air Cambodge' },
  { code: 'RR', name: 'Royal Air Force' },
  { code: 'AT', name: 'Royal Air Maroc' },
  { code: 'QN', name: 'Royal Aviation' },
  { code: 'BI', name: 'Royal Brunei' },
  { code: 'RJ', name: 'Royal Jordanian' },
  { code: 'RA', name: 'Royal Nepal Airlines' },
  { code: 'ZC', name: 'Royal Swazi National Air' },
  { code: 'WR', name: 'Royal Tongan Airlines' },
  { code: 'R4', name: 'Russia' },
  { code: '4T', name: 'Russian Airlines' },
  { code: 'FR', name: 'Ryanair' },
  { code: 'ZG', name: 'Sabair Airlines' },
  { code: 'SN', name: 'Sabena' },
  { code: 'S9', name: 'SAE Swe Aviation Europe' },
  { code: 'EH', name: 'Saeta' },
  { code: 'S2', name: 'Sahara India Airlines' },
  { code: '8S', name: 'Salair Inc' },
  { code: 'E5', name: 'Samara Airlines' },
  { code: 'TS', name: 'Samoa Aviation' },
  { code: 'WB', name: 'SAN' },
  { code: 'Q3', name: 'Sandaun Air Services' },
  { code: 'RZ', name: 'Sansa' },
  { code: 'QT', name: 'Sar Avions Taxis' },
  { code: 'N5', name: 'Sardairline Soc Coop Airline' },
  { code: 'SP', name: 'Sata Air Acores' },
  { code: 'SV', name: 'Saudi Arabian Airlines' },
  { code: 'SK', name: 'Scandinavian Airlines' },
  { code: 'YR', name: 'Scenic Airlines' },
  { code: 'ZM', name: 'Scibe Airlift' },
  { code: 'BB', name: 'Seaborne Aviation Inc' },
  { code: 'U9', name: 'Selcon Airlines Ltd' },
  { code: 'SG', name: 'Sempati Air' },
  { code: 'VC', name: 'Servicios Avensa' },
  { code: 'NL', name: 'Shaheen Air' },
  { code: 'SC', name: 'Shandong Airlines' },
  { code: 'FM', name: 'Shanghai Airlines' },
  { code: '8C', name: 'Shanxi Airlines' },
  { code: '4G', name: 'Shenzhen Airlines' },
  { code: 'OB', name: 'Shepparton Airlines' },
  { code: '7Q', name: 'Shorouk Air' },
  { code: '3S', name: 'Shuswap Air' },
  { code: 'DD', name: 'Shuttle Air Cargo' },
  { code: 'TB', name: 'Shuttle Inc' },
  { code: 'S7', name: 'Siberia Airlines' },
  { code: '3U', name: 'Sichuan Airlines' },
  { code: 'LJ', name: 'Sierra National Airlines' },
  { code: 'MI', name: 'Silkair' },
  { code: 'SQ', name: 'Singapore Airlines' },
  { code: '5Q', name: 'Skaergaardsflyget' },
  { code: 'PO', name: 'Skippers Aviation Pty Lt' },
  { code: 'OO', name: 'Sky West Airlines' },
  { code: 'D2', name: 'Skyline Nepc Ltd' },
  { code: 'NP', name: 'Skytrans' },
  { code: 'JZ', name: 'Skyways Ab' },
  { code: 'YT', name: 'Skywest Airlines' },
  { code: '6Q', name: 'Slovak Airlines' },
  { code: 'Q7', name: 'Sobelair' },
  { code: 'MM', name: 'Soc Aero de Medellín' },
  { code: 'IE', name: 'Solomon Airlines' },
  { code: '4Z', name: 'South African Airlink' },
  { code: 'SA', name: 'South African Airways' },
  { code: 'YB', name: 'South African Express' },
  { code: '6J', name: 'Southeast European Air' },
  { code: 'SJ', name: 'Southern Air Transport' },
  { code: 'F2', name: 'Southern Independent Air' },
  { code: 'A4', name: 'Southern Winds' },
  { code: 'WN', name: 'Southwest Airlines Texas' },
  { code: 'S4', name: 'Spain Air Transport' },
  { code: 'JK', name: 'Spanair' },
  { code: 'NK', name: 'Spirit Airlines' },
  { code: '5K', name: 'State Air Company Odessa' },
  { code: '7J', name: 'State Air Company Tajikistan' },
  { code: 'R2', name: 'State Orenburg Avia' },
  { code: 'KV', name: 'State United Venture' },
  { code: 'TX', name: 'Ste Nile Air Guadeloupe' },
  { code: 'PN', name: 'Ste Nile Air Martinique' },
  { code: 'NB', name: 'Sterling Airways' },
  { code: 'CB', name: 'Suckling Airways' },
  { code: 'SD', name: 'Sudan Airways Co Ltd' },
  { code: 'BV', name: 'Sun Air' },
  { code: 'EZ', name: 'Sun Air Of Scandinavia' },
  { code: 'SY', name: 'Sun Country Airlines' },
  { code: 'PI', name: 'Sunflower Airlines' },
  { code: 'SM', name: 'Sumworld Intl Airlines' },
  { code: 'PY', name: 'Surinam Airways' },
  { code: 'SI', name: 'Svea Flug' },
  { code: 'HK', name: 'Swan Airlines' },
  { code: 'HJ', name: 'Swedeways AB' },
  { code: 'W3', name: 'Swiftair SA' },
  { code: 'SO', name: 'Swiss World Airways' },
  { code: 'SR', name: 'Swissair' },
  { code: 'RB', name: 'Syrianair' },
  { code: 'DT', name: 'Taag' },
  { code: 'T2', name: 'Taba Transportes Aereos' },
  { code: 'TA', name: 'Taca Intl Airlines' },
  { code: 'VR', name: 'TACV Cabo Verde Airlines' },
  { code: 'W5', name: 'Tajikistan International' },
  { code: 'JJ', name: 'TAM Meridional' },
  { code: 'KK', name: 'Tam Regional' },
  { code: 'GG', name: 'Tamair' },
  { code: 'EQ', name: 'Tame' },
  { code: '4E', name: 'Tanana Air Service' },
  { code: 'TP', name: 'TAP Air Portugal' },
  { code: 'H7', name: 'Taquan Air Service Inc' },
  { code: 'RO', name: 'Tarom' },
  { code: 'TJ', name: 'TAS Airways' },
  { code: '9X', name: 'Tasawi Air Services Ltd' },
  { code: '3K', name: 'Tatonduk Flying Service' },
  { code: 'QS', name: 'Tatra Air' },
  { code: 'T6', name: 'Tarvey Airompany' },
  { code: 'Z4', name: 'Tayfunair Inc' },
  { code: 'RU', name: 'TCI Skyking Ltd' },
  { code: 'ZJ', name: 'Teddy Air' },
  { code: 'TG', name: 'Thai Airways Intl' },
  { code: '4A', name: 'Thunderbird' },
  { code: 'FC', name: 'Thuringia Airlines' },
  { code: '5B', name: 'Tie Aviation' },
  { code: 'TD', name: 'TNT Sava' },
  { code: 'B6', name: 'Top Air Havacilik Sanayi' },
  { code: 'FF', name: 'Tower Air Inc' },
  { code: 'PZ', name: 'Tra.Aereos del Mercosur' },
  { code: 'YZ', name: 'Trans Aer Guine Bissau' },
  { code: 'GD', name: 'Trans Aereos Ejecutivos' },
  { code: 'P6', name: 'Trans Air' },
  { code: 'Y9', name: 'Trans Air Congo' },
  { code: '7T', name: 'Trans Cote' },
  { code: 'JQ', name: 'Trans Jamaican Airlines' },
  { code: '4Q', name: 'Trans North Aviation Ltd' },
  { code: 'DN', name: 'Trans Pacific Airlines' },
  { code: '9N', name: 'Trans State Airlines' },
  { code: '6N', name: 'Trans Travel Airlines' },
  { code: 'TW', name: 'Trans World Airlines Inc' },
  { code: 'UN', name: 'Transaero Airlines' },
  { code: 'GE', name: 'Transasia Airways' },
  { code: 'HV', name: 'Transavia airlines' },
  { code: 'TR', name: 'Transbrasil' },
  { code: 'DZ', name: 'Transcarabies Air Intl' },
  { code: 'T4', name: 'Transeast Airlines' },
  { code: 'UE', name: 'Transeuropean Airlines' },
  { code: '4P', name: 'Transportes aereos' },
  { code: 'T8', name: 'Transportes Aereos Neuqu' },
  { code: 'BH', name: 'Transstate Airlines' },
  { code: 'OF', name: 'Trav Aeriens Madagascar' },
  { code: 'U3', name: 'Travelair' },
  { code: 'PM', name: 'Tropic Air' },
  { code: 'VF', name: 'Tropical Airlines' },
  { code: 'UG', name: 'Tuninter' },
  { code: 'TU', name: 'Tunis Air' },
  { code: '3T', name: 'Turan Air Airline Company' },
  { code: 'UF', name: 'Turkestan Airlines' },
  { code: 'TK', name: 'Turkish Airlines' },
  { code: 'QW', name: 'Turks and Caicos Airways' },
  { code: 'P2', name: 'Tymen Air Carrier' },
  { code: 'VO', name: 'Tyrolean Airways' },
  { code: '7M', name: 'Tyumen Airlines' },
  { code: 'WI', name: 'U Land Airlines' },
  { code: 'QU', name: 'Uganda Airlines' },
  { code: 'PS', name: 'Ukraine Intl Airlines' },
  { code: 'B7', name: 'Uni Airways Corp' },
  { code: 'UA', name: 'United Airlines' },
  { code: 'U7', name: 'United Aviation' },
  { code: 'DH', name: 'United Express' },
  { code: 'UZ', name: 'UP Air' },
  { code: 'U6', name: 'Ural Airlines' },
  { code: 'US', name: 'US Airways' },
  { code: 'HY', name: 'Uzbekistan Airways' },
  { code: 'V2', name: 'Valdrestly AS' },
  { code: 'J7', name: 'Valujet Airlines' },
  { code: 'V3', name: 'Vanair Limited' },
  { code: 'FT', name: 'Vancouver Island Air Ltd' },
  { code: 'NJ', name: 'Vanguard Airlines Inc' },
  { code: 'RG', name: 'Varig' },
  { code: 'VP', name: 'Vaspbrazilian Airlines' },
  { code: 'V4', name: 'Venus Airlines' },
  { code: 'VA', name: 'Viasa Venezolana' },
  { code: 'VI', name: 'Vieques Air Link' },
  { code: 'VN', name: 'Vietnam Airlines' },
  { code: '9V', name: 'VIP Air' },
  { code: 'VS', name: 'Virgin Atlantic' },
  { code: 'BQ', name: 'Virgin Express' },
  { code: 'S5', name: 'Virgin Islands Airways' },
  { code: '5V', name: 'Vistajet' },
  { code: 'VG', name: 'VLM Vlaamse' },
  { code: 'V5', name: 'Vnukovo Airlines' },
  { code: '8D', name: 'Volare Airlines' },
  { code: 'G6', name: 'Volga Airlines' },
  { code: '4V', name: 'Voyageur Airways' },
  { code: '4W', name: 'Warbelows Air Ventures' },
  { code: 'WG', name: 'Wasaya Airways Ltd' },
  { code: 'KY', name: 'Waterwings Airways' },
  { code: 'PT', name: 'West Air Sweden' },
  { code: '8O', name: 'West Coast Air' },
  { code: '7Y', name: 'West Isle Air' },
  { code: 'OE', name: 'Westair Commuter Airline' },
  { code: 'EM', name: 'Western Airlines' },
  { code: '2U', name: 'Western Pacific Air' },
  { code: 'W7', name: 'Western Pacific Airlines' },
  { code: 'WW', name: 'Whyalla Airlines' },
  { code: 'WF', name: 'Wideroe' },
  { code: '6W', name: 'Wilderness Airlines' },
  { code: 'WM', name: 'Windward Island Airways' },
  { code: 'SE', name: 'Wings of Alaska' },
  { code: 'WO', name: 'World Airways' },
  { code: '8V', name: 'Wright Air Services' },
  { code: 'MF', name: 'Xiamen Airlines' },
  { code: 'XO', name: 'Xinjiang Airlines' },
  { code: 'ST', name: 'Yanda Airlines' },
  { code: 'IY', name: 'Yemenia' },
  { code: 'JU', name: 'Yugoslav Airlines' },
  { code: '4Y', name: 'Yute Air Alaska' },
  { code: 'OQ', name: 'Zambian Express Airways' },
  { code: 'ZA', name: 'ZAS Airline of Egypt' },
  { code: 'Z7', name: 'Zimbabwe Express Air' },
  { code: 'OD', name: 'Zuliana de Aviaci' },
  { code: 'ZZ', name: 'Buzz' }
];

export default function AirlinesList() {
  return (
    <div className="p-4 bg-gray-100 min-h-screen">
      <h1 className="text-2xl font-bold mb-4">Airlines List</h1>
      <ul className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {airlines.map((airline) => (
          <li key={airline.code} className="bg-white p-4 rounded-xl shadow-md hover:shadow-lg transition">
            <span className="font-semibold">{airline.code}</span> - {airline.name}
          </li>
        ))}
      </ul>
    </div>
  );
}