// homeApi.js
import axios from 'axios';
import useSWR from 'swr';

const fetcher = async (url, requestBody) => {
  const response = await axios.post(url, requestBody);
  return response.data;
};

const API_URL = 'https://backend.graycorp.io:9603/efly/api/hotelBooking/getSavedAvailableHotelDetailsByHotelId';

export const useHotelDetails = (requestBody) => {
  // Serialize requestBody into the key for uniqueness
  const key = requestBody ? [API_URL, JSON.stringify(requestBody)] : null;
  
  const { data, error } = useSWR(key, ([url, bodyStr]) => {
    // Parse the stringified body back to an object
    const body = JSON.parse(bodyStr);
    return fetcher(url, body);
  });
  
  return {
    hotels: data,
    isLoading: requestBody && !error && !data,
    isError: error,
  };
};