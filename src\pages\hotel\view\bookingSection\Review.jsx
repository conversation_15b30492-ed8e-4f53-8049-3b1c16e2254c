import React from "react";
import PeopleReview from "./PeopleReview";
import { ChevronRight, Star } from "lucide-react";

const reviewData = [
  { label: "Price Performance", value: 4 },
  { label: "Position", value: 5 },
  { label: "Room", value: 4 },
  { label: "Cleanliness", value: 5 },
  { label: "Service", value: 5 },
  { label: "Sleep Quality", value: 4 },
];

const reviews = [
  {
    url: "https://i.pravatar.cc/150?img=4",
    name: "<PERSON><PERSON> <PERSON>",
    date: "21 Jan 2023",
    rating: 4,
    topic: "Good customer service",
    review: `wander through ancient 
temples, indulge in the local 
dishes or explore the popular
bazaars.`,
    reviewCount: [
      { label: "Price Review", value: 4 },
      { label: "Cleanliness", value: 4 },
      { label: "Position", value: 4 },
      { label: "Service", value: 4 },
      { label: "Room", value: 4 },
      { label: "Sleep Quality", value: 4 },
    ],
  },
  {
    url: "https://i.pravatar.cc/150?img=4",
    name: "<PERSON><PERSON>",
    date: "21 Jan 2023",
    rating: 4,
    topic: "Good customer service",
    review: `wander through ancient 
temples, indulge in the local 
dishes or explore the popular
bazaars.`,
    reviewCount: [
      { label: "Price Review", value: 4 },
      { label: "Cleanliness", value: 4 },
      { label: "Position", value: 4 },
      { label: "Service", value: 4 },
      { label: "Room", value: 4 },
      { label: "Sleep Quality", value: 4 },
    ],
  },
  {
    url: "https://i.pravatar.cc/150?img=4",
    name: "Aisha Kelly",
    date: "21 Jan 2023",
    rating: 4,
    topic: "Good customer service",
    review: `wander through ancient 
temples, indulge in the local 
dishes or explore the popular
bazaars.`,
    reviewCount: [
      { label: "Price Review", value: 4 },
      { label: "Cleanliness", value: 4 },
      { label: "Position", value: 4 },
      { label: "Service", value: 4 },
      { label: "Room", value: 4 },
      { label: "Sleep Quality", value: 4 },
    ],
  },
  {
    url: "https://i.pravatar.cc/150?img=4",
    name: "Brylee Singleton",
    date: "21 Jan 2023",
    rating: 4,
    topic: "Good customer service",
    review: `wander through ancient 
temples, indulge in the local 
dishes or explore the popular
bazaars.`,
    reviewCount: [
      { label: "Price Review", value: 5 },
      { label: "Cleanliness", value: 4 },
      { label: "Position", value: 3 },
      { label: "Service", value: 4 },
      { label: "Room", value: 4 },
      { label: "Sleep Quality", value: 1 },
    ],
  },
  {
    url: "https://i.pravatar.cc/150?img=4",
    name: "Aisha Kelly",
    date: "21 Jan 2023",
    rating: 4,
    topic: "Good customer service",
    review: `wander through ancient 
temples, indulge in the local 
dishes or explore the popular
bazaars.`,
    reviewCount: [
      { label: "Price Review", value: 4 },
      { label: "Cleanliness", value: 4 },
      { label: "Position", value: 4 },
      { label: "Service", value: 4 },
      { label: "Room", value: 4 },
      { label: "Sleep Quality", value: 4 },
    ],
  },
  {
    url: "https://i.pravatar.cc/150?img=4",
    name: "Aisha Kelly",
    date: "21 Jan 2023",
    rating: 4,
    topic: "Good customer service",
    review: `wander through ancient 
temples, indulge in the local 
dishes or explore the popular
bazaars.`,
    reviewCount: [
      { label: "Price Review", value: 4 },
      { label: "Cleanliness", value: 4 },
      { label: "Position", value: 4 },
      { label: "Service", value: 4 },
      { label: "Room", value: 4 },
      { label: "Sleep Quality", value: 4 },
    ],
  },
];

const Review = () => {
  return (
    <div className="w-full flex flex-col space-y-10 md:space-y-16 px-6 py-3 md:px-0 md:py-0">
      <h2 className="text-2xl text-darkBlue font-semibold">Feedback and Review </h2>
      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
        {reviewData?.map((review, index) => (
          <div
            key={index}
            className="px-3 py-2 rounded-lg md:text-sm text-xs transition-colors border border-borderGray w-full flex justify-between items-center"
          >
            <span>{review.label}</span>
            <div className="flex flex-row space-x-0.5 md:space-x-1 items-center">
              {[...Array(5)].map((_, starIdx) => (
                <Star
                  key={starIdx}
                  size={starIdx < review.value ? 20 : 16}
                  className={`transition-all duration-300 transform stroke-orange 
                     ${starIdx < review.value ? "fill-orange scale-110" : "fill-white"}  w-[9px] h-3 md:w-4 md:h-5`}
                />
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Reviews Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <PeopleReview reviews={reviews} />
      </div>
    </div>

  );
};

export default Review;
