import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { toast } from 'react-toastify';

const downloadPDF = (bookingDetails, voucherDetails) => {
  try {
    // Validate input parameters
    if (!bookingDetails && !voucherDetails) {
      throw new Error('No booking or voucher details provided');
    }

    // Use fallback data if voucherDetails is not provided
    const safeVoucherDetails = voucherDetails || {};
    const safeBookingDetails = bookingDetails || {};

    const doc = new jsPDF();

    // Set document properties
    doc.setProperties({
      title: `Booking Confirmation ${safeVoucherDetails.bookingReferenceNo || 'HTL-WBD-781230483'}`,
      author: 'EFly AG',
      creator: 'EFly Booking System',
    });

    // Branding: Header
    doc.setFont('helvetica', 'bold');
    doc.setFontSize(18);
    doc.setTextColor(0, 51, 102); // Dark blue
    doc.text('Booking Confirmation', 105, 20, { align: 'center' });
    doc.setFontSize(10);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100); // Smoky gray
    doc.text(
      `Generated by EFly AG on ${safeVoucherDetails.printedOn || new Date().toLocaleDateString('en-US')}`,
      105,
      28,
      { align: 'center' }
    );
    // Placeholder for logo (uncomment if logo available)
    // doc.addImage('path/to/efly-logo.png', 'PNG', 20, 10, 30, 15);
    doc.line(20, 32, 190, 32); // Horizontal line

    // Voucher Info Table
    // doc.setFontSize(12);
    // doc.setFont('helvetica', 'bold');
    // doc.setTextColor(0, 51, 102);
    // doc.text('Voucher Info', 20, 40);
    // autoTable(doc, {
    //   startY: 45,
    //   head: [['Field', 'Details']],
    //   body: safeVoucherDetails.voucherInfo?.map((item) => [item.label, item.value]) || [
    //     ['Booking Reference No', 'N/A'],
    //     ['Printed On', new Date().toLocaleDateString('en-US')],
    //     ['Itinerary Number', 'N/A'],
    //     ['Booked By', 'EFly AG'],
    //     ['Booking Status', 'Confirmed'],
    //   ],
    //   theme: 'striped',
    //   headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
    //   bodyStyles: { fontSize: 9, textColor: [75, 75, 75] },
    //   margin: { left: 20, right: 20 },
    //   columnStyles: { 0: { cellWidth: 60 }, 1: { cellWidth: 110 } },
    // });

    // Service Provider Details Table
    let finalY = doc.lastAutoTable.finalY + 10;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 51, 102);
    doc.text('Service Provider Details', 20, finalY);
    autoTable(doc, {
      startY: finalY + 5,
      head: [['Field', 'Details']],
      body: safeVoucherDetails.serviceProviderInfo?.map((item) => [item.label, item.value]) || [
        ['Supplier Name', 'N/A'],
        ['Service Name', 'N/A'],
        ['Address', 'N/A'],
        ['Telephone', 'N/A'],
      ],
      theme: 'striped',
      headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
      bodyStyles: { fontSize: 9, textColor: [75, 75, 75] },
      margin: { left: 20, right: 20 },
      columnStyles: { 0: { cellWidth: 60 }, 1: { cellWidth: 110 } },
    });

    // Guest Details Table
    finalY = doc.lastAutoTable.finalY + 10;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 51, 102);
    doc.text('Guest Details', 20, finalY);
    autoTable(doc, {
      startY: finalY + 5,
      head: [['Field', 'Details']],
      body: safeVoucherDetails.passengerInfo?.map((item) => [item.label, item.value]) || [
        ['Guest Name', 'N/A'],
        ['Guest Email Address', 'N/A'],
        ['Guest Phone Number', 'N/A'],
        ['Guest Nationality', 'N/A'],
        ['Country of Residence', 'N/A'],
        ['Service Type', 'Accommodation'],
        ['City', 'N/A'],
        ['Supplier Reference', 'N/A'],
        ['Check-in', 'N/A'],
        ['Check-out', 'N/A'],
        ['Room Type', 'N/A'],
        ['Room Occupancy', 'N/A'],
        ['Rate Basis', 'N/A'],
        ['Additional Requests', 'none'],
      ],
      theme: 'striped',
      headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
      bodyStyles: { fontSize: 9, textColor: [75, 75, 75] },
      margin: { left: 20, right: 20 },
      columnStyles: { 0: { cellWidth: 60 }, 1: { cellWidth: 110 } },
    });

    // Daily Rates Table
    finalY = doc.lastAutoTable.finalY + 10;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 51, 102);
    doc.text('Daily Rates', 20, finalY);
    const dailyRatesBody = safeVoucherDetails.dailyRates?.map((rate) => [
      rate.date,
      `${rate.payableRate} ${safeVoucherDetails.currency || 'CHF'}`,
      rate.rateBasis,
      rate.rateMarket,
    ]) || [['N/A', 'N/A', 'N/A', 'N/A']];
    autoTable(doc, {
      startY: finalY + 5,
      head: [['Date', 'Payable Rate', 'Rate Basis', 'Rate Market']],
      body: dailyRatesBody,
      theme: 'striped',
      headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
      bodyStyles: { fontSize: 9, textColor: [75, 75, 75] },
      margin: { left: 20, right: 20 },
      columnStyles: {
        0: { cellWidth: 40 },
        1: { cellWidth: 40 },
        2: { cellWidth: 40 },
        3: { cellWidth: 50 },
      },
      didDrawPage: () => {
        finalY = doc.lastAutoTable.finalY + 5;
        doc.setFontSize(9);
        doc.setFont('helvetica', 'normal');
        doc.setTextColor(75, 75, 75);
        doc.text(
          'Rates include all Room taxes and Service charges and may not include City Taxes. Note that rates shown may be average daily rates.',
          20,
          finalY,
          { maxWidth: 170 }
        );
      },
    });

    // Total Payable Table
    finalY = doc.lastAutoTable.finalY + 15;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 51, 102);
    doc.text('Total Payable', 20, finalY);
    autoTable(doc, {
      startY: finalY + 5,
      head: [['Description', 'Amount']],
      body: [
        [
          'Total Payable for this Booking',
          `${safeVoucherDetails.totalPayable || 'N/A'} ${safeVoucherDetails.currency || 'CHF'}`,
        ],
      ],
      theme: 'striped',
      headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
      bodyStyles: { fontSize: 9, textColor: [75, 75, 75], fontStyle: 'bold' },
      margin: { left: 20, right: 20 },
      columnStyles: { 0: { cellWidth: 110 }, 1: { cellWidth: 60 } },
    });

    // Cancellation Policy Table
    finalY = doc.lastAutoTable.finalY + 10;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 51, 102);
    doc.text('Cancellation Policy', 20, finalY);
    autoTable(doc, {
      startY: finalY + 5,
      head: [['Policy']],
      body: safeVoucherDetails.cancellationRules?.map((rule) => [
        rule.subText ? `${rule.text}\n${rule.subText}` : rule.text,
      ]) || [['No cancellation policy available.']],
      theme: 'striped',
      headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
      bodyStyles: { fontSize: 9, textColor: [75, 75, 75] },
      margin: { left: 20, right: 20 },
      columnStyles: { 0: { cellWidth: 170 } },
    });

    // Tariff Notes Table
    finalY = doc.lastAutoTable.finalY + 10;
    doc.setFontSize(12);
    doc.setFont('helvetica', 'bold');
    doc.setTextColor(0, 51, 102);
    doc.text('Tariff Notes', 20, finalY);
    autoTable(doc, {
      startY: finalY + 5,
      head: [['Notes']],
      body: [[safeVoucherDetails.tariffNotes || 'No additional notes.']],
      theme: 'striped',
      headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
      bodyStyles: { fontSize: 9, textColor: [75, 75, 75] },
      margin: { left: 20, right: 20 },
      columnStyles: { 0: { cellWidth: 170 } },
    });

    // Additional Notes from Itinerary Response (if applicable)
    if (safeBookingDetails.confirmationDetails?.checkInInstructions) {
      finalY = doc.lastAutoTable.finalY + 10;
      doc.setFontSize(12);
      doc.setFont('helvetica', 'bold');
      doc.setTextColor(0, 51, 102);
      doc.text('Check-In Instructions', 20, finalY);
      autoTable(doc, {
        startY: finalY + 5,
        head: [['Instructions']],
        body: [[safeBookingDetails.confirmationDetails.checkInInstructions || 'Contact property for details.']],
        theme: 'striped',
        headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 10 },
        bodyStyles: { fontSize: 9, textColor: [75, 75, 75] },
        margin: { left: 20, right: 20 },
        columnStyles: { 0: { cellWidth: 170 } },
      });
    }

    // Footer
    finalY = doc.lastAutoTable.finalY + 15;
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100);
    doc.text(
      'Generated by EFly AG | Longstrasse 214, CH 8005 Zurich | <EMAIL>',
      105,
      290,
      { align: 'center' }
    );

    // Save the PDF
    doc.save(`Booking_Confirmation_${safeVoucherDetails.bookingReferenceNo || 'HTL-WBD-781230483'}.pdf`);
    toast.success('PDF downloaded successfully!');
  } catch (error) {
    console.error('PDF generation error:', error);
    toast.error('Failed to generate PDF. Please try again.');
  }
};

export { downloadPDF };