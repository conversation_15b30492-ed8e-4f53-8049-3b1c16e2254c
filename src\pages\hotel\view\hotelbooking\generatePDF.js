import jsPDF from 'jspdf';
import autoTable from 'jspdf-autotable';
import { toast } from 'react-toastify';

// Helper function to parse XHTML and extract text content
const parseXHTMLContent = (htmlString) => {
  if (!htmlString) return [];

  try {
    // Create a temporary DOM element to parse HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(htmlString, 'text/html');

    const content = [];

    // Extract text content from various elements
    const extractTextFromElement = (element, level = 0) => {
      const tagName = element.tagName?.toLowerCase();
      const text = element.textContent?.trim();

      if (!text) return;

      // Handle different HTML elements
      switch (tagName) {
        case 'h1':
        case 'h2':
        case 'h3':
          content.push({ type: 'heading', text, level: parseInt(tagName.charAt(1)) });
          break;
        case 'p':
          content.push({ type: 'paragraph', text });
          break;
        case 'table':
          // Extract table data
          const rows = element.querySelectorAll('tr');
          const tableData = [];
          rows.forEach(row => {
            const cells = row.querySelectorAll('td, th');
            const rowData = Array.from(cells).map(cell => cell.textContent?.trim() || '');
            if (rowData.length > 0) tableData.push(rowData);
          });
          if (tableData.length > 0) {
            content.push({ type: 'table', data: tableData });
          }
          break;
        case 'ul':
        case 'ol':
          const listItems = element.querySelectorAll('li');
          listItems.forEach(li => {
            content.push({ type: 'list', text: li.textContent?.trim() || '' });
          });
          break;
        default:
          if (text && !['script', 'style', 'meta', 'link'].includes(tagName)) {
            content.push({ type: 'text', text });
          }
      }
    };

    // Process all elements in the body
    const body = doc.body || doc;
    const walker = document.createTreeWalker(
      body,
      NodeFilter.SHOW_ELEMENT,
      null,
      false
    );

    let node;
    while (node = walker.nextNode()) {
      extractTextFromElement(node);
    }

    return content;
  } catch (error) {
    console.error('Error parsing XHTML:', error);
    // Fallback: extract text using regex and simple parsing
    const cleanText = htmlString
      .replace(/<script[^>]*>.*?<\/script>/gi, '') // Remove scripts
      .replace(/<style[^>]*>.*?<\/style>/gi, '') // Remove styles
      .replace(/<[^>]*>/g, ' ') // Remove all HTML tags
      .replace(/\s+/g, ' ') // Replace multiple spaces with single space
      .trim();

    if (cleanText) {
      // Split into paragraphs based on common patterns
      const paragraphs = cleanText
        .split(/\n\n|\r\n\r\n/)
        .filter(p => p.trim().length > 0)
        .map(p => ({ type: 'paragraph', text: p.trim() }));

      return paragraphs.length > 0 ? paragraphs : [{ type: 'text', text: cleanText }];
    }

    return [];
  }
};

// Alternative simple HTML parser for better compatibility
const parseHTMLSimple = (htmlString) => {
  if (!htmlString) return [];

  const content = [];

  // Extract tables
  const tableRegex = /<table[^>]*>(.*?)<\/table>/gis;
  let tableMatch;
  while ((tableMatch = tableRegex.exec(htmlString)) !== null) {
    const tableContent = tableMatch[1];
    const rows = tableContent.match(/<tr[^>]*>(.*?)<\/tr>/gis) || [];
    const tableData = rows.map(row => {
      const cells = row.match(/<t[hd][^>]*>(.*?)<\/t[hd]>/gis) || [];
      return cells.map(cell => cell.replace(/<[^>]*>/g, '').trim());
    }).filter(row => row.length > 0);

    if (tableData.length > 0) {
      content.push({ type: 'table', data: tableData });
    }
  }

  // Extract headings
  const headingRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gis;
  let headingMatch;
  while ((headingMatch = headingRegex.exec(htmlString)) !== null) {
    content.push({
      type: 'heading',
      text: headingMatch[2].replace(/<[^>]*>/g, '').trim(),
      level: parseInt(headingMatch[1])
    });
  }

  // Extract paragraphs
  const paragraphRegex = /<p[^>]*>(.*?)<\/p>/gis;
  let paragraphMatch;
  while ((paragraphMatch = paragraphRegex.exec(htmlString)) !== null) {
    const text = paragraphMatch[1].replace(/<[^>]*>/g, '').trim();
    if (text) {
      content.push({ type: 'paragraph', text });
    }
  }

  // If no structured content found, extract all text
  if (content.length === 0) {
    const allText = htmlString
      .replace(/<script[^>]*>.*?<\/script>/gis, '')
      .replace(/<style[^>]*>.*?<\/style>/gis, '')
      .replace(/<[^>]*>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();

    if (allText) {
      content.push({ type: 'text', text: allText });
    }
  }

  return content;
};

const downloadPDF = (bookingDetails, voucherDetails, htmlContent) => {
  try {
    // Validate input parameters
    if (!bookingDetails && !voucherDetails) {
      throw new Error('No booking or voucher details provided');
    }

    // Debug: Log inputs
    console.log('generatePDF - bookingDetails:', bookingDetails);
    console.log('generatePDF - voucherDetails:', voucherDetails);
    console.log('generatePDF - htmlContent:', htmlContent);

    // Use fallback data
    const safeVoucherDetails = voucherDetails || {};
    const safeBookingDetails = bookingDetails || {};

    const doc = new jsPDF();

    // Set document properties
    doc.setProperties({
      title: `Booking Confirmation ${safeVoucherDetails.bookingReferenceNo || 'HTL-WBD-781230483'}`,
      author: 'EFly AG',
      creator: 'EFly Booking System',
    });

    // ... rest of the header and tables unchanged ...

    // XHTML Content from API Response
    if (htmlContent || safeBookingDetails.html || safeBookingDetails.confirmationDetails?.html) {
      const xhtmlContent = htmlContent || safeBookingDetails.html || safeBookingDetails.confirmationDetails?.html;

      // Debug logging
      console.log('generatePDF - XHTML Content:', xhtmlContent);

      // Try the advanced parser first, fallback to simple parser
      let parsedContent = parseXHTMLContent(xhtmlContent);
      if (parsedContent.length === 0) {
        parsedContent = parseHTMLSimple(xhtmlContent);
      }

      // If parsing fails, treat as plain text
      if (parsedContent.length === 0 && xhtmlContent) {
        parsedContent = [{ type: 'text', text: xhtmlContent }];
      }

      console.log('generatePDF - Parsed Content:', parsedContent);

      if (parsedContent.length > 0) {
        finalY = (doc.lastAutoTable?.finalY || 50) + 15;

        if (finalY > 250) {
          doc.addPage();
          finalY = 20;
        }

        doc.setFontSize(12);
        doc.setFont('helvetica', 'bold');
        doc.setTextColor(0, 51, 102);
        doc.text('Booking Confirmation Details', 20, finalY);
        finalY += 10;

        parsedContent.forEach((item) => {
          if (finalY > 270) {
            doc.addPage();
            finalY = 20;
          }

          switch (item.type) {
            case 'heading':
              doc.setFontSize(item.level === 1 ? 14 : item.level === 2 ? 12 : 10);
              doc.setFont('helvetica', 'bold');
              doc.setTextColor(0, 51, 102);
              doc.text(item.text, 20, finalY);
              finalY += item.level === 1 ? 8 : 6;
              break;

            case 'paragraph':
            case 'text':
              doc.setFontSize(9);
              doc.setFont('helvetica', 'normal');
              doc.setTextColor(75, 75, 75);
              const lines = doc.splitTextToSize(item.text, 170);
              lines.forEach(line => {
                if (finalY > 280) {
                  doc.addPage();
                  finalY = 20;
                }
                doc.text(line, 20, finalY);
                finalY += 4;
              });
              finalY += 2;
              break;

            case 'table':
              if (item.data && item.data.length > 0) {
                autoTable(doc, {
                  startY: finalY,
                  head: item.data.length > 1 ? [item.data[0]] : [],
                  body: item.data.length > 1 ? item.data.slice(1) : item.data,
                  theme: 'striped',
                  headStyles: { fillColor: [0, 51, 102], textColor: [255, 255, 255], fontSize: 9 },
                  bodyStyles: { fontSize: 8, textColor: [75, 75, 75] },
                  margin: { left: 20, right: 20 },
                  styles: { cellPadding: 2 },
                });
                finalY = (doc.lastAutoTable?.finalY || finalY) + 5;
              }
              break;

            case 'list':
              doc.setFontSize(9);
              doc.setFont('helvetica', 'normal');
              doc.setTextColor(75, 75, 75);
              doc.text(`• ${item.text}`, 25, finalY);
              finalY += 4;
              break;
          }
        });

        finalY += 10;
      }
    }

    // Footer
    finalY = Math.max(finalY, doc.lastAutoTable?.finalY + 15 || finalY);
    doc.setFontSize(8);
    doc.setFont('helvetica', 'normal');
    doc.setTextColor(100);
    doc.text(
      'Generated by EFly AG | Longstrasse 214, CH 8005 Zurich | <EMAIL>',
      105,
      290,
      { align: 'center' }
    );

    // Save the PDF
    doc.save(`Booking_Confirmation_${safeVoucherDetails.bookingReferenceNo || 'HTL-WBD-781230483'}.pdf`);
    toast.success('PDF downloaded successfully!');
  } catch (error) {
    console.error('PDF generation error:', error);
    toast.error('Failed to generate PDF. Please try again.');
  }
};

export { downloadPDF };