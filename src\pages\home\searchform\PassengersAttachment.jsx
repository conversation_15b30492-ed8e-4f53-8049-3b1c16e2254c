import { Minus, Plus } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";

const data = [
  { label: "Adult", field: "adults", age: "Ages 12+", max: 6 },
  { label: "Child", field: "children", age: "Ages 2-11", max: 6 },
  { label: "Baby", field: "babies", age: "Ages Under 2", max: 1 },
];

const PassengersAttachment = ({ values, setFieldValue, onClose }) => {
  const modalRef = useRef(null);
  const maxPassengers = 9;

  // Create local state to track temporary changes
  const [tempValues, setTempValues] = useState({
    adults: values.adults,
    children: values.children,
    babies: values.babies,
  });

  const handleIncrement = (field) => {
    const total = tempValues.adults + tempValues.children + tempValues.babies;
    if (total < maxPassengers) {
      setTempValues({
        ...tempValues,
        [field]: tempValues[field] + 1,
      });
    }
  };

  const handleDecrement = (field) => {
    if (field === "adults" && tempValues[field] > 1) {
      setTempValues({
        ...tempValues,
        [field]: tempValues[field] - 1,
      }); // Ensure adults never go below 1
    } else if (field !== "adults" && tempValues[field] > 0) {
      setTempValues({
        ...tempValues,
        [field]: tempValues[field] - 1,
      }); // Other fields can go to 0
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  const handleConfirm = () => {
    console.log("Confirming:", tempValues);
    setFieldValue("adults", tempValues.adults);
    setFieldValue("children", tempValues.children);
    setFieldValue("babies", tempValues.babies);
    onClose();
  };

  // Function to check if decrement button should be disabled
  const isDecrementDisabled = (field) => {
    if (field === "adults") {
      return tempValues.adults <= 1; // Adults can't go below 1
    } else {
      return tempValues[field] <= 0; // Other passengers can't go below 0
    }
  };

  // Function to check if increment button should be disabled
  const isIncrementDisabled = (field, maxFieldValue) => {
    const total = tempValues.adults + tempValues.children + tempValues.babies;

    // Check if we've reached total max passengers
    if (total >= maxPassengers) {
      return true;
    }

    // Check individual passenger type limits
    if (field === "adults" && tempValues.adults >= maxFieldValue) {
      return true;
    } else if (field === "children" && tempValues.children >= maxFieldValue) {
      return true;
    } else if (field === "babies" && tempValues.babies >= maxFieldValue) {
      return true;
    }

    return false;
  };

  return (
    <div
      className="absolute z-20 w-full bg-white px-8 py-4 rounded-[2px] text-smokyGray left-auto mt-14 border border-border"
      ref={modalRef}
    >
      <div className="flex flex-col space-y-4">
        <span className="flex justify-center text-base text-black border-b border-border pb-4">
          Passengers
        </span>

        {data.map(({ label, field, age, max }) => (
          <div key={field} className="">
            <div className="flex items-center justify-between">
              <button
                type="button"
                onClick={() => handleDecrement(field)}
                disabled={isDecrementDisabled(field)}
                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[2px] ${
                  isDecrementDisabled(field) ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : " "
                }`}
              >
                <Minus className=" w-3 h-3" />
              </button>

              <div className="text-center">
                <span className="text-sm font-light text-darkGray">
                  {tempValues[field]} {label}
                </span>
                <div className="text-smokyGray text-[10px] font-light">{age}</div>
              </div>

              <button
                type="button"
                onClick={() => handleIncrement(field)}
                disabled={isIncrementDisabled(field, max)}
                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[2px]${
                  isIncrementDisabled(field, max) ? " cursor-not-allowed bg-opacity-60 text-smokyGray/60" : ""
                }`}
              >
                 <Plus className=" w-3 h-3" />
              </button>
            </div>
          </div>
        ))}
        <p className="text-[10px] flex justify-center text-center text-smokyGray font-light">
        Book for up to {maxPassengers} passengers.
        </p>

        <button
          type="button"
          onClick={handleConfirm}
          className="w-full bg-darkBlue text-base font-light text-white py-2 rounded-[2px]"
        >
          Confirm
        </button>
      </div>
    </div>
  );
};

export default PassengersAttachment;
