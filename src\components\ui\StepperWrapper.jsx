import React from 'react';
import { Stepper, Step, StepLabel, StepConnector } from '@mui/material';
import { ArrowLeft } from 'lucide-react';

// Custom Step Icon
const CustomStepIcon = ({ active, completed, icon }) => {
  const baseStyle = {
    width: 32,
    height: 32,
    borderRadius: '50%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    fontSize: '16px',
  };

  if (completed) {
    return (
      <div style={{ ...baseStyle, backgroundColor: '#024575', color: 'white', border: '2px solid #024575' }}>
        {icon}
      </div>
    );
  } else if (active) {
    return (
      <div style={{ ...baseStyle, border: '4px solid #024575', color: '#024575' }}>
        {icon}
      </div>
    );
  } else {
    return (
      <div style={{ ...baseStyle, border: '2px solid #024575', color: '#9CA3AF' }}>
        {icon}
      </div>
    );
  }
};

// Stepper Wrapper
const StepperWrapper = ({ steps, currentStep, onBack, children }) => {
  return (
    <div className="w-full flex justify-center relative mt-6 px-4 sm:px-6 md:px-8">
      <div className="w-full max-w-[100%] md:max-w-[90%] lg:max-w-[85%] flex flex-col space-y-6">
        {/* Back Button */}
        {onBack && (
          <button
            onClick={onBack}
            className="flex items-center text-sm text-smokyGray hover:text-darkBlue transition-colors w-fit ml-0 md:ml-4"
          >
            <ArrowLeft className="w-5 h-5 mr-1" />
            <span className="text-sm font-medium">Back</span>
          </button>
        )}
        {/* Stepper */}
        <Stepper
          alternativeLabel
          activeStep={currentStep - 1}
          connector={
            <StepConnector
              sx={{
                '& .MuiStepConnector-line': {
                  borderColor: '#024575',
                  borderTopWidth: 2,
                },
              }}
            />
          }
        >
          {steps.map((step) => (
            <Step key={step.number}>
              <StepLabel
                StepIconComponent={(props) => <CustomStepIcon {...props} icon={null} />}
                sx={{
                  '& .MuiStepLabel-label': {
                    color: currentStep >= step.number ? '#024575' : '#9CA3AF',
                    fontSize: {
                      xs: '0.75rem', // text-sm
                      sm: '0.875rem', // text-base
                      md: '1rem', // text-md
                    },
                    marginTop: {
                      xs: 1,
                      sm: 1.5,
                      md: 2,
                    },
                    textAlign: 'center',
                    fontWeight: 500,
                  },
                }}
              >
                {step.title}
              </StepLabel>
            </Step>
          ))}
        </Stepper>
        {/* Content Slot */}
        {children}
      </div>
    </div>
  );
};

export default StepperWrapper;
