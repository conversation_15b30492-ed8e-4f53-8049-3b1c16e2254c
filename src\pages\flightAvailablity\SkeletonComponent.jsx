import Skeleton from '@mui/material/Skeleton';
import React from 'react';
import LoadingPage from './LoadingPage';
import LoadingPage2 from './LoadingPage2';

const SkeletonComponent = () => {
    return (
        <div className="flex flex-col space-y-10 w-full relative lg:space-x-10">
        {/* Centered Loading Page */}
        <div className="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
          <div className="w-[1000px] h-[700px] bg-white rounded-lg shadow-xl overflow-hidden scale-75 ml-5">
            <LoadingPage2 />
          </div>
        </div>
        
        {/* Header Skeleton */}
        <div className="flex flex-row justify-between px-4 mt-8">
          <Skeleton variant="text" width={300} height={40} animation="wave" />
          <Skeleton variant="rectangular" width={120} height={40} className="rounded-2xl " animation="wave" />
        </div>
  
        {/* Tariff Sections Skeleton */}
        {[...Array(3)].map((_, index) => (
          <div
            key={index}
            className="max-w-[1100px] w-full rounded-3xl shadow-lg p-6 bg-white ml-16"
          >
            {/* Outbound Header */}
            <div className="flex items-center py-1 px-6 mt-6">
              <Skeleton variant="rectangular" width={20} height={20} className="mr-2" animation="wave" />
              <Skeleton variant="text" width={150} height={30} className="ml-4" animation="wave" />
            </div>
  
            {/* Outbound Flight Options */}
            {[...Array(2)].map((_, idx) => (
              <div
                key={`outbound-${idx}`}
                className="flex flex-row justify-between items-center space-x-2 mb-2 p-3"
              >
                <Skeleton variant="circular" width={28} height={28} />
                <Skeleton variant="rectangular" width={112} height={80} className="rounded-2xl" />
                <div className="flex flex-col items-center space-y-2">
                  <Skeleton variant="text" width={100} height={20} />
                  <Skeleton variant="text" width={120} height={40} />
                  <Skeleton variant="text" width={150} height={20} />
                </div>
                <div className="flex flex-col items-center space-y-2">
                  <Skeleton variant="rectangular" width={80} height={20} className="rounded-full" />
                  <Skeleton variant="rectangular" width={296} height={16} />
                  <Skeleton variant="rectangular" width={100} height={20} className="rounded-full" />
                </div>
                <div className="flex flex-col items-center space-y-2">
                  <Skeleton variant="text" width={100} height={20} />
                  <Skeleton variant="text" width={120} height={40} />
                  <Skeleton variant="text" width={150} height={20} />
                </div>
                <Skeleton variant="rectangular" width={32} height={32} />
              </div>
            ))}
  
            {/* Divider */}
            <Skeleton variant="rectangular" width="100%" height={1} className="my-4 -ml-6" />
  
            {/* Return Header */}
            <div className="flex items-center py-1 px-6 mt-6">
              <Skeleton variant="rectangular" width={20} height={20} className="mr-2" />
              <Skeleton variant="text" width={150} height={30} className="ml-4" />
            </div>
  
            {/* Return Flight Options */}
            {[...Array(2)].map((_, idx) => (
              <div
                key={`return-${idx}`}
                className="flex flex-row justify-between items-center space-x-2 mb-2 p-3"
              >
                <Skeleton variant="circular" width={28} height={28} />
                <Skeleton variant="rectangular" width={112} height={80} className="rounded-2xl" />
                <div className="flex flex-col items-center space-y-2">
                  <Skeleton variant="text" width={100} height={20} />
                  <Skeleton variant="text" width={120} height={40} />
                  <Skeleton variant="text" width={150} height={20} />
                </div>
                <div className="flex flex-col items-center space-y-2">
                  <Skeleton variant="rectangular" width={80} height={20} className="rounded-full" />
                  <Skeleton variant="rectangular" width={296} height={16} />
                  <Skeleton variant="rectangular" width={100} height={20} className="rounded-full" />
                </div>
                <div className="flex flex-col items-center space-y-2">
                  <Skeleton variant="text" width={100} height={20} />
                  <Skeleton variant="text" width={120} height={40} />
                  <Skeleton variant="text" width={150} height={20} />
                </div>
                <Skeleton variant="rectangular" width={32} height={32} />
              </div>
            ))}
  
            {/* Price and Booking Button */}
            <div className="flex flex-col md:flex-row justify-between items-center mt-8">
              <div className="flex items-center mb-4 md:mb-0">
                <Skeleton variant="rectangular" width={20} height={20} className="mr-2" />
                <Skeleton variant="text" width={150} height={30} />
              </div>
              <div className="flex flex-row space-x-8">
                <div className="flex flex-col text-end">
                  <Skeleton variant="text" width={100} height={40} />
                  <Skeleton variant="text" width={200} height={20} />
                </div>
                <Skeleton variant="rectangular" width={200} height={56} className="rounded-2xl" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  };
  
  export default SkeletonComponent;