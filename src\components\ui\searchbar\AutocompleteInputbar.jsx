import React, { useEffect, useRef, useState } from 'react';

import { MapPin } from 'lucide-react';
import PropTypes from 'prop-types';

const AutocompleteInputbar = ({ onCitySelect, cardsData, isLoading }) => {
  const [inputValue, setInputValue] = useState('');
  const [filteredOptions, setFilteredOptions] = useState([]);
  const [isDropdownVisible, setIsDropdownVisible] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (containerRef.current && !containerRef.current.contains(event.target)) {
        setIsDropdownVisible(false);
        setIsFocused(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Group hotels by city and find cheapest in each city
  const getCitiesWithCheapestHotels = (hotels) => {
    const citiesMap = new Map();
    
    hotels.forEach(hotel => {
      const cityKey = `${hotel.cityData.cityName}, ${hotel.cityData.countryData.countryName}`.toLowerCase();
      
      if (!citiesMap.has(cityKey)) {
        citiesMap.set(cityKey, hotel);
      } else {
        const existingHotel = citiesMap.get(cityKey);
        if (hotel.cheapestRoomCharge < existingHotel.cheapestRoomCharge) {
          citiesMap.set(cityKey, hotel);
        }
      }
    });
    
    return Array.from(citiesMap.values());
  };

  useEffect(() => {
    if (isFocused || inputValue) {
      let filtered = [...cardsData];

      // If input is a number, filter by price
      const priceValue = parseFloat(inputValue);
      const isPriceSearch = !isNaN(priceValue) && inputValue.trim() !== '';

      if (isPriceSearch) {
        filtered = filtered.filter(item => item.cheapestRoomCharge <= priceValue);
      } else if (inputValue) {
        // Filter by city/country/hotel name
        const searchString = inputValue.toLowerCase();
        filtered = filtered.filter(item =>
          item.cityData.cityName.toLowerCase().includes(searchString) ||
          item.cityData.countryData.countryName.toLowerCase().includes(searchString) ||
          item.hotelName.toLowerCase().includes(searchString)
        );
      }

      // Get unique cities with cheapest hotels
      const citiesWithCheapestHotels = getCitiesWithCheapestHotels(filtered);
      
      // Sort by price (cheapest first)
      citiesWithCheapestHotels.sort((a, b) => a.cheapestRoomCharge - b.cheapestRoomCharge);
      setFilteredOptions(citiesWithCheapestHotels);
    }
  }, [cardsData, inputValue, isFocused]);

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
    setIsDropdownVisible(true);
  };

  const handleSelect = (hotel) => {
    setInputValue(`${hotel.cityData.cityName}, ${hotel.cityData.countryData.countryName}`);
    setIsDropdownVisible(false);
    onCitySelect(hotel);
  };

  return (
    <div className="relative w-full md:w-96" ref={containerRef}>
      <input
        type="text"
        className={`w-full px-4 py-3 text-sm transition-all duration-200 ease-in-out ${
          isFocused ? 'border-2 border-blue-500 rounded-lg shadow-sm' : 'border-b-2 border-gray-300 rounded-t-lg'
        } focus:outline-none focus:ring-0`}
        placeholder="Search destinations, hotels or max price (e.g., '200')..."
        value={inputValue}
        onChange={handleInputChange}
        onFocus={() => {
          setIsFocused(true);
          setIsDropdownVisible(true);
        }}
        disabled={isLoading}
      />

      {isDropdownVisible && (
        <div className="absolute mt-1 w-full max-h-60 overflow-y-auto bg-white rounded-b-lg shadow-lg z-50 border border-t-0 border-gray-200">
          {isLoading ? (
            <div className="px-4 py-3 text-sm text-gray-500 flex justify-center">
              <svg className="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </div>
          ) : filteredOptions.length > 0 ? (
            filteredOptions.map((hotel) => (
              <div
                key={hotel.hotelId}
                onClick={() => handleSelect(hotel)}
                className="px-4 py-3 text-sm cursor-pointer hover:bg-blue-50 transition-colors duration-150 border-b border-gray-100 last:border-b-0"
              >
                <div className="font-medium flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>{hotel.cityData.cityName}, {hotel.cityData.countryData.countryName}</span>
                </div>
                <div className="text-xs text-gray-500 mt-1 pl-6">
                  {hotel.cheapestRoomCharge > 0 ? (
                    <>
                      <span className="font-medium">Cheapest: {hotel.hotelName}</span>
                      <span className="ml-2">(CHF {hotel.cheapestRoomCharge.toFixed(2)})</span>
                    </>
                  ) : 'Price available soon'}
                </div>
              </div>
            ))
          ) : (
            <div className="px-4 py-3 text-sm text-gray-500">No matching destinations found</div>
          )}
        </div>
      )}
    </div>
  );
};

AutocompleteInputbar.propTypes = {
  onCitySelect: PropTypes.func.isRequired,
  cardsData: PropTypes.arrayOf(PropTypes.shape({
    hotelId: PropTypes.number.isRequired,
    hotelName: PropTypes.string.isRequired,
    cityData: PropTypes.shape({
      cityName: PropTypes.string.isRequired,
      countryData: PropTypes.shape({
        countryName: PropTypes.string.isRequired,
      }).isRequired,
    }).isRequired,
    cheapestRoomCharge: PropTypes.number.isRequired,
  })).isRequired,
  isLoading: PropTypes.bool,
};

export default AutocompleteInputbar;