import React, { Suspense } from "react";
import { RouterProvider } from "react-router-dom";
import { router } from "./routes/AllRoutes";
import ErrorBoundary from "./components/ui/errorBoundary/ErrorBoundary";
import LoadingScreen from "./components/ui/loading/LoadingScreen";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const App = () => {
  return (
    <ErrorBoundary>
      <Suspense fallback={<LoadingScreen />}>
        <RouterProvider router={router} />
      </Suspense>
      <ToastContainer
        position="top-right"
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={false}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />
    </ErrorBoundary>
  );
};

export default App;
