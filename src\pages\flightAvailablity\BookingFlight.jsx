import React, { useState } from "react";
import { Formik, Field, Form } from "formik";
import * as Yup from "yup";
import { ChevronDown, ChevronUp, Clock, Bookmark, Moon, Star, ArrowLeftRight, Loader2, X, Briefcase, Package, Plane, Armchair } from "lucide-react";
import { MdOutlineAirplanemodeActive } from "react-icons/md";
import Line from "../../assets/view/Arrow.svg";
import Vector from "../../assets/view/Vector.png";
import airports from "../../data/airports.json";
import { airlines } from "./AirlinesData";
import { useNavigate } from "react-router-dom";
import axios from "axios";
import FlightAvailableFilter from "./FlightAvailableFilter";
import FlightList from "./FlightList";


const parseDateTime = (dateStr, timeStr) => {
  return new Date(`${dateStr}T${timeStr}`);
};

const computeTotalDuration = (legs) => {
  let totalMs = 0;
  legs.forEach((leg, index) => {
    const depDateTime = parseDateTime(leg.departure_date, leg.departure_time);
    const arrDateTime = parseDateTime(leg.arrival_date, leg.arrival_time);
    totalMs += arrDateTime - depDateTime;
    if (index < legs.length - 1) {
      const nextLeg = legs[index + 1];
      const nextDepDateTime = parseDateTime(nextLeg.departure_date, nextLeg.departure_time);
      const waitingMs = nextDepDateTime - arrDateTime;
      totalMs += Math.max(waitingMs, 0);
    }
  });
  const hours = Math.floor(totalMs / (1000 * 60 * 60));
  const minutes = Math.floor((totalMs % (1000 * 60 * 60)) / (1000 * 60));
  return `${hours} h  ${minutes} m`;
};

const BookingFlight = ({ data, totalPassengers, dep_apt, des_apt, adults, children, babies, sessionId }) => {
  const [expandedFlight, setExpandedFlight] = useState(null);
  const Navigate = useNavigate();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [selectedAirline, setSelectedAirline] = useState(null);
  const [selectedTarifId, setSelectedTarifId] = useState(null);// Added state to track selected tariff

  const toggleFilter = () => {
    setIsFilterOpen(!isFilterOpen);
  };

  const toggleFlightDetails = (flightId) => {
    setExpandedFlight((prev) => (prev === flightId ? null : flightId));
  };

  // Filter data based on selectedAirline
  const filteredData = selectedAirline
    ? data.filter((tarif) =>
      tarif.fareDTOList.some((fare) => fare.airline === selectedAirline)
    )
    : data;

  const getAirportDetails = (code) => {
    const airport = airports.find((a) => a.iata === code);
    return airport ? `${airport.name}` : "Airport details not found";
  };

  const getAirportLocation = (code) => {
    const airport = airports.find((a) => a.iata === code);
    return airport ? `${airport.city}, ${airport.country}` : "Airport location not found";
  };

  const getAirportByIata = (iataCode) => {
    return airports.find((airport) => airport.iata === iataCode);
  };

  const renderFlightHeader = (type, airlineCode) => (
    // <div className="flex flex-row justify-between items-center p-2 md:p-4 md:pr-10 bg-backgroundColor rounded-2xl">
    //   <div className="flex items-center space-x-4 p-2">
    //     {type === "outbound" ? (
    //       <>
    //         <MdOutlineAirplanemodeActive size={20} className="mr-2 text-black/50" />
    //         <div className="flex flex-col ml-4">
    //           <div className="font-medium text-base text-black/60">Outbound Flight</div>
    //           <div className="text-xs flex items-center">
    //             <MdOutlineAirplanemodeActive size={14} className="mr-2 text-black/50" />
    //             <span className="text-black/60">
    //               {airlines[airlineCode]?.name || airlines.default.name}
    //             </span>
    //           </div>
    //         </div>
    //       </>
    //     ) : (
    //       <>
    //         <MdOutlineAirplanemodeActive size={20} className="mr-2 text-black/50" style={{ transform: "rotate(180deg)" }} />
    //         <div className="flex flex-col ml-4">
    //           <div className="font-medium text-base text-black/60">Return Flight</div>
    //           <div className="text-xs flex items-center">
    //             <MdOutlineAirplanemodeActive size={14} className="mr-2 text-black/50" />
    //             <span className="text-black/60">
    //               {airlines[airlineCode]?.name || airlines.default.name}
    //             </span>
    //           </div>
    //         </div>
    //       </>
    //     )}
    //   </div>
    //   <div className="w-[60px] bg-white h-[60px] border border-border rounded-full p-2 flex items-center justify-center">
    //     <img
    //       src={airlines[airlineCode]?.logo || airlines.default.logo}
    //       alt="Airline Logo"
    //       className="w-full h-full object-contain"
    //     />
    //   </div>
    <div className="flex flex-col py-1  px-6 mt-6 rounded-2xl">
      <div className="flex items-center">
        {type === "outbound" ? (
          <MdOutlineAirplanemodeActive size={20} className="mr-2 text-black rotate-90" />
        ) : (
          <MdOutlineAirplanemodeActive size={20} className="mr-2 text-black" style={{ transform: "rotate(270deg)" }} />
        )}
        <div className="font-semibold text-xl text-smokyGray ml-4">
          {type === "outbound" ? "Outbound" : "Return Flight"}
        </div>
      </div>
    </div>
  );

  const renderExpandedFlightDetails = (legs) => (
    <div className="p-6 w-full bg-white justify-center text-sm border border-border rounded-3xl font-inter mb-4">
      {legs.map((leg, index) => {
        const airlineCode =
          leg.fno && typeof leg.fno === "string" && leg.fno.length >= 2
            ? leg.fno.substring(0, 2).toUpperCase()
            : "default";
        const depDateTime = parseDateTime(leg.departure_date, leg.departure_time);
        const arrDateTime = parseDateTime(leg.arrival_date, leg.arrival_time);
        const durationMs = arrDateTime - depDateTime;
        const durationString = `${Math.floor(durationMs / (1000 * 60 * 60))}h ${Math.floor(
          (durationMs % (1000 * 60 * 60)) / (1000 * 60)
        )}m`;
        let waitingString = null;
        if (index < legs.length - 1) {
          const nextLeg = legs[index + 1];
          const nextDepDateTime = parseDateTime(nextLeg.departure_date, nextLeg.departure_time);
          const waitingMs = nextDepDateTime - arrDateTime;
          if (waitingMs > 0) {
            const waitingHours = Math.floor(waitingMs / (1000 * 60 * 60));
            const waitingMinutes = Math.floor((waitingMs % (1000 * 60 * 60)) / (1000 * 60));
            waitingString = `${waitingHours}h ${waitingMinutes}m`;
          }
        }
        return (
          <React.Fragment key={index}>
            <div className="flex items-center justify-center mb-4">
              <div className="flex flex-col ml-20 space-y-1 items-center">
                <div className="text-xl font-bold text-darkBlue">{leg.departure_airport_code}</div>
                <div className="text-xs text-smokyGray">{getAirportDetails(leg.departure_airport_code)}</div>
                <div className="text-xs text-smokyGray">{getAirportLocation(leg.departure_airport_code)}</div>
                <div className="text-sm font-medium text-darkBlue">{leg.departure_date}</div>
                <div className="text-sm font-bold text-darkBlue">{leg.departure_time}</div>
              </div>
              <div className="flex flex-col items-center flex-grow mx-4">
                <div className="text-smokyGray text-sm text-center mt-2">Travel Time {durationString}</div>
                <div className="flex items-center w-full max-w-[541px]">
                  <div className="h-0.5 bg-border flex-grow"></div>
                  <MdOutlineAirplanemodeActive size={20} className="text-orange rotate-90" />
                  <div className="h-0.5 bg-border flex-grow"></div>
                </div>
                <div className="text-xs text-smokyGray">{leg.fno || "655"}</div>
              </div>
              <div className="flex flex-col items-center space-y-1 mr-20">
                <div className="text-xl font-bold text-darkBlue">{leg.destination_airport_code}</div>
                <div className="text-xs text-smokyGray">{getAirportDetails(leg.destination_airport_code)}</div>
                <div className="text-xs text-smokyGray">{getAirportLocation(leg.destination_airport_code)}</div>
                <div className="text-sm font-medium text-darkBlue">{leg.arrival_date}</div>
                <div className="text-sm font-bold text-darkBlue">{leg.arrival_time}</div>
              </div>
            </div>

            <div className="flex flex-wrap justify-center text- font-light gap-4 my-4">
              <div className="flex items-center bg-bgcolor px-3  py-1 text-xs">
                <Clock size={14} className="mr-1" />
                <span>Duration {durationString}</span>
              </div>
              <div className="flex items-center bg-bgcolor px-3 py-1  text-xs">
                <Bookmark size={14} className="mr-1" />
                <span>Lowcost {leg.lowcost || "(Y)"}</span>
              </div>
              <div className="flex items-center bg-bgcolor px-3 py-1 text-xs">
                <Star size={14} className="mr-1 " />
                <span>Economy</span>
              </div>
              <div className="flex items-center bg-bgcolor px-3 py-1 text-xs">
                <ArrowLeftRight size={14} className="mr-1 " />
                <span>Distance: {leg.distance || "3456"} km</span>
              </div>
              <div className="flex items-center bg-bgcolor px-3 py-1 text-xs">
                <Moon size={14} className="mr-1 " />
                <span>Overnight</span>
              </div>
            </div>

            <div className="flex flex-wrap justify-center text-smokyGray gap-4 mb-4">
              <div className="flex items-center bg-bgcolor px-3 py-1 text-xs">
                <Briefcase size={14} className="mr-1 " />
                <span>Cabin Baggage: Included</span>
              </div>
              <div className="flex items-center bg-bgcolor px-3 py-1 text-xs">
                <Package size={14} className="mr-1 " />
                <span>Free Baggage Allowance: 1 X Pcs</span>
              </div>
            </div>
            {waitingString && (
              <div className="my-6 text-center flex flex-row space-x-2 py-3 px-4 rounded-lg">
                <div className="h-0.5 mt-[10px] bg-border flex-grow"></div>
                <div className="text-gray-600 font-medium">Waiting Duration: {waitingString}</div>
                <div className="h-0.5 mt-[10px] bg-border flex-grow"></div>
              </div>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );

  const initialValues = { outboundFlight: "", returnFlight: "" };
  const validationSchema = Yup.object({
    outboundFlight: Yup.string().required("Please select an outbound flight"),
    returnFlight: Yup.string().required("Please select a return flight"),
  });

  const getPricing = async (outboundFlightId, returnFlightId, tarifId) => {
    const payload = {
      tarif_id: tarifId,
      flightIds: [outboundFlightId, returnFlightId]
    };

    try {
      const response = await axios.post(
        "https://backend.graycorp.io:9100/eflyer-bookings/api/v1/pricing",
        payload,
        {
          headers: {
            "Content-Type": "application/json",
            "session_id": sessionId
          }
        }
      );
      return response.data;
    } catch (error) {
      console.error("Pricing request error:", error);
      return error.response?.data || {
        response: "<?xml version=\"1.0\" encoding=\"UTF-8\"?><errorResponse no=\"0\">Unknown error occurred</errorResponse>",
        Session: sessionId
      };
    }
  };

  const handleSubmit = async (values, tarif) => {
    const selectedOutbound = tarif.fareDTOList[0].flightDTOS.find(
      (f) => f.flightId === values.outboundFlight
    );
    const selectedReturn = tarif.fareDTOList[1].flightDTOS.find(
      (f) => f.flightId === values.returnFlight
    );

    const apiResponse = await getPricing(
      values.outboundFlight,
      values.returnFlight,
      tarif.tarifId
    );
    const totalPrice = apiResponse.totalPrice
      ? parseFloat(apiResponse.totalPrice)
      : tarif.tarifPriceDetails.total_sell_Price;

    const formatLegDetails = (flight) =>
      flight.legDTOS.map((leg) => ({
        legId: leg.legId || "N/A",
        departure_airport_code: leg.departure_airport_code,
        departure_time: leg.departure_time || "N/A",
        arrival_airport_code: leg.destination_airport_code,
        arrival_time: leg.arrival_time || "N/A",
        arrival_date: leg.arrival_date,
        departure_date: leg.departure_date,
        stops: leg.stops || "Direct",
        airlineCode:
          leg.fno && typeof leg.fno === "string" && leg.fno.length >= 2
            ? leg.fno.substring(0, 2).toUpperCase()
            : "default",
        fno: leg.fno || "N/A",
      }));

    const bookingDetails = {
      tarifId: tarif.tarifId,
      outboundFlightId: values.outboundFlight,
      returnFlightId: values.returnFlight,
      departureDate: selectedOutbound.first_departure_date,
      departureTime: selectedOutbound.first_departure_time,
      arrivalDate: selectedOutbound.final_destination_date,
      arrivalTime: selectedOutbound.final_destination_time,
      returnDepartureDate: selectedReturn.first_departure_date,
      returnDepartureTime: selectedReturn.first_departure_time,
      returnArrivalDate: selectedReturn.final_destination_date,
      returnArrivalTime: selectedReturn.final_destination_time,
      outboundStops: formatLegDetails(selectedOutbound),
      returnStops: formatLegDetails(selectedReturn),
      outboundAirlineCode: tarif.fareDTOList[0].airline,
      returnAirlineCode: tarif.fareDTOList[1].airline,
      totalPrice,
      passangers: { totalPassengers },
      adults: adults || 2,
      children: children || 0,
      babies: babies || 0,
      sessionId,
      apiResponse,
      priceDetails: tarif.tarifPriceDetails,
      outboundBaggage: selectedOutbound.baggage || { FBA_1: "N/A" },
      returnBaggage: selectedReturn.baggage || { FBA_1: "N/A" },
    };
    console.log("Booking Details Object:", bookingDetails);

    Navigate("/Personaldetails", { state: { bookingDetails } });
  };

  const TarifForm = ({ tarif, selectedTarifId, setSelectedTarifId, airlineCode }) => (
    <Formik
      initialValues={initialValues}
      validationSchema={validationSchema}
      onSubmit={(values) => handleSubmit(values, tarif)}
    >
      {({ isSubmitting, values, resetForm, setFieldValue }) => {
        // Reset form if this tariff is not selected
        React.useEffect(() => {
          if (selectedTarifId !== tarif.tarifId) {
            resetForm({ values: initialValues });
          }
        }, [selectedTarifId, tarif.tarifId, resetForm]);

        return (
          <Form className="space-y-8 px-6 font-inter">

            {renderFlightHeader(
              "outbound",
              tarif.fareDTOList[0].airline,
              tarif.fareDTOList[0].flightDTOS[0].first_departure_airport,
              tarif.fareDTOList[0].flightDTOS[0].final_destination_airport
            )}
            <div className="flex flex-col space-y-6">
              {tarif.fareDTOList[0].flightDTOS.map((flight) => {
                const airlineCode = (flight.airways || 'default').trim().toUpperCase();
                return (
                  <div
                    key={flight.flightId}
                    className={`w-full -mt-6 rounded-3xl transition-color  ${values.outboundFlight === flight.flightId ? "bg-white " : "bg-white"
                      }`}
                  >
                    <div className="flex flex-row justify-between items-center space-x-0 mb-2">
                      <label className="flex items-center justify-between cursor-pointer w-full mr-20">
                        <div className="flex flex-row justify-between space-x-2 items-center w-full">
                          <Field
                            type="radio"
                            name="outboundFlight"
                            value={flight.flightId}
                            onChange={(e) => {
                              setFieldValue('outboundFlight', e.target.value);
                              setSelectedTarifId(tarif.tarifId);
                            }}
                            className="w-7 h-7 accent-darkBlue ml-8"
                          />
                          <div className="flex flex-col items-center space-y-2">
                            <div className="w-16 h-16 rounded-xl border border-border bg-white flex items-center justify-center">
                              <img
                                src={airlines[airlineCode]?.logo || airlines.default.logo}
                                alt="Airline Logo"
                                className="w-full h-full object-contain p-2"
                              />
                            </div>
                            <div className="text-xs flex items-center space-x-2 ">
                              <Plane size={14} className=" text-black/50" />
                              <span className="text-black/60">
                                {airlines[airlineCode]?.name || airlines.default.name}
                              </span>
                            </div>
                          </div>
                          <div className="flex flex-col items-center">
                            {/* <span className="text-base text-smokyGray">{flight.first_departure_airport}</span> */}
                            <span className="text-xs text-smokyGray">{`${(getAirportByIata(dep_apt)?.city || dep_apt).toUpperCase()}`}</span>
                            <span className="font-bold text-3xl text-darkBlue tracking-widest font-roboto"> {flight.final_destination_time} </span>
                            <span className="text-xs text-smokyGray">
                              {new Date(flight.first_departure_date).toLocaleDateString("en-GB", {
                                weekday: "short",
                                day: "2-digit",
                                month: "short",
                                year: "numeric",
                              }).replace(",", " |")}
                            </span>
                          </div>
                          {/* <div className="flex flex-col items-center mx-4">
                          <span className="text-xs text-smokyGray">{computeTotalDuration(flight.legDTOS)}</span>
                          <img src={Line} alt="Line" className="w-32" />
                          <span className="text-xs text-smokyGray">
                            {flight.stops === 0
                              ? `${flight.stops}`
                              : flight.stops === 1
                                ? `stop: ${flight.stops}`
                                : `stops: ${flight.stops}`}
                          </span>
                        </div> */}
                          <div className="flex flex-col items-center space-y-6">
                            <span className="bg-blue-100 text-darkBlue text-[10px] font-normal px-3 py-1 mb-0 rounded-full">
                              Economy
                            </span>
                            <div className="relative w-80 mt-6">
                              <img src={Line} alt="Line" className="w-74 h-4 text-[#757575]" />
                              <div className="absolute left-1/2 top-1/2 w-9 h-9 bg-darkBlue text-white rounded-full flex items-center justify-center -translate-x-1/2 -translate-y-1/2">
                                <MdOutlineAirplanemodeActive size={16} className="text-white rotate-90" />
                              </div>
                              <div className="absolute left-8 -mt-6 transform -translate-y-1/2 text-xs text-smokyGray">
                                {computeTotalDuration(flight.legDTOS)}
                              </div>
                              <div className="absolute right-10 -mt-6 transform -translate-y-1/2 text-xs text-smokyGray">
                                {flight.stops === 0
                                  ? "direct"
                                  : flight.stops === 1
                                    ? `1 stop`
                                    : `${flight.stops} stop`
                                }
                              </div>
                            </div>
                            <div className="bg-smokyGray/5 text-smokyGray text-[10px] font-normal  px-4 py-1 rounded-full flex items-center">
                              <Briefcase size={14} className="mr-1 " />
                              <span>Baggage included {flight.baggage.FBA_1}
                              </span>
                            </div>
                          </div>
                          <div className="flex flex-col items-center">
                            {/* <span className="text-base text-smokyGray">{flight.final_destination_airport}</span> */}
                            <span className="text-xs text-smokyGray">{`${(getAirportByIata(des_apt)?.city || des_apt).toUpperCase()}`}</span>
                            <span className="font-bold text-3xl text-darkBlue tracking-widest font-roboto">{flight.final_destination_time}</span>
                            <span className="text-xs text-smokyGray">
                              {new Date(flight.final_destination_date).toLocaleDateString("en-GB", {
                                weekday: "short",
                                day: "2-digit",
                                month: "short",
                                year: "numeric",
                              }).replace(",", " |")}
                            </span>
                          </div>
                        </div>
                      </label>
                      <div className="flex">
                        <div
                          onClick={() => toggleFlightDetails(flight.flightId)}
                          className="cursor-pointer bg-gray-100 p-2"
                        >
                          {expandedFlight === flight.flightId ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                        </div>
                      </div>
                    </div>
                    {expandedFlight === flight.flightId && renderExpandedFlightDetails(flight.legDTOS)}
                  </div>
                );
              })}

            </div>
            <div className="w-[1100px] border-b border-border -ml-12 "></div>

            {renderFlightHeader(
              "return",
              tarif.fareDTOList[1].airline,
              tarif.fareDTOList[1].flightDTOS[0].first_departure_airport,
              tarif.fareDTOList[1].flightDTOS[0].final_destination_airport
            )}
            <div className="flex flex-col space-y-6 ">
              {tarif.fareDTOList[1].flightDTOS.map((flight) => {
                const airlineCode = (flight.airways || 'default').trim().toUpperCase();
                return (
                  <div
                    key={flight.flightId}
                    className={`w-full -mt-6 rounded-3xl transition-colors ${values.returnFlight === flight.flightId ? "bg-white" : "bg-white"
                      }`}
                  >
                    <div className="flex flex-row justify-between items-center space-x-0 mb-2">
                      <label className="flex items-center justify-between cursor-pointer w-full mr-20">
                        <div className="flex flex-row justify-between space-x-2 items-center w-full">
                          <Field
                            type="radio"
                            name="returnFlight"
                            value={flight.flightId}
                            onChange={(e) => {
                              setFieldValue('returnFlight', e.target.value);
                              setSelectedTarifId(tarif.tarifId); // Set this tariff as selected
                            }}
                            className="w-7 h-7 accent-darkBlue ml-8"
                          />
                          <div className="flex flex-col items-center space-y-4">
                            <div className="w-16 h-16 bg-white border border-border rounded-xl flex items-center justify-center">
                              <img
                                src={airlines[airlineCode]?.logo || airlines.default.logo}
                                alt="Airline Logo"
                                className="w-full h-full object-contain p-2"
                              />
                            </div>
                            <div className="text-xs flex items-center space-x-2 ">
                            <Plane size={14} className=" text-black/50" />
                            <span className="text-black/60">
                              {airlines[airlineCode]?.name || airlines.default.name}
                            </span>
                          </div>
                          </div>
                          <div className="flex flex-col items-center ">
                            <span className="text-xs text-smokyGray">{`${(getAirportByIata(des_apt)?.city || des_apt).toUpperCase()}`}</span>
                            {/* <span className="text-base text-smokyGray">{flight.first_departure_airport}</span> */}
                            <span className="font-bold text-3xl text-darkBlue tracking-widest font-roboto ">{flight.first_departure_time}</span>
                            <span className="text-xs text-smokyGray">
                              {new Date(flight.first_departure_date).toLocaleDateString("en-GB", {
                                weekday: "short",
                                day: "2-digit",
                                month: "short",
                                year: "numeric",
                              }).replace(",", " |")}
                            </span>
                          </div>
                          <div className="flex flex-col items-center space-y-6">
                            <span className="bg-blue-100 text-darkBlue text-[10px] font-normal px-3 py-1 mb-0 rounded-full">
                              Economy
                            </span>
                            <div className="relative w-80 mt-6">
                              <img src={Line} alt="Line" className="w-74 h-4 text-[#757575]" />
                              <div className="absolute left-1/2 top-1/2 w-9 h-9 bg-darkBlue text-white rounded-full flex items-center justify-center -translate-x-1/2 -translate-y-1/2">
                                <MdOutlineAirplanemodeActive size={20} className="text-white rotate-90" />
                              </div>
                              <div className="absolute left-8 -mt-6 transform -translate-y-1/2 text-xs text-smokyGray">
                                {computeTotalDuration(flight.legDTOS)}
                              </div>
                              <div className="absolute right-10 -mt-6 transform -translate-y-1/2 text-xs text-smokyGray">
                                {flight.stops === 0
                                  ? "Non-stop"
                                  : flight.stops === 1
                                    ? `1 stop`
                                    : `${flight.stops} stops`
                                }
                              </div>
                            </div>
                            <div className="bg-smokyGray/5 text-smokyGray text-[10px] font-normal  px-4 py-1 rounded-full flex items-center">
                              <Briefcase size={14} className="mr-1 " />
                              <span>Baggage included {flight.baggage.FBA_1}
                              </span>
                            </div>
                          </div>
                          <div className="flex flex-col items-center -ml-4">
                            <span className="text-base text-smokyGray">{`${(getAirportByIata(dep_apt)?.city || dep_apt).toUpperCase()}`}</span>
                            {/* <span className="text-base text-smokyGray">{flight.final_destination_airport}</span> */}
                            <span className="font-bold text-3xl text-darkBlue tracking-widest font-roboto">{flight.final_destination_time}</span>
                            <span className="text-base text-smokyGray">
                              {new Date(flight.final_destination_date).toLocaleDateString("en-GB", {
                                weekday: "short",
                                day: "2-digit",
                                month: "short",
                                year: "numeric",
                              }).replace(",", " |")}
                            </span>
                          </div>
                        </div>
                      </label>
                      <div
                        onClick={() => toggleFlightDetails(flight.flightId)}
                        className="cursor-pointer bg-gray-100 p-2 "
                      >
                        {expandedFlight === flight.flightId ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                      </div>
                    </div>
                    {expandedFlight === flight.flightId && renderExpandedFlightDetails(flight.legDTOS)}
                  </div>
                );
              })}
            </div>
            <div className="w-[1100px] border-b border-border -ml-12 mb-8"></div>

            <div className="flex flex-col md:flex-row justify-between items-center">
              {/* <div className="flex items-center text-xl text-darkBlue mb-4 md:mb-0">
                <img src={Vector} alt="Line" className="mr-1" />
                <span className="ml-2">9 Seats Available </span>
              </div> */}
              <div className="flex flex-col mt-4 text-start">
                <span className="font-bold text-4xl text-darkBlue text-nowrap font-roboto">
                  CHF {tarif.tarifPriceDetails.taxAndSell_adt}
                </span>
                <div className="text-sm ">
                  Total:
                  <span className="text-sm ml-1">CHF {tarif.tarifPriceDetails.total_sell_Price} ({totalPassengers} passengers)</span>
                </div>
              </div>
              {/* <div className="flex flex-col md:flex-row space-x-4 mb-4 md:mb-0 ml-4">
                <span className="text-smokyGray text-base md:text-3xl font-medium text-nowrap">
                  CHF {tarif.tarifPriceDetails.taxAndSell_adt}
                </span> */}
              <div className="flex flex-row space-x-8 ">
                {/* <div className="flex flex-col text-end mt-4">
                  <span className="font-bold text-4xl text-darkBlue text-nowrap font-roboto">
                    CHF {tarif.tarifPriceDetails.taxAndSell_adt}
                  </span>
                  <div className="text-sm ">
                    Total:
                    <span className="text-sm ml-1">CHF {tarif.tarifPriceDetails.total_sell_Price} ({totalPassengers} passengers)</span>
                  </div>
                </div> */}
                {/* </div> */}
                <button
                  type="submit"
                  disabled={!values.outboundFlight || !values.returnFlight || isSubmitting}
                  className="bg-darkBlue text-2xl hover:bg-blue-600 text-white font-medium py-4 mt-4 px-12 rounded-3xl w-full md:w-auto mr-4"
                >
                  {isSubmitting ? (
                    <span className="flex items-center">
                      <Loader2 className="animate-spin mr-2" /> Processing...
                    </span>
                  ) : (
                    "Book"
                  )}
                </button>
              </div>
            </div>
          </Form>
        );
      }}
    </Formik>
  );

  if (!data) return null;

  return (
    <div className="flex flex-col space-y-8 w-full">
      <div className="flex flex-row justify-between">
        <div className="text-sm md:text-base px-4 mt-8 text-smokyGray font-medium">
          {data?.length || 0} Offers Found{" "}
          <span className="text-darkBlue text-lg">{getAirportByIata(dep_apt)?.city || 'Origin'}</span> to{" "}
          <span className="text-darkBlue text-lg">{getAirportByIata(des_apt)?.city || 'Destination'}</span>
        </div>
        <div className="flex justify-end  text-center mb-2 mt-6">
          <button
            onClick={toggleFilter}
            className="px-4 py-2 text-darkBlue border border-darkBlue rounded-2xl max-w-40 w-full flex justify-center"
          >
            Sort And Filter
          </button>
        </div>
      </div>
      {isFilterOpen && (
        <div className="fixed inset-0 bg-black/50 z-40 flex justify-end">
          <div className="bg-white h-auto overflow-y-auto w-full max-w-[420px] animate-slide-in translate-x-4">
            <div className="pt-4 flex justify-end items-center">
              <button
                onClick={toggleFilter}
                className="rounded-full hover:bg-smokyGray"
              >
                <X className="w-6 h-6" />
              </button>
            </div>
            <div className="p-4">
              <FlightAvailableFilter setIsFilterOpen={setIsFilterOpen} />
            </div>
          </div>
        </div>
      )}
      <FlightList
        data={data}
        selectedAirline={selectedAirline}
        setSelectedAirline={setSelectedAirline}
      />
      {filteredData.map((tarif) => (
        <div
          key={tarif.tarifId}
          className="max-w-[1100px] w-full rounded-3xl border border-border  text-smokyGray p-6 "
        >
          <TarifForm
            tarif={tarif}
            selectedTarifId={selectedTarifId}
            setSelectedTarifId={setSelectedTarifId}
          />
        </div>
      ))}
    </div>
  );
};

export default BookingFlight;