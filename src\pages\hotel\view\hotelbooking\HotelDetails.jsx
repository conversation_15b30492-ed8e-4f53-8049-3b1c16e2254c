import React from "react";
import { MapPin, Star, Check } from "lucide-react";
import FacilitiesIcon from "../FacilitiesIcon";

const HotelDetails = ({ bookingDetails }) => {
  const { hotelDetails, selectedRoom, searchCriteria, nights, dateRangeText } = bookingDetails || {};
  const name = hotelDetails?.name || "Unknown Hotel";
  const address = `${hotelDetails?.address || "Unknown Address"}, ${hotelDetails?.city || "Unknown City"}, ${hotelDetails?.country || "Unknown Country"}`;
  const rating = hotelDetails?.rating || 9.2;
  const reviewCount = hotelDetails?.reviewCount || 9504;
  const starCount = hotelDetails?.starCount || 0;
  const classificationName = hotelDetails?.classificationName || "Unrated";
  const facilities = hotelDetails?.facilities || [];
  const benefits = selectedRoom?.benefits || [];
  const adults = searchCriteria?.adults || 2;
  const children = searchCriteria?.children || 0;
  const rooms = searchCriteria?.rooms || 1;
  const totalSelectedRooms = selectedRoom?.selectedRoomCounts
    ? Object.values(selectedRoom?.selectedRoomCounts).reduce((sum, count) => sum + count, 0)
    : rooms;


  return (
    <div className="rounded-2xl border border-darkBlue w-full p-6 bg-white shadow-sm space-y-6">
      <div className="flex flex-col md:ml-6">
        <h3 className="text-base font-semibold text-darkBlue">{name}</h3>
        <div className="flex items-center mt-2">
          <MapPin className="h-5 w-5 mr-1 text-smokyGray" strokeWidth={1} />
          <span className="text-sm text-gray-600 underline cursor-pointer">{address}</span>
        </div>
        <div className="flex items-center mt-2">
          {starCount === 0 ? (
            <span className="text-sm text-darkBlue font-medium">{classificationName}</span>
          ) : (
            [...Array(5)].map((_, i) => (
              <Star
                key={i}
                className="w-4 h-4"
                fill={i < starCount ? "orange" : "none"}
                stroke={i < starCount ? "none" : "orange"}
              />
            ))
          )}
        </div>
      </div>
      <div className="flex flex-col md:ml-6">
        <h1 className="font-medium text-sm">{selectedRoom?.roomTypeName || 'Standard Room'}</h1>
        <ul className="mt-0 space-y-2">
          {benefits.length > 0 ? (
            benefits.map((benefit, index) => (
              <li key={index} className="flex items-center text-sm text-darkGreen">
                <Check className="w-4 h-4 mr-2" strokeWidth={1} />
                {benefit.text}
                {benefit.condition && (
                  <span className="text-xs text-darkGreen ml-0">({benefit.condition})</span>
                )}
              </li>
            ))
          ) : (
            <li className="text-sm text-gray-600">No additional benefits included.</li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default HotelDetails;