export const getStarCount = (hotelDetails) => {
  if (hotelDetails.classificationBody) {
    const starsFromName = hotelDetails.classificationBody.classificationName?.match(/\*+/)?.[0]?.length;
    if (starsFromName) return starsFromName;

    const starsFromRating = hotelDetails.classificationBody.rating?.match(/(\d+)\s*Star/i)?.[1];
    if (starsFromRating) return parseInt(starsFromRating, 10);

    const classificationId = hotelDetails.classificationBody.classificationId;
    if (classificationId) {
      const mapping = { 559: 1, 560: 2, 561: 3, 562: 4, 563: 5 };
      return mapping[classificationId] || 0;
    }
  }

  if (hotelDetails.classificationCode) {
    const mapping = { 559: 1, 560: 2, 561: 3, 562: 4, 563: 5 };
    return mapping[hotelDetails.classificationCode] || 0;
  }

  return 0;
};

export const getClassificationName = (hotelDetails) => {
  if (hotelDetails.classificationBody?.classificationName) {
    return hotelDetails.classificationBody.classificationName.replace(/\*+$/, '').trim();
  }
  if (hotelDetails.classificationBody?.rating) {
    return hotelDetails.classificationBody.rating;
  }
  return hotelDetails.classificationName || 'Unrated';
};

export const calculateNightsAndTravelers = (dateRange, travelerConfig) => {
  let nights = 1;
  let dateRangeText = 'Select dates';
  let travelersText = '2 Adults';

  try {
    const startDate = new Date(dateRange?.startDate);
    const endDate = new Date(dateRange?.endDate);
    if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
      if (endDate >= startDate) {
        nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
        const formatter = new Intl.DateTimeFormat('en-US', {
          month: 'short',
          day: '2-digit',
          year: 'numeric',
        });
        dateRangeText = `${formatter.format(startDate)} - ${formatter.format(endDate)}`;
      } else {
        console.warn('Invalid date range: endDate is before startDate');
        dateRangeText = 'Invalid dates';
      }
    }
  } catch (error) {
    console.error('Error calculating nights:', error);
  }

  const adults = travelerConfig?.adults || 2;
  const children = travelerConfig?.children || 0;
  travelersText = [
    adults > 0 ? `${adults} ${adults === 1 ? 'Adult' : 'Adults'}` : '',
    children > 0 ? `${children} ${children === 1 ? 'Child' : 'Children'}` : '',
  ].filter(Boolean).join(', ');

  return {
    nights: Math.max(1, nights),
    displayText: `${nights} ${nights === 1 ? 'night' : 'nights'}, ${travelersText}`,
    dateRangeText,
    travelersText,
  };
};

export const defaultImages = [
  '/assets/adventure/HotelImage1.svg',
  '/assets/adventure/HotelImage2.svg',
  '/assets/adventure/HotelImage3.svg',
  '/assets/adventure/HotelImage4.svg',
  '/assets/adventure/HotelImage5.svg',
  '/assets/explore/img_2.png',
  '/assets/explore/img_3.png',
  '/assets/explore/img_4.jpg',
  '/assets/explore/img_5.jpg',
  '/assets/explore/img_6.jpg',
];

export const defaultFacilities = [
  { label: 'Room' },
  { label: 'Free wifi' },
  { label: 'Pool with view' },
  { label: 'Balcony' },
  { label: 'Air conditioning' },
  { label: 'Mini Bar' },
  { label: 'Soundproofing' },
  { label: 'Roof Top pool' },
];

export const defaultRoomFeatures = [
  { label: 'Garden view' },
  { label: 'Pool view' },
  { label: 'City view' },
  { label: 'Free private parking available at the hotel' },
];