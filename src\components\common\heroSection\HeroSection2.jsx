import React, { useState, useEffect } from "react";
import backgroundImage from "../../../assets/heroSection/HomeImage.jpeg";
import backgroundImage2 from "../../../assets/heroSection/background.png";
import backgroundImage3 from "../../../assets/heroSection/bg-2.png";
import backgroundImage4 from "../../../assets/heroSection/bg-3.png";
// import backgroundImage5 from "../../../assets/heroSection/i-1.jpg";
// import backgroundImage6 from "../../../assets/heroSection/i-2.jpg";
import backgroundImage7 from "../../../assets/heroSection/i-3.jpg";
import backgroundImage8 from "../../../assets/heroSection/i-4.png";
import SearchForm2 from "../../../pages/home/<USER>/SearchForm2";
import WhiteLogo from "../../../assets/footer/whitelogo.png";
import { IoIosAirplane } from "react-icons/io";
import { BedDouble, Navigation } from "lucide-react";

const HeroSection2 = () => {
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());
  const [selectedTab, setSelectedTab] = useState(0); // Track selected tab
  const [currentImageIndex, setCurrentImageIndex] = useState(0); // Track current background image

  // Array of background images
  const backgroundImages = [
    backgroundImage,
    backgroundImage2,
    backgroundImage3,
    backgroundImage4,
    // backgroundImage5,
    // backgroundImage6,
    backgroundImage7,
    backgroundImage8,
  ];

  function calculateTimeLeft() {
    const targetDate = new Date("April 12, 2025 12:00:00");
    const now = new Date();
    const difference = targetDate - now;
    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / (1000 * 60)) % 60),
        seconds: Math.floor((difference / (1000) % 60)),
      };
    } else {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  // Background image slider effect
  useEffect(() => {
    const sliderTimer = setInterval(() => {
      setCurrentImageIndex((prevIndex) =>
        prevIndex === backgroundImages.length - 1 ? 0 : prevIndex + 1
      );
    }, 5000); // Change image every 5 seconds
    return () => clearInterval(sliderTimer);
  }, [backgroundImages.length]);

  const currentDate = new Date();
  const options = { day: "numeric", month: "long", year: "numeric" };
  const currentDayMonthYear = currentDate.toLocaleDateString("en-GB", options);

  // Calculate date 3 days later
  const threeDaysLater = new Date(currentDate);
  threeDaysLater.setDate(currentDate.getDate() + 3);
  const plusDayMonthYear = threeDaysLater.toLocaleDateString("en-GB", options);

  // Callback to handle tab changes from SearchForm2
  const handleTabChange = (tabIndex) => {
    setSelectedTab(tabIndex);
  };

  // Handle manual navigation to specific image
  const goToImage = (index) => {
    setCurrentImageIndex(index);
  };

  // const getSubheadingText = (tabIndex) => {
  //   switch (tabIndex) {
  //     case 0:
  //       return "BOOK TRENDING PACKAGES WITH";
  //     case 1:
  //       return "BOOK FLIGHTS WITH";
  //     case 2:
  //       return "BOOK HOTELS WITH";
  //     case 3:
  //       return "DISCOVER AMAZING TOURS WITH";
  //     default:
  //       return "BOOK FLIGHTS, HOTELS AND TRAVEL PACKAGES WITH";
  //   }
  // };

  // Define tabConfig here to pass to TravelDateCard
  const tabConfig = [
    {
      destinationLabel: "Jaffna",
      prefixText: "Stay at",
      icon: (
        <div className="flex items-center space-x-2">
          <IoIosAirplane className="w-8 h-8 text-white" />
          <span className="text-white text-base md:text-xl font-semibold uppercase">+</span>
          <BedDouble className="w-6 h-6 text-white" />
        </div>
      ),
      dateText: `Between ${currentDayMonthYear} - ${plusDayMonthYear}`,
    },
    {
      destinationLabel: "Jaffna",
      prefixText: "Travel to",
      icon: <IoIosAirplane className="w-8 h-8 text-white" />,
      dateText: `Travel-on ${currentDayMonthYear}`,
    },
    {
      destinationLabel: "Jaffna",
      prefixText: "Stay at",
      icon: <BedDouble className="w-8 h-8 text-white" />,
      dateText: `Between ${currentDayMonthYear} - ${plusDayMonthYear}`,
    },
    {
      destinationLabel: "Sri Lanka",
      prefixText: "Explore",
      icon: <Navigation className="w-8 h-8 text-white" />,
      dateText: `On ${currentDayMonthYear}`,
    },
  ];

  const currentConfig = tabConfig[selectedTab] || tabConfig[0];

  return (
    <>
      {/* Hero Section */}
      <div className="w-full h-fit min-h-screen flex justify-center">
        {/* Background image slider */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          {backgroundImages.map((image, index) => (
            <img
              key={index}
              src={image}
              alt={`Background ${index + 1}`}
              className={`w-full h-full object-fill absolute top-0 left-0 transition-opacity duration-1000 ease-in-out ${
                index === currentImageIndex ? "opacity-100" : "opacity-0"
              }`}
            />
          ))}
          {/* Navigation dots */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-20 flex space-x-2">
            {backgroundImages.map((_, index) => (
              <button
                key={index}
                onClick={() => goToImage(index)}
                className={`w-3 h-3 rounded-full ${
                  index === currentImageIndex ? "bg-orange" : "bg-white/50"
                } hover:bg-orange/80 transition-colors`}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
        <div className="absolute inset-0 z-10 bg-gradient-to-b from-black/70 via-white/10 to-black/70"></div>
        {/* <div className="absolute top-[117px] left-0 w-full z-40 flex justify-center space-y-10">
          <div className="text-white text-center">
            <p className="text-sm sm:text-base font-inter font-light mb-3">
              {getSubheadingText(selectedTab)}
            </p>
            <h2 className="text-xl sm:text-4xl font-inter font-semibold">
              The Best Travel Guide
            </h2>
          </div>
          <SearchForm2 onTabChange={handleTabChange} />
        </div> */}
      </div>

      {/* Desktop & Tablet View */}
      {/* <div className="hidden sm:block">
        <div className="absolute z-10 bottom-0 w-full h-20 bg-darkBlue bg-opacity-70 flex items-center justify-center">
          <OfferSectionContent
            timeLeft={timeLeft}
            selectedTab={selectedTab}
            currentDayMonthYear={currentDayMonthYear}
            plusDayMonthYear={plusDayMonthYear}
          />
          <TravelDateCard
            dateText={tabConfig[selectedTab]?.dateText}
            prefixText={currentConfig.prefixText}
            destinationLabel={currentConfig.destinationLabel}
          />
         
        </div>
      </div> */}

      {/* Mobile View */}
      {/* <div className="sm:hidden relative">
        <div className="absolute inset-0 z-0">
          <img
            src={backgroundImages[currentImageIndex]}
            alt="Mountains"
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-darkBlue bg-opacity-85"></div>
        </div>
        <div className="relative z-10 px-4 flex-col flex items-center justify-center space-y-4 py-6 -mt-20">
          <OfferSectionContent
            timeLeft={timeLeft}
            currentDayMonthYear={currentDayMonthYear}
            plusDayMonthYear={plusDayMonthYear}
            selectedTab={selectedTab}
          />
        </div>
      </div> */}
    </>
  );
};

const OfferSectionContent = ({ timeLeft, currentDayMonthYear, plusDayMonthYear, selectedTab }) => {
  // Map tab index to text, icon, and date text
  const tabConfig = [
    {
      destinationLabel: "Jaffna",
      prefixText: "Stay at",
      icon: (
        <div className="flex items-center space-x-2">
          <IoIosAirplane className="w-8 h-8 text-white" />
          <span className="text-white text-base md:text-xl font-semibold uppercase">+</span>
          <BedDouble className="w-6 h-6 text-white" />
        </div>
      ),
      dateText: `Between ${currentDayMonthYear} - ${plusDayMonthYear}`,
    },
    {
      destinationLabel: "Sri Lanka",
      prefixText: "Travel to",
      icon: <IoIosAirplane className="w-8 h-8 text-white" />,
      dateText: `Travel-on  ${currentDayMonthYear} - ${plusDayMonthYear}`,
    },
    {
      destinationLabel: "Jaffna",
      prefixText: "Stay at",
      icon: <BedDouble className="w-8 h-8 text-white" />,
      dateText: `Between ${currentDayMonthYear} - ${plusDayMonthYear}`,
    },
    {
      destinationLabel: "Sri Lanka",
      prefixText: "Explore",
      icon: <Navigation className="w-8 h-8 text-white" />,
      dateText: `On ${currentDayMonthYear}`,
    },
  ];

  const currentConfig = tabConfig[selectedTab] || tabConfig[0];

  return (
    <div className="flex flex-col md:flex-row items-center justify-between w-full max-w-[80%] space-y-4 md:space-y-0">
      <div className="bg-white p-4 w-36 h-14 rounded-[2px] flex flex-col items-center justify-center">
        <p className="text-darkBlue text-xs font-medium mt-1">STARTS FROM</p>
        <p className="text-darkBlue text-xs font-semibold mr-2">
          CHF
          <span className="text-darkBlue text-2xl font-bold ml-2">500</span>
        </p>
      </div>

      <div className="flex items-center justify-center">
        {currentConfig.icon}
      </div>

      <div className="items-center flex flex-col">
        <button className="bg-orange rounded-[8px] px-4 py-2 md:h-[50px] text-white hover:bg-orange transition-colors text-base md:text-xl hover:scale-105 font-semibold">
          BOOK NOW
        </button>
        <p className="text-xs text-white font-extralight mt-1">T&C Apply</p>
      </div>
    </div>
  );
};

const TravelDateCard = ({ dateText = "", prefixText = "", destinationLabel = "" }) => {
  const dateParts = dateText.split(" ");
  const label = dateParts[0] || "";
  const date = dateParts.slice(1).join(" ") || "";

  return (
    <div className="absolute flex-col flex md:flex-row space-x-36 left-[595px] w-[40%] ">
      <div className="flex flex-col text-base items-end  ">
        <h1 className="text-orange text-xs">{prefixText}</h1>
        <p className="text-white text-base font-semibold uppercase">{destinationLabel}</p>
      </div>
      <div className="flex flex-col items-start text-left  ">
        <h1 className="text-orange text-xs">{label}</h1>
        <p className="text-white text-base font-semibold uppercase">{date}</p>
      </div>
    </div>
  );
};

export default HeroSection2;