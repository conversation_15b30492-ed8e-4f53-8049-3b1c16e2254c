import React from 'react';
import { useLocation } from 'react-router-dom';
import ProgressTracker from '../personal_details/progresstracker';
import ConfirmationCard from '../../components/ui/ConfirmationCard';
import { Clock, Bookmark, Star, ArrowLeftRight, Moon } from 'lucide-react';
import OutboundFlight from '../../assets/view/OutboundFlight.svg';
import ReturnFlight from '../../assets/view/ReturnFlight.svg';
import airports from '../../data/airports.json';
import Line from '../../assets/view/Arrow.svg';
import { airlines } from '../flightAvailablity/AirlinesData';

const PersonalDetailsSummary = ({ InvoiceDetails, travelers, bookingDetails, bookingResponse }) => {
  const parseXmlResponse = (xmlString) => {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlString, 'text/xml');
    const errorResponse = xmlDoc.getElementsByTagName('errorResponse')[0];
    if (errorResponse) {
      const errorNo = errorResponse.getAttribute('no');
      const errorMessage = errorResponse.textContent;
      return { errorNo, errorMessage };
    }
    return null;
  };

  const xmlError = bookingResponse.contents?.Results?.response
    ? parseXmlResponse(bookingResponse.contents.Results.response)
    : null;

  return (
    <div className="w-full p-4 mb-6 bg-slate-50 rounded-xl border border-border">
      <h2 className="text-lg font-medium text-darkBlue mb-3">Personal Details Summary</h2>
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2">Main Contact</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Name:</span> {InvoiceDetails.firstName || 'N/A'} {InvoiceDetails.lastName || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Email:</span> {InvoiceDetails.email || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Phone:</span> {InvoiceDetails.phoneNumber || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Address:</span> {InvoiceDetails.streetName || 'N/A'}, {InvoiceDetails.postalCode || 'N/A'}
          </p>
        </div>
      </div>
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2">Booking Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Outbound Flight ID:</span> {bookingDetails.outboundFlightId || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Return Flight ID:</span> {bookingDetails.returnFlightId || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Tarif ID:</span> {bookingDetails.tarifId || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Session ID:</span> {bookingDetails.sessionId || 'N/A'}
          </p>
        </div>
      </div>
      <div className="mb-4">
        <h3 className="text-sm font-medium mb-2">Booking Response</h3>
        <div className="text-sm text-smokyGray p-2 bg-white rounded border border-border">
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Validation Status:</span> {bookingResponse.validation_status || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Validation Code:</span> {bookingResponse.validation_Code || 'N/A'}
          </p>
          <p className="text-sm text-smokyGray">
            <span className="font-medium">Validation Message:</span> {bookingResponse.validation_message || 'N/A'}
          </p>
          {bookingResponse.contents?.Results?.Session && (
            <p className="text-sm text-smokyGray">
              <span className="font-medium">Session ID:</span> {bookingResponse.contents.Results.Session}
            </p>
          )}
          {xmlError ? (
            <p className="text-sm text-red-600">
              <span className="font-medium">Error:</span> {xmlError.errorMessage}
            </p>
          ) : (
            <p className="text-sm text-green-600">
              <span className="font-medium">Booking Successful</span>
            </p>
          )}
        </div>
      </div>
      {travelers && travelers.length > 0 && (
        <div>
          <h3 className="text-sm font-medium mb-2">Travelers</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {travelers.map((traveler) => (
              <div key={traveler.id} className="text-sm text-smokyGray p-2 bg-white rounded border border-border">
                <p>
                  <span className="font-medium">Traveler {traveler.id}:</span> {traveler.type}
                </p>
                <p>
                  <span className="font-medium">Name:</span> {traveler.firstName || 'N/A'} {traveler.lastName || 'N/A'}
                </p>
                <p>
                  <span className="font-medium">Gender:</span> {traveler.gender || 'N/A'}
                </p>
                <p>
                  <span className="font-medium">DOB:</span> {traveler.dateOfBirth || 'N/A'}
                </p>
                <p>
                  <span className="font-medium">Nationality:</span> {traveler.nationality || 'N/A'}
                </p>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const FlightDetails = ({ bookingDetails, staticFlightData }) => {
  const getAirportDetails = (code) => {
    const airport = airports.find((a) => a.iata === code);
    return airport ? `${airport.name}, ${airport.city}, ${airport.country}` : 'Airport details not found';
  };

  const computeTotalDuration = (legs) => {
    let totalMs = 0;
    legs.forEach((leg, index) => {
      const depDateTime = new Date(`${leg.departure_date}T${leg.departure_time}`);
      const arrDateTime = new Date(`${leg.arrival_date}T${leg.arrival_time}`);
      totalMs += arrDateTime - depDateTime;
      if (index < legs.length - 1) {
        const nextLeg = legs[index + 1];
        const nextDepDateTime = new Date(`${nextLeg.departure_date}T${nextLeg.departure_time}`);
        const waitingMs = nextDepDateTime - arrDateTime;
        totalMs += Math.max(waitingMs, 0);
      }
    });
    const hours = Math.floor(totalMs / (1000 * 60 * 60));
    const minutes = Math.floor((totalMs % (1000 * 60 * 60)) / (1000 * 60));
    return `${hours}H ${minutes}M`;
  };

  const renderFlightHeader = (type, airlineCode) => {
    const flightData = type === 'outbound' ? staticFlightData.outbound : staticFlightData.return;
    return (
      <div className="flex flex-row justify-between items-center p-2 md:p-4 md:pr-10 bg-[#FAFAFA] rounded-2xl">
        <div className="flex items-center space-x-8">
          <img src={type === 'outbound' ? OutboundFlight : ReturnFlight} alt={`${type} Flight`} />
          <div className="flex flex-col">
            <div className="font-medium text-base">{type.charAt(0).toUpperCase() + type.slice(1)} Flight</div>
            <span className="text-sm text-smokyGray">
              {flightData.legs.length > 0
                ? `${flightData.legs[0].departure_airport_code} → ${flightData.legs[flightData.legs.length - 1].destination_airport_code}`
                : 'Direct Flight'}
            </span>
          </div>
        </div>
        <div className="w-[70px] h-[70px] border border-border rounded-full p-2 flex items-center justify-center">
          <img
            src={airlines[airlineCode]?.logo || airlines.default.logo}
            alt="Airline Logo"
            className="w-full h-full object-contain"
          />
        </div>
      </div>
    );
  };

  const renderExpandedFlightDetails = (legs) => (
    <div className="p-6 w-full bg-white text-sm border border-border rounded-3xl font-inter">
      {legs.map((leg, index) => {
        const airlineCode = leg.fno.substring(0, 2).toUpperCase();
        const durationString = leg.duration;
        let waitingString = null;
        if (index < legs.length - 1) {
          const nextLeg = legs[index + 1];
          const arrDateTime = new Date(`${leg.arrival_date}T${leg.arrival_time}`);
          const nextDepDateTime = new Date(`${nextLeg.departure_date}T${nextLeg.departure_time}`);
          const waitingMs = nextDepDateTime - arrDateTime;
          if (waitingMs > 0) {
            const waitingHours = Math.floor(waitingMs / (1000 * 60 * 60));
            const waitingMinutes = Math.floor((waitingMs % (1000 * 60 * 60)) / (1000 * 60));
            waitingString = `${waitingHours}h ${waitingMinutes}m`;
          }
        }
        return (
          <React.Fragment key={index}>
            <div className="relative flex flex-col w-full max-w-3xl mx-auto">
              <div className="flex relative">
                <div className="w-24 text-end text-sm flex flex-col">
                  <div className="mb-40 mt-2">
                    <div className="text-smokyGray">{leg.departure_date}</div>
                    <div className="text-smokyGray font-semibold">{leg.departure_time}</div>
                    <div className="text-smokyGray mt-1">{leg.fno}</div>
                  </div>
                  <div>
                    <div className="text-smokyGray">{leg.arrival_date}</div>
                    <div className="text-smokyGray font-semibold">{leg.arrival_time}</div>
                  </div>
                </div>
                <div className="relative w-12 mx-1">
                  <div className="absolute top-2 left-1/2 w-0.5 h-64 bg-border transform -translate-x-1/2"></div>
                  <div className="absolute top-2 left-1/2 w-4 h-4 rounded-full bg-darkBlue transform -translate-x-1/2"></div>
                  <div className="absolute bottom-2 left-1/2 w-4 h-4 rounded-full border border-darkBlue bg-white transform -translate-x-1/2"></div>
                </div>
                <div className="flex-1 flex flex-col text-sm">
                  <div className="mb-2 mt-2">
                    <span className="text-smokyGray font-medium">{leg.departure_airport_code}</span>
                    <div className="text-xs text-gray-500">{getAirportDetails(leg.departure_airport_code)}</div>
                  </div>
                  <div className="lg:grid lg:grid-cols-2 gap-x-4 gap-y-2 lg:my-6 text-sm">
                    <div className="flex items-center font-light">
                      <Clock size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">Duration: {durationString}</span>
                    </div>
                    <div className="flex items-center font-light">
                      <Bookmark size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">Lowcost: {leg.cost}</span>
                    </div>
                    <div className="flex items-center font-light">
                      <Star size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">Class: {leg.flight_class}</span>
                    </div>
                    <div className="flex items-center font-light">
                      <ArrowLeftRight size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">Distance: {leg.distance_km} km</span>
                    </div>
                    <div className="flex items-center font-light">
                      <Moon size={14} className="mr-2 text-smokyGray" />
                      <span className="text-smokyGray">{leg.overnight ? 'Overnight' : 'Daytime'}</span>
                    </div>
                  </div>
                  <div className="mb-2 mt-auto">
                    <span className="text-smokyGray font-medium">{leg.destination_airport_code}</span>
                    <div className="text-xs text-gray-500">{getAirportDetails(leg.destination_airport_code)}</div>
                  </div>
                </div>
              </div>
            </div>
            {waitingString && (
              <div className="flex items-center justify-center gap-6 mb-4 mt-6 bg-[#FAFAFA] rounded-xl p-3 shadow-sm">
                <span className="text-smokyGray font-light">Layover: {waitingString}</span>
              </div>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );

  return (
    <div className="border border-border rounded-2xl p-6 pb-2 mt-4">
      <h2 className="text-xl font-normal text-smokyGray mb-6 ml-6">Flight Details</h2>
      <div className="p-6 mt-2 mb-2">
        {renderFlightHeader('outbound', staticFlightData.outbound.airline)}
        <div className="flex flex-row justify-between items-center md:pl-4 space-x-4 mb-2">
          <div className="flex flex-row justify-between items-center w-full mt-6">
            <div className="flex flex-col items-start ml">
              <span className="text-sm text-smokyGray">{staticFlightData.outbound.first_departure_date}</span>
              <span className="font-medium text-smokyGray">{staticFlightData.outbound.first_departure_time}</span>
            </div>
            <div className="flex flex-col items-center mx-4">
              <span className="text-xs text-smokyGray">{computeTotalDuration(staticFlightData.outbound.legs)}</span>
              <img src={Line} alt="Line" className="w-32" />
              <span className="text-xs text-smokyGray">
                {staticFlightData.outbound.stops === 0
                  ? `${staticFlightData.outbound.stops}`
                  : staticFlightData.outbound.stops === 1
                    ? `stop: ${staticFlightData.outbound.stops}`
                    : `stops: ${staticFlightData.outbound.stops}`}
              </span>
            </div>
            <div className="flex flex-col items-end">
              <span className="text-sm text-smokyGray">{staticFlightData.outbound.final_destination_date}</span>
              <span className="font-medium text-smokyGray">{staticFlightData.outbound.final_destination_time}</span>
            </div>
          </div>
        </div>
        {renderExpandedFlightDetails(staticFlightData.outbound.legs)}
      </div>
      <div className="p-6 mt-2 mb-2">
        {renderFlightHeader('return', staticFlightData.return.airline)}
        <div className="flex flex-row justify-between items-center md:pl-4 space-x-4 mb-2">
          <div className="flex flex-row justify-between items-center w-full mt-6">
            <div className="flex flex-col items-start">
              <span className="text-sm text-smokyGray">{staticFlightData.return.first_departure_date}</span>
              <span className="font-medium text-smokyGray">{staticFlightData.return.first_departure_time}</span>
            </div>
            <div className="flex flex-col items-center mx-4">
              <span className="text-xs text-smokyGray">{computeTotalDuration(staticFlightData.return.legs)}</span>
              <img src={Line} alt="Line" className="w-32" />
              <span className="text-xs text-smokyGray">
                {staticFlightData.return.stops === 0
                  ? `${staticFlightData.return.stops}`
                  : staticFlightData.return.stops === 1
                    ? `stop: ${staticFlightData.return.stops}`
                    : `stops: ${staticFlightData.return.stops}`}
              </span>
            </div>
            <div className="flex flex-col items-end">
              <span className="text-sm text-smokyGray">{staticFlightData.return.final_destination_date}</span>
              <span className="font-medium text-smokyGray">{staticFlightData.return.final_destination_time}</span>
            </div>
          </div>
        </div>
        {renderExpandedFlightDetails(staticFlightData.return.legs)}
      </div>
    </div>
  );
};

const PaymentComponent = () => {
  const location = useLocation();
  const values = location.state?.values || {};
  const InvoiceDetails = values.InvoiceDetails || {};
  const bookingDetails = values.bookingDetails || {};
  const bookingResponse = values.bookingResponse || {};
  const travelers = values.travelers || [];

  const reservationData = {
    code: bookingResponse.bookingCode || 'Q2OTDD',
    issueDeadline: bookingResponse.issueDeadline || '10 . 03 . 2025 , 05 : 05',
    bookingCompletedAt: bookingResponse.completedAt || '07. 03. 2025, 06:06',
  };

  const invoiceAddress = [
    { label: 'First name', value: InvoiceDetails.firstName || 'N/A' },
    { label: 'Last Name', value: InvoiceDetails.lastName || 'N/A' },
    { label: 'Street', value: InvoiceDetails.streetName || 'N/A' },
    { label: 'Postcode/ city', value: InvoiceDetails.postalCode || 'N/A' },
    { label: 'Phone Number', value: InvoiceDetails.phoneNumber || 'N/A' },
    { label: 'Email Address', value: InvoiceDetails.email || 'N/A' },
  ];

  const priceDetails = [
    {
      label: 'Price Range',
      value: `${bookingDetails.departureDate || 'N/A'} ${bookingDetails.departureTime || 'N/A'} from ${bookingDetails.outboundStops?.[0]?.departure_airport_code || 'N/A'}`,
    },
    { label: 'Last Name', value: InvoiceDetails.lastName || 'N/A' },
    { label: 'Taxes', value: `CHF ${bookingResponse.taxes || 'N/A'}` },
    { label: 'Total Incl . Taxes', value: `CHF ${bookingDetails.totalPrice || 'N/A'}` },
    { label: 'Payment Key', value: 'On account' },
    { label: 'Total Price', value: `CHF ${bookingDetails.totalPrice || 'N/A'}` },
  ];

  const agencyInfo = {
    name: 'EFly AG',
    address: 'Longstrasse 214',
    city: 'CH 8005 Zurich',
    phone: '0041 44666 06 06',
    email: '<EMAIL>',
    description: 'EFly internet travel shop',
    subDescription: 'insurance package for',
    openingHours: {
      current: 'Open Now',
      closing: 'Close at 18:00',
      nextDay: 'Open on Friday 09:00 - 18:00',
    },
  };

  const constructFlightData = (type) => {
    const stops = type === 'outbound' ? bookingDetails.outboundStops : bookingDetails.returnStops;
    const airlineCode = type === 'outbound' ? bookingDetails.outboundAirlineCode : bookingDetails.returnAirlineCode;

    if (!stops || stops.length === 0) {
      return {
        airline: airlineCode || 'default',
        first_departure_date: type === 'outbound' ? bookingDetails.departureDate : bookingDetails.returnDepartureDate,
        first_departure_time: type === 'outbound' ? bookingDetails.departureTime : bookingDetails.returnDepartureTime,
        final_destination_date: type === 'outbound' ? bookingDetails.arrivalDate : bookingDetails.returnArrivalDate,
        final_destination_time: type === 'outbound' ? bookingDetails.arrivalTime : bookingDetails.returnArrivalTime,
        stops: 0,
        legs: [],
      };
    }

    return {
      airline: airlineCode || 'default',
      first_departure_date: stops[0].departure_time ? bookingDetails.departureDate : stops[0].departure_date,
      first_departure_time: stops[0].departure_time || (type === 'outbound' ? bookingDetails.departureTime : bookingDetails.returnDepartureTime),
      final_destination_date: stops[stops.length - 1].arrival_time ? bookingDetails.arrivalDate : stops[stops.length - 1].arrival_date,
      final_destination_time: stops[stops.length - 1].arrival_time || (type === 'outbound' ? bookingDetails.arrivalTime : bookingDetails.returnArrivalTime),
      stops: stops.length - 1,
      legs: stops.map((stop) => ({
        departure_date: stop.departure_date || (type === 'outbound' ? bookingDetails.departureDate : bookingDetails.returnDepartureDate),
        departure_time: stop.departure_time || (type === 'outbound' ? bookingDetails.departureTime : bookingDetails.returnDepartureTime),
        arrival_date: stop.arrival_date || (type === 'outbound' ? bookingDetails.arrivalDate : bookingDetails.returnArrivalDate),
        arrival_time: stop.arrival_time || (type === 'outbound' ? bookingDetails.arrivalTime : bookingDetails.returnArrivalTime),
        departure_airport_code: stop.departure_airport_code,
        destination_airport_code: stop.arrival_airport_code || stop.destination_airport_code,
        fno: stop.fno || 'N/A',
        cost: 'Yes',
        flight_class: 'Economy',
        distance_km: Math.floor(Math.random() * 5000) + 1000,
        overnight: new Date(`${stop.arrival_date}T${stop.arrival_time}`) < new Date(`${stop.departure_date}T${stop.departure_time}`) ? 'Yes' : 'No',
        duration: `${Math.floor(Math.random() * 5) + 1}h ${Math.floor(Math.random() * 60)}m`,
      })),
    };
  };

  const staticFlightData = {
    outbound: constructFlightData('outbound'),
    return: constructFlightData('return'),
  };

  return (
    <div className='w-full'>
      <ProgressTracker currentStep={4} />
      <ConfirmationCard
        title="Thank You for booking!"
        reservationData={reservationData}
        invoiceAddress={invoiceAddress}
        priceDetails={priceDetails}
        agencyInfo={agencyInfo}
        bookingCompletedAt={reservationData.bookingCompletedAt}
        specificDetails={
          <>
            <PersonalDetailsSummary
              InvoiceDetails={InvoiceDetails}
              travelers={travelers}
              bookingDetails={bookingDetails}
              bookingResponse={bookingResponse}
            />
            <FlightDetails bookingDetails={bookingDetails} staticFlightData={staticFlightData} />
          </>
        }
        showChangeCheckbox={true}
      />
    </div>
  );
};

export default PaymentComponent;