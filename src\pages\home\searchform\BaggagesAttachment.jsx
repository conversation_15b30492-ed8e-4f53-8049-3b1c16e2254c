import React, { useState, useRef, useEffect } from "react";
import { CheckCircle2 } from "lucide-react";

const BaggagesAttachment = ({ values, setFieldValue, onClose }) => {
  const modalRef = useRef(null);
  const [selectedBaggage, setSelectedBaggage] = useState(
    values.baggage || "Carry-on baggage only"
  );

  // Close when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        onClose();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  const baggageOptions = ["Free Baggage", "Carry-on Baggage"];

  return (
    <div
      className="absolute z-20 mt-14 w-full bg-white rounded-[2px] shadow-lg p-4 border border-border"
      ref={modalRef}
    >
      <div className="mb-4">
        <h3 className="text-base  mb-2">Baggage</h3>
        <div className="space-y-2">
          {baggageOptions.map((option) => (
            <div
              key={option}
              onClick={() => {
                setSelectedBaggage(option);
                setFieldValue("baggage", option);
                onClose();
              }}
              className={`flex items-center justify-between p-2 text-sm font-light text-darkGray cursor-pointer rounded-[2px] ${
                selectedBaggage === option ? "bg-[#E6EDF2]" : "hover:bg-gray-50"
              }`}
            >
              <span>{option}</span>
              {selectedBaggage === option && (
                <CheckCircle2 className="text-darkBlue h-5 w-5" />
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default BaggagesAttachment;
