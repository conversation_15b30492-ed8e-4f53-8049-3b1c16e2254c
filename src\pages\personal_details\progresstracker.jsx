import React from 'react';
import { useNavigate } from 'react-router-dom';
import StepperWrapper from '../../components/ui/StepperWrapper';

const ProgressTracker = ({ currentStep, pageContent }) => {
  const steps = [
    { number: 1, title: 'Search' },
    { number: 2, title: 'Choose Flight' },
    { number: 3, title: 'Personal Data' },
    { number: 4, title: 'Confirmation' },
  ];

  const navigate = useNavigate();

  return (
    <StepperWrapper
      steps={steps}
      currentStep={currentStep}
      onBack={() => navigate(-1)}
    >
      {pageContent}
    </StepperWrapper>
  );
};

export default ProgressTracker;