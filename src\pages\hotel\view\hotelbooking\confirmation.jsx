import React from 'react';
import { useLocation, Link } from 'react-router-dom';
import TrackingStepper from './TrackingStepper';
import ConfirmationCard from '../../../../components/ui/ConfirmationCard';
import HotelDetails from './HotelDetails';
import ButtonCom from '../../../../components/ui/button/ButtonCom';
import { downloadPDF } from './generatePDF';

// Format date helper
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  } catch {
    return 'N/A';
  }
};

// Parse confirmation details to extract voucher information
const parseVoucherDetails = (confirmationDetails, bookingDetails) => {
  const { guestDetails, searchCriteria, selectedRoom, hotelDetails } = bookingDetails || {};
  const { adults = 2, children = 0 } = searchCriteria || {};
  const mainPassenger = guestDetails?.passengers?.[0] || {};

  // Debug logging to check hotelDetails structure
  console.log('Confirmation - Full bookingDetails:', bookingDetails);
  console.log('Confirmation - hotelDetails:', hotelDetails);
  console.log('Confirmation - hotelPhone:', hotelDetails?.hotelPhone);
  console.log('Confirmation - regionName:', hotelDetails?.regionName);
  console.log('Confirmation - All hotelDetails keys:', Object.keys(hotelDetails || {}));

  // Extract phone number with multiple fallbacks
  const getHotelPhone = () => {
    return hotelDetails?.hotelPhone ||
           hotelDetails?.phone ||
           hotelDetails?.phoneNumber ||
           hotelDetails?.contactPhone ||
           'N/A';
  };

  // Extract region name with multiple fallbacks
  const getRegionName = () => {
    return hotelDetails?.regionName ||
           hotelDetails?.region ||
           hotelDetails?.city ||
           hotelDetails?.location ||
           'N/A';
  };

  // Calculate total price and currency
  const totalPrice = confirmationDetails?.totalPrice || selectedRoom?.totalCharge || 0;
  const currencyCode = confirmationDetails?.currencyCode || selectedRoom?.currencyCode || 'CHF';
  const totalTax = selectedRoom?.totalTaxes ? parseFloat(selectedRoom.totalTaxes) : 0;
  const totalInclTaxes = totalPrice ? (parseFloat(totalPrice) + totalTax).toFixed(2) : 'N/A';

  const parseCancellationRules = (rules) => {
    if (!rules || !Array.isArray(rules) || rules.length === 0) {
      return [{ text: 'No cancellation policy available.' }];
    }
    return rules.map((rule) => {
      if (rule.type === 'free_cancellation') {
        return { text: `Free Cancellation: ${rule.toDateDetails || 'Before check-in'}` };
      } else if (rule.type === 'penalty_period') {
        return {
          text: `Cancellation Fee: ${rule.formattedCancelCharge || 'N/A'}`,
          subText: `From ${rule.fromDateDetails || 'N/A'}${rule.toDateDetails ? ` to ${rule.toDateDetails}` : ''}`,
        };
      } else if (rule.type === 'no_show') {
        return {
          text: `No Show Charge: ${rule.formattedCharge || rule.formattedCancelCharge || 'Full amount'}`,
        };
      }
      return { text: 'Unknown cancellation rule.' };
    });
  };

  // Format voucher details as an array of { label, value } for consistent UI
  const voucherInfo = [
    { label: 'Booking Reference No', value: confirmationDetails?.returnedCode || 'N/A' },
    { label: 'Printed On', value: formatDate(new Date()) },
    { label: 'Itinerary Number', value: confirmationDetails?.itineraryNumber || 'N/A' },
    { label: 'Booked By', value: 'EFly AG' },
    { label: 'Booking Status', value: confirmationDetails?.isSuccessful ? 'Confirmed' : 'Pending' },
  ];

  const serviceProviderInfo = [
    { label: 'Supplier Name', value: hotelDetails?.name || 'N/A' },
    { label: 'Service Name', value: hotelDetails?.name || 'N/A' },
    { label: 'Address', value: `${hotelDetails?.address || ''}, ${hotelDetails?.city || ''}, ${hotelDetails?.country || ''}`.trim() || 'N/A' },
    { label: 'Telephone', value: getHotelPhone() },
  ];

  const passengerInfo = [
    { label: 'Guest Name', value: mainPassenger ? `${mainPassenger.title || ''} ${mainPassenger.firstName || ''} ${mainPassenger.lastName || ''}`.trim() : 'N/A' },
    { label: 'Guest Email Address', value: guestDetails.passengers?.[0]?.email || 'N/A' },
    { label: 'Guest Phone Number', value: guestDetails.passengers?.[0]?.phoneNumber ? `+${guestDetails.passengers[0].phoneCode}${guestDetails.passengers[0].phoneNumber}` : 'N/A' },
    { label: 'Guest Nationality', value: mainPassenger.nationality || 'N/A' },
    { label: 'Country of Residence', value: mainPassenger.country || 'N/A' },
    { label: 'Service Type', value: 'Accommodation' },
    { label: 'City', value: hotelDetails?.city || 'N/A' },
    { label: 'Supplier Reference', value: confirmationDetails?.supplierReference || 'The supplier confirmation number will be visible 5 days before check-in.' },
    { label: 'Check-in', value: formatDate(searchCriteria?.dateRange?.startDate) },
    { label: 'Check-out', value: formatDate(searchCriteria?.dateRange?.endDate) },
    { label: 'Room Type', value: selectedRoom?.roomTypeName || 'N/A' },
    { label: 'Room Occupancy', value: `${adults} Adults${children > 0 ? `, ${children} Children` : ''}` },
    { label: 'Rate Basis', value: selectedRoom?.rateBaseName || 'N/A' },
    { label: 'Additional Requests', value: mainPassenger.additionalRequests || 'none' },
  ];

  return {
    voucherInfo,
    serviceProviderInfo,
    passengerInfo,
    dailyRates: selectedRoom?.availableDates?.map((date) => ({
      date: date.day || 'N/A',
      payableRate: `${date.formatedPrice || date.price || 'N/A'} CHF`,
      rateBasis: selectedRoom?.rateBaseName || 'N/A',
      rateMarket: getRegionName(),
    })) || [],
    totalPayable: totalPrice !== 'N/A' ? `${totalInclTaxes}` : 'N/A',
    currency: `CHF`,
    cancellationRules: parseCancellationRules(selectedRoom?.cancellationRules),
    tariffNotes: selectedRoom?.tariffNotes || 'No additional notes.',
  };
};

const Confirmation = () => {
  const { state } = useLocation();
  const { bookingDetails } = state || {};

  if (!bookingDetails) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-offWhite">
        <div className="text-center text-gray-600">
          <h3 className="text-lg font-semibold">No Booking Details Available</h3>
            <p className="text-sm mt-2">Please complete the booking process to view your confirmation.</p>
          <Link to="/">
            <ButtonCom variant="primary" size="md" rounded="md" width="auto">
              Return to Home
            </ButtonCom>
          </Link>
        </div>
      </div>
    );
  }

  const { guestDetails = {}, selectedRoom = {}, hotelDetails = {}, nights = 1, bookingCode, confirmationDetails, selectedPaymentMethod } = bookingDetails;

  const reservationData = {
    code: bookingCode || 'N/A',
    issueDeadline: confirmationDetails?.issueDeadline || formatDate(new Date(new Date().setDate(new Date().getDate() + 7))),
    bookingCompletedAt: confirmationDetails?.bookingCompletedAt || new Date().toLocaleString(),
  };

    // const invoiceAddress = [
  //   { label: 'First Name', value: guestDetails.passengers?.[0]?.firstName || 'N/A' },
  //   { label: 'Last Name', value: guestDetails.passengers?.[0]?.lastName || 'N/A' },
  //   { label: 'Email Address', value: guestDetails.passengers?.[0]?.email || 'N/A' },
  //   { label: 'Phone Number', value: guestDetails.passengers?.[0]?.phoneNumber ? `+${guestDetails.passengers[0].phoneCode}${guestDetails.passengers[0].phoneNumber}` : 'N/A' },
  // ];

  const totalPrice = confirmationDetails?.totalPrice || selectedRoom?.totalCharge || 0;
   const currencyCode =  'CHF';
  const totalTax = selectedRoom?.totalTaxes ? parseFloat(selectedRoom.totalTaxes) : 0;
  const totalInclTaxes = totalPrice ? (parseFloat(totalPrice) + totalTax).toFixed(2) : 'N/A';

  const priceDetails = [
    { label: 'Room Price', value: totalPrice ? `${currencyCode} ${parseFloat(totalPrice).toFixed(2)}` : 'N/A' },
    { label: 'Total Incl. Taxes', value: totalInclTaxes !== 'N/A' ? `${currencyCode} ${totalInclTaxes}` : 'N/A' },
    { label: 'Payment Method', value: selectedPaymentMethod || 'On account' },
  ];

  const agencyInfo = {
    name: 'EFly AG',
    address: 'Longstrasse 214',
    city: 'CH 8005 Zurich',
    phone: '0041 44666 06 06',
    email: '<EMAIL>',
    description: 'EFly internet travel shop',
    subDescription: 'insurance package for',
    openingHours: {
      current: 'Open Now',
      closing: 'Close at 18:00',
      nextDay: 'Open on Friday 09:00 - 18:00',
    },
  };

  const voucherDetails = parseVoucherDetails(confirmationDetails, bookingDetails);

  return (
    <>
      <TrackingStepper currentStep={3} />
      <div className="w-full md:max-w-[80%] mx-auto p-4 animate-fade-in">
        <ConfirmationCard
          title="Thank You for Booking!"
          reservationData={reservationData}
           // invoiceAddress={invoiceAddress}
          priceDetails={priceDetails}
          agencyInfo={agencyInfo}
          bookingCompletedAt={reservationData.bookingCompletedAt}
          voucherDetails={voucherDetails}
          specificDetails={<HotelDetails bookingDetails={bookingDetails} />}
          showChangeCheckbox={true}
        />
        <div className="flex justify-end items-center mt-6 w-full">
          <div className="flex justify-end items-center gap-4">
            <Link to="/">
              <ButtonCom variant="outline" size="md" rounded="md" width="auto">
                Back to Home
              </ButtonCom>
            </Link>
            <ButtonCom
              variant="primary"
              size="md"
              rounded="md"
              width="auto"
              icon="download"
              iconPosition="left"
              onClick={() => downloadPDF(bookingDetails, voucherDetails, bookingDetails.confirmationDetails.html)}
            >
              Download PDF
            </ButtonCom>
          </div>
        </div>
      </div>
    </>
  );
};

export default Confirmation;