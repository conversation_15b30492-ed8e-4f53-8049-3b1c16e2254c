import React, { useState } from "react";
import { ChevronLeft, ChevronRight, BedDouble, Bath, ParkingCircle, Utensils, Wifi, Eye, Globe, Check, ArrowRightLeft, CreditCard, Info, Baby, ArrowRightFromLine } from "lucide-react";
import { PiPersonSimpleWalkBold, PiCompass } from "react-icons/pi";
import { FaUtensils, FaTrain, FaUmbrellaBeach } from "react-icons/fa6";
import { IoMdAirplane } from "react-icons/io";
import Icon1 from "../../../../assets/packages/icon_1.svg";
import Icon2 from "../../../../assets/packages/icon_2.svg";
import Icon3 from "../../../../assets/packages/icon_3.svg";
import Icon4 from "../../../../assets/packages/icon_4.svg";
import Icon5 from "../../../../assets/packages/icon_5.svg";
import Icon6 from "../../../../assets/packages/icon_6.svg";
import Icon7 from "../../../../assets/packages/icon_7.svg";
import Icon8 from "../../../../assets/packages/icon_8.svg";
import img1 from "../../../../assets/view/hotel/img1.png";
import img2 from "../../../../assets/view/hotel/img2.png";
import img3 from "../../../../assets/view/hotel/img3.png";
import Visa from "../../../../assets/footer/paymentmethod/Visa.svg";
import Master from "../../../../assets/footer/paymentmethod/Mastercard.svg";
import Amex from "../../../../assets/footer/paymentmethod/Amex.svg";

const LocationPage = () => {
  const [currentIndex, setCurrentIndex] = useState(0);

  const nearbyPlaces = [
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" }
  ];

  const publicTransport = [
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" }
  ];

  const topAttractions = [
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" }
  ];

  const beach = [
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" }
  ];

  const hotel = [
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" },
    { name: "Gall Face Green", distance: "600 m" }
  ];

  const closestAirport = [
    { name: "Gall Face Green", distance: "600 m" }
  ];

  const restaurants = [
    {
      id: 1,
      name: "The Garden Coffee Shop",
      logo: img1,
      rating: 5,
      services: {
        customer: "Good customer service",
        cuisines: [
          "Chinese", "British", "French", "Indian",
          "Indonesian", "Italian", "Japanese", "Malaysian",
          "Pizza", "Seafood", "Sushi", "Thai", "Australian",
          "Local", "Asian", "International", "Grill/BBQ"
        ],
        openFor: ["Breakfast", "Brunch", "Lunch", "Dinner", "High tea"],
        ambiance: ["Family friendly", "Romantic"],
        dietary: ["Halal", "Vegetarian", "Gluten-free"]
      }
    },
    {
      id: 2,
      name: "The Garden Coffee Shop",
      logo: img2,
      rating: 5,
      services: {
        customer: "Good customer service",
        cuisines: [
          "Chinese", "British", "French", "Indian",
          "Indonesian", "Italian", "Japanese", "Malaysian",
          "Pizza", "Seafood", "Sushi", "Thai", "Australian",
          "Local", "Asian", "International", "Grill/BBQ"
        ],
        openFor: ["Breakfast", "Brunch", "Lunch", "Dinner", "High tea"],
        ambiance: ["Family friendly", "Romantic"],
        dietary: ["Halal", "Vegetarian", "Gluten-free"]
      }
    },
    {
      id: 3,
      name: "The Garden Coffee Shop",
      logo: img3,
      rating: 5,
      services: {
        customer: "Good customer service",
        cuisines: [
          "Chinese", "British", "French", "Indian",
          "Indonesian", "Italian", "Japanese", "Malaysian",
          "Pizza", "Seafood", "Sushi", "Thai", "Australian",
          "Local", "Asian", "International", "Grill/BBQ"
        ],
        openFor: ["Breakfast", "Brunch", "Lunch", "Dinner", "High tea"],
        ambiance: ["Family friendly", "Romantic"],
        dietary: ["Halal", "Vegetarian", "Gluten-free"]
      }
    },
    {
      id: 4,
      name: "The Garden Coffee Shop",
      logo: img1,
      rating: 5,
      services: {
        customer: "Good customer service",
        cuisines: [
          "Chinese", "British", "French", "Indian",
          "Indonesian", "Italian", "Japanese", "Malaysian",
          "Pizza", "Seafood", "Sushi", "Thai", "Australian",
          "Local", "Asian", "International", "Grill/BBQ"
        ],
        openFor: ["Breakfast", "Brunch", "Lunch", "Dinner", "High tea"],
        ambiance: ["Family friendly", "Romantic"],
        dietary: ["Halal", "Vegetarian", "Gluten-free"]
      }
    },
  ];

  const visibleRestaurants = restaurants.slice(currentIndex, currentIndex + 3);

  const nextSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex + 1 >= restaurants.length ? 0 : prevIndex + 1
    );
  };

  const prevSlide = () => {
    setCurrentIndex((prevIndex) =>
      prevIndex - 1 < 0 ? restaurants.length - 1 : prevIndex - 1
    );
  };

  const renderStars = (count) => {
    return Array(count)
      .fill()
      .map((_, i) => (
        <span key={i} className="text-orange text-lg">★</span>
      ));
  };

  const popularFacilities = [
    { icon: Icon1, label: "Room" },
    { icon: Icon2, label: "Free WiFi" },
    { icon: Icon4, label: "Pool with view" },
    { icon: Icon3, label: "Balcony" },
    { icon: Icon5, label: "Air conditioning" },
    { icon: Icon7, label: "Mini Bar" },
    { icon: Icon6, label: "Soundproofing" },
    { icon: Icon4, label: "Roof Top pool" },
  ];

  const facilityData = {
    "Great For your stay": [
      "Gall Face 2 Restaurant",
      "Parking",
      "Air Conditioning",
      "Private Bathroom",
      "Free WiFi",
      "SPA and Wellness Center",
      "Fitness Center",
      "Airport Shuttle",
    ],
    Bathroom: [
      "Gall Face 2 Restaurant",
      "Parking",
      "Air Conditioning",
      "Private Bathroom",
      "Free WiFi",
      "SPA and Wellness Center",
      "Fitness Center",
    ],
    Parking: [
      "Gall Face 2 Restaurant",
      "Parking",
      "Air Conditioning",
      "Private Bathroom",
      "Free WiFi",
      "SPA and Wellness Center",
      "Fitness Center",
      "Airport Shuttle",
    ],
    "Food and drink": [
      "Gall Face 2 Restaurant",
      "Parking",
      "Air Conditioning",
      "Private Bathroom",
      "Free WiFi",
    ],
    Bedroom: ["Free WiFi", "SPA and Wellness Center", "Fitness Center"],
    View: [
      "Private Bathroom",
      "Free WiFi",
      "SPA and Wellness Center",
      "Airport Shuttle",
    ],
    Internet: ["Fitness Center"],
  };

  const checkInData = [
    {
      title: "Check in",
      time: "From 14:00",
      note: "Guests are required to show a photo identification and credit card upon check-in",
    },
    {
      title: "Check in",
      time: "From 14:00",
    },
  ];

  const cancellationPolicy = {
    title: "Cancellation / Prepayment",
    note: "Cancellation and prepayment policies vary according to accommodation type. Please check what conditions may apply to each option when making your selection.",
  };

  const paymentMethods = {
    title: "Card accepted at this hotel",
    imageMap: {
      visa: Visa,
      mastercard: Master,
      amex: Amex,
    }
  };

  const childrenPolicy = {
    description:
      "Children of any age are welcome. Children 12 years and above will be charged as adults at this property.",
    note: "To see correct prices and occupancy information, please add the number of children in your group and their ages to your search.",
    policies: [
      {
        age: "0 – 2 Years",
        items: [
          { type: "Cot upon request", price: "Free" },
        ],
      },
      {
        age: "3 Years",
        items: [
          { type: "Cot upon request", price: "Free" },
          { type: "Extra Bed upon request", price: "US$ 10 per child per night" },
        ],
      },
      {
        age: "4+ Years",
        items: [
          { type: "Extra Bed upon request", price: "US$ 10 per child per night" },
        ],
      },
    ],
    disclaimer:
      "Prices for cots and extra beds are not included in the total price, and will have to be paid for separately during your stay.\n" +
      "The number of cots and extra beds allowed depends on the option you choose. Please check your selected option for more information.\n" +
      "All cots and extra beds are subject to availability."
  };

  return (
    <div className="w-full space-y-14 text-base">
      <h1 className="text-2xl text-darkBlue font-semibold">Location</h1>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-20">
        {/*  What's near by? + Restaurant */}
        <div className="space-y-10">
          <div className="space-y-4">
            <h2 className="text-base font-bold flex items-center">
              <PiPersonSimpleWalkBold className="w-5 h-5 mr-2" strokeWidth={1} /> What's near by ?
            </h2>
            <div className="space-y-2">
              {nearbyPlaces.map((place, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{place.name}</span>
                  <span>{place.distance}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-base font-bold flex items-center">
              <FaUtensils className="w-5 h-5 mr-2" strokeWidth={1} /> Restaurant
            </h2>
            <div className="space-y-2">
              {hotel.map((hotel, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{hotel.name}</span>
                  <span>{hotel.distance}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/*  Public Transport + Beach near us + Closest Airport */}
        <div className="space-y-8">
          <div className="space-y-4">
            <h2 className="text-base font-bold flex items-center">
              <FaTrain className="w-5 h-5 mr-2" strokeWidth={1} /> Public Transport
            </h2>
            <div className="space-y-2">
              {publicTransport.map((transport, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{transport.name}</span>
                  <span>{transport.distance}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-base font-bold flex items-center">
              <FaUmbrellaBeach className="w-5 h-5 mr-2" strokeWidth={1} /> Beach near us
            </h2>
            <div className="space-y-2">
              {beach.map((item, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{item.name}</span>
                  <span>{item.distance}</span>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <h2 className="text-base font-bold flex items-center">
              <IoMdAirplane className="w-5 h-5 mr-2 rotate-45" strokeWidth={1} /> Closest Airport
            </h2>
            <div className="space-y-2">
              {closestAirport.map((airport, index) => (
                <div key={index} className="flex justify-between text-sm">
                  <span>{airport.name}</span>
                  <span>{airport.distance}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Top Attraction */}
        <div className="space-y-4">
          <h2 className="text-base font-bold flex items-center">
            <PiCompass className="w-5 h-5 mr-2" strokeWidth={1} /> Top Attraction
          </h2>
          <div className="space-y-2">
            {topAttractions.map((attraction, index) => (
              <div key={index} className="flex justify-between text-sm">
                <span>{attraction.name}</span>
                <span>{attraction.distance}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className=" space-y-6">
        <h2 className="text-2xl text-darkBlue font-semibold">Restaurant
          <p className="font-light text-sm">2 Restaurant on site</p>
        </h2>

        <div className="relative">
          {/* Carousel navigation - left */}
          <button
            onClick={prevSlide}
            className="absolute left-0 top-1/2 -translate-y-1/2 -translate-x-6 w-16 h-16 bg-black/20 text-white rounded-full flex items-center justify-center z-10 shadow-md"
          >
            <ChevronLeft size={50} strokeWidth={1} />
          </button>

          {/* Carousel items */}
          <div className="flex space-x-4 px-8 overflow-visible">
            {visibleRestaurants.map((restaurant) => (
              <div key={restaurant.id} className="flex-none w-full md:w-1/3 border border-borderGray rounded-md p-6 space-y-6">
                <div className="flex items-start">
                  <img
                    src={restaurant.logo}
                    alt={restaurant.name}
                    className="w-16 h-16 rounded-full mr-4"
                  />
                  <div>
                    <h3 className="font-medium text-base text-darkBlue">{restaurant.name}</h3>
                    <div className="flex">{renderStars(restaurant.rating)}</div>
                  </div>
                </div>

                <div className="space-y-3 ">
                  <p className="font-medium text-sm">{restaurant.services.customer}</p>

                  <div>
                    <p className="text-xs font-light leading-relaxed">
                      {restaurant.services.cuisines.slice(0, 4).join(" • ")} •<br />
                      {restaurant.services.cuisines.slice(4, 8).join(" • ")} •<br />
                      {restaurant.services.cuisines.slice(8, 12).join(" • ")} •<br />
                      {restaurant.services.cuisines.slice(12).join(" • ")}
                    </p>
                  </div>

                  <div>
                    <p className="font-medium text-sm">Open For</p>
                    <p className="text-xs font-light">
                      {restaurant.services.openFor.join(" • ")}
                    </p>
                  </div>

                  <div>
                    <p className="font-medium text-sm">Ambiance</p>
                    <p className="text-xs font-light">
                      {restaurant.services.ambiance.join(" • ")}
                    </p>
                  </div>

                  <div>
                    <p className="font-medium text-sm">Dietary options</p>
                    <p className="text-xs font-light">
                      {restaurant.services.dietary.join(" • ")}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Carousel navigation - right */}
          <button
            onClick={nextSlide}
            className="absolute right-0 top-1/2 -translate-y-1/2 translate-x-12 w-16 h-16 bg-black/20 text-white rounded-full flex items-center justify-center z-10 shadow-md"
          >
            <ChevronRight size={50} strokeWidth={1} />
          </button>
        </div>
      </div>
      <div className="w-full space-y-10 text-base">
        <div className="">
          <h2 className="text-2xl text-darkBlue font-semibold">Facilities Of Hotel</h2>
          <p className="font-light text-sm">Great Facilities ! Review Score 8.1</p>
        </div>

        {/* Most Popular Facilities */}
        <div className="flex flex-wrap gap-4">
          {popularFacilities.map((item, index) => (
            <div
              key={index}
              className="flex items-center gap-2 border border-border text-sm px-3 py-1 rounded-[2px] font-light"
            >
              <img src={item.icon} alt={item.label} className="w-4 h-4" />
              {item.label}
            </div>
          ))}
        </div>

        {/* Grid Section */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-12 text-base">
          {/* First column */}
          <div className="space-y-10">
            <FacilityBlock
              title="Great For your stay"
              icon={<BedDouble className="w-5 h-5 mr-2" />}
              items={facilityData["Great For your stay"]}
            />
            <FacilityBlock
              title="Food and drink"
              icon={<Utensils className="w-5 h-5 mr-2" />}
              items={facilityData["Food and drink"]}
            />
          </div>

          {/* Second column */}
          <div className="space-y-8">
            <FacilityBlock
              title="Bathroom"
              icon={<Bath className="w-5 h-5 mr-2" />}
              items={facilityData["Bathroom"]}
            />
            <FacilityBlock
              title="Bedroom"
              icon={<Wifi className="w-5 h-5 mr-2" />}
              items={facilityData["Bedroom"]}
            />
            <FacilityBlock
              title="Internet"
              icon={<Globe className="w-5 h-5 mr-2" />}
              items={facilityData["Internet"]}
            />
          </div>

          {/* Third column */}
          <div className="space-y-10">
            <FacilityBlock
              title="Parking"
              icon={<ParkingCircle className="w-5 h-5 mr-2" />}
              items={facilityData["Parking"]}
            />
            <FacilityBlock
              title="View"
              icon={<Eye className="w-5 h-5 mr-2" />}
              items={facilityData["View"]}
            />

          </div>
        </div>
      </div>
      <div className="w-full space-y-10 text-base">
        <h1 className="text-2xl text-darkBlue font-semibold">House Rules</h1>

        {/* Check-in Section */}
        {checkInData.map((item, idx) => (
          <div
            key={idx}
            className="grid grid-cols-1 md:grid-cols-[300px_1fr] gap-4 border border-border w-full h-20 px-6 items-center"
          >
            <div className="flex items-center gap-2 font-semibold text-lg ">
              <ArrowRightFromLine className="w-5 h-5 mt-1" strokeWidth={1} />
              {item.title}
            </div>
            <div>
              <p className="text-sm items-center">{item.time}</p>
              {item.note && (
                <p className="text-sm ">{item.note}</p>
              )}
            </div>
          </div>
        ))}

        {/* Cancellation Policy */}
        <div className="grid grid-cols-1 md:grid-cols-[300px_1fr] gap-4  border border-border p-6 items-center">
          <div className="flex items-center gap-2 font-semibold text-lg">
            <Info className="w-5 h-5 mt-1" strokeWidth={1} />
            {cancellationPolicy.title}
          </div>
          <div className=" text-sm ">
            {cancellationPolicy.note}
          </div>
        </div>

        {/* Payment Methods */}
        <div className="grid grid-cols-1 md:grid-cols-[300px_1fr] gap-4 border border-border p-6 items-center">
          <div className="flex items-center gap-2 font-semibold text-lg">
            <CreditCard className="w-5 h-5 mt-1" />
            {paymentMethods.title}
          </div>
          <div className="flex gap-4">
            {Object.entries(paymentMethods.imageMap).map(([key, Icon], index) => (
              <img
                key={index}
                src={Icon}
                alt={key}
                className="h-6 object-contain"
              />
            ))}
          </div>
        </div>

        {/* Children & Beds */}
        <div className="grid grid-cols-1 md:grid-cols-[300px_1fr] gap-4 border border-border p-6 ">
          <div className="flex items-start gap-2 font-semibold text-lg">
            <Baby className="w-5 h-5 mt-1" />
            Children & Beds
          </div>
          <div className="space-y-4">
            <h1 className="text-base font-semibold">Child Policies </h1>
            <p className="text-sm font-light">{childrenPolicy.description}</p>
            <p className="text-sm font-light">{childrenPolicy.note}</p>

            <div className="space-y-4">
              <h1 className="text-base font-semibold">Cot and extra bed policies</h1>
              {childrenPolicy.policies.map((policy, idx) => (
                <div key={idx}>
                  <div className="space-y-1 border border-border py-4 px-6 rounded-[2px]">
                    <h3 className="font-medium text-base ">{policy.age}</h3>
                    <hr className="text-border"></hr>
                    {policy.items.map((item, i) => (
                      <div
                        key={i}
                        className="flex justify-between font-light items-center text-base"
                      >
                        <span>{item.type}</span>
                        <span className=" ">{item.price}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
            <p className="text-base font-light whitespace-pre-line ">
              {childrenPolicy.disclaimer}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};
const FacilityBlock = ({ title, icon, items }) => (
  <div className="space-y-4">
    <h2 className="text-base font-semibold flex items-center">
      {icon} {title}
    </h2>
    <ul className="space-y-2 text-sm ">
      {items.map((item, idx) => (
        <li key={idx} className="flex items-start gap-4">
          <Check className="text-darkGreen w-4 h-4" strokeWidth={1} />
          {item}
        </li>
      ))}
    </ul>
  </div>
);

export default LocationPage;