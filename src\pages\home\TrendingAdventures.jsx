// TrendingAdventures.jsx
import React from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import CommonCard from "../../components/ui/commonCard/CommonCard";
import { useHotelDetails } from "./api/homeApi";
import HotelImage1 from "../../assets/adventure/HotelImage1.svg";
import HotelImage2 from "../../assets/adventure/HotelImage2.svg";
import HotelImage3 from "../../assets/adventure/HotelImage3.svg";
import HotelImage4 from "../../assets/adventure/HotelImage4.svg";
import HotelImage5 from "../../assets/adventure/HotelImage5.svg";
import CityCard1 from "../../assets/cityAndTrip/CityAndTip4.svg";
import CityCard2 from "../../assets/adventure/HotelImage1.svg";
import CityCard3 from "../../assets/adventure/HotelImage4.svg";
import CityCard4 from "../../assets/adventure/HotelImage5.svg";
import CityCard5 from "../../assets/adventure/HotelImage2.svg";

const TrendingAdventures = ({ subtitle }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const selectedTab = location.state?.selectedTab || 0;

  // Define dates: today and one week later
  const today = new Date();
  const oneWeekLater = new Date(today);
  oneWeekLater.setDate(today.getDate() + 7);

  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  };

  const fromDate = formatDate(today);
  const toDate = formatDate(oneWeekLater);

  // Request body for API when hotel tab is selected
  const requestBody =
    selectedTab === 2
      ? {
          fromDate,
          toDate,
          currencyCode: "520",
          cityCode: 4644,
          hotelIds: [1047388, 90888, 43804, 1475138, 528385, 2169675, 835445],
          isNearbyCities: false,
          roomCount: 1,
          roomRequests: [
            {
              adultsCount: 2,
              childCount: 0,
              passengerNationalityCode: 81,
              passengerCountryOfResidenceCode: 72,
            },
          ],
        }
      : null;

  const { hotels, isLoading, isError } = useHotelDetails(requestBody);

  // Hardcoded adventures for other tabs (unchanged)
  const hotelflightAdventures = [
    {
      id: 1,
      type: "hotel",
      title: "Catalonia Riviera Maya",
      location: "France",
      dates: "25 may 2025-28 may 2025",
      duration: "4 nights - 2 Adults",
      package: "All Inclusive | Including Transfer",
      originalPrice: "CHF 334567",
      discountedPrice: "CHF 234567",
      discount: "30% Off",
      image: HotelImage1,
      rating: 4,
      isTop: true,
    },
    {
      id: 2,
      type: "hotel",
      title: "Catalonia Riviera Maya",
      location: "France",
      dates: "25 may 2025-28 may 2025",
      duration: "4 nights - 2 Adults",
      package: "All Inclusive | Including Transfer",
      originalPrice: "CHF 334567",
      discountedPrice: "CHF 234567",
      discount: "",
      image: HotelImage2,
      rating: 4,
      isTop: true,
    },
    {
      id: 3,
      type: "hotel",
      title: "Catalonia Riviera Maya",
      location: "France",
      dates: "25 may 2025-28 may 2025",
      duration: "4 nights - 2 Adults",
      package: "All Inclusive | Including Transfer",
      originalPrice: "CHF 334567",
      discountedPrice: "CHF 234567",
      discount: "",
      image: HotelImage3,
      rating: 0,
      isTop: false,
    },
    {
      id: 4,
      type: "hotel",
      title: "",
      location: "Rome",
      dates: "25 may 2025-28 may 2025",
      duration: "4 nights - 2 Adults",
      package: "Economy from",
      originalPrice: "",
      discountedPrice: "CHF 234567",
      discount: "",
      image: HotelImage4,
      rating: 0,
      isTop: false,
    },
    {
      id: 5,
      type: "hotel",
      title: "",
      location: "Spain",
      dates: "25 may 2025-28 may 2025",
      duration: "4 nights - 2 Adults",
      package: "Economy from",
      originalPrice: "",
      discountedPrice: "CHF 234567",
      discount: "",
      image: HotelImage5,
      rating: 0,
      isTop: false,
    },
  ];

  const flightAdventures = [
    {
      id: 1,
      type: "flight",
      title: "",
      location: "France",
      dates: "25 may 2025-28 may 2025",
      duration: "",
      package: "",
      originalPrice: "",
      discountedPrice: "CHF 234567",
      discount: "",
      image: CityCard1,
      rating: 5,
      isLimitedTime: true,
      isTrending: false,
      isTop: true,
    },
    {
      id: 2,
      type: "flight",
      title: "",
      location: "France",
      dates: "25 may 2025-28 may 2025",
      duration: "",
      package: "",
      originalPrice: "",
      discountedPrice: "CHF 234567",
      discount: "",
      image: CityCard2,
      rating: 4,
      isLimitedTime: false,
      isTrending: false,
      isTop: true,
    },
    {
      id: 3,
      type: "flight",
      title: "",
      location: "France",
      dates: "25 may 2025-28 may 2025",
      duration: "",
      package: "",
      originalPrice: "",
      discountedPrice: "CHF 234567",
      discount: "",
      image: CityCard3,
      rating: 0,
      isLimitedTime: true,
      isTrending: false,
      isTop: false,
    },
    {
      id: 4,
      type: "flight",
      title: "",
      location: "Rome",
      dates: "25 may 2025-28 may 2025",
      duration: "",
      package: "Economy from",
      originalPrice: "",
      discountedPrice: "CHF 234567",
      discount: "",
      image: CityCard4,
      rating: 0,
      isLimitedTime: false,
      isTrending: true,
      isTop: false,
    },
    {
      id: 5,
      type: "flight",
      title: "",
      location: "Spain",
      dates: "25 may 2025-28 may 2025",
      duration: "",
      package: "Economy from",
      originalPrice: "",
      discountedPrice: "CHF 234567",
      discount: "",
      image: CityCard5,
      rating: 0,
      isLimitedTime: false,
      isTrending: true,
      isTop: false,
    },
  ];

  // Define adventures based on selectedTab
  let adventures;
  if (selectedTab === 0) {
    adventures = hotelflightAdventures;
  } else if (selectedTab === 1) {
    adventures = flightAdventures;
  } else if (selectedTab === 2) {
    if (isLoading) {
      return <div className="text-center text-darkBlue">Loading hotels...</div>;
    } else if (isError) {
      return (
        <div className="text-center text-red-500">
          Error loading hotels: {isError.message}
        </div>
      );
    } else {
      adventures = hotels.map((hotel) => ({
        id: hotel.hotelId,
        type: "hotel",
        title: hotel.hotelName,
        location: `${hotel.cityData.cityName}, ${hotel.cityData.countryData.countryName}`,
        dates: `${fromDate} - ${toDate}`,
        duration: "7 nights - 2 Adults",
        package: hotel.cheapestRoomTypeName,
        originalPrice: "",
        discountedPrice: `CHF ${hotel.cheapestRoomCharge.toFixed(2)}`,
        discount: "",
        image: hotel.thumbUrl,
        rating:
          hotel.classificationCode === 563
            ? 5
            : hotel.classificationCode === 562
            ? 4
            : hotel.classificationCode === 561
            ? 3
            : 0,
        isTop: false,
        searchCriteria: {
          dateRange: {
            startDate: new Date(fromDate),
            endDate: new Date(toDate),
          },
          rooms: 1,
          adults: 2,
          children: 0,
          cityCode: 4644,
        },
      }));
    }
  }

  const displayTitle =
    selectedTab === 1
      ? "Find Your Best Fares Here"
      : selectedTab === 2
      ? "Find Your Hotels"
      : "Trending Adventures";

  // Define formData for navigation
  const formData = selectedTab === 2 ? {
    destination: "Kandy",
    cityCode: 4644,
    dateRange: {
      startDate: new Date(fromDate),
      endDate: new Date(toDate),
    },
    rooms: 1,
    adults: 2,
    children: 0,
  } : {};

  return (
    <div className="w-full flex justify-center">
      <div className="md:max-w-[80%] w-full space-y-8">
        <div className="space-y-2">
          <Link to="/TrendingPackage">
            <h2 className="text-3xl font-medium text-center text-gray">{displayTitle}</h2>
          </Link>
          <p className="text-base font-extralight text-center text-smokyGray">{subtitle}</p>
        </div>

        <div className="flex flex-col space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {adventures.slice(0, 2).map((adventure) => (
              <CommonCard adventure={adventure} key={adventure.id} navigate={navigate} />
            ))}
          </div>
          <div className="flex flex-col lg:flex-row space-y-6 lg:space-y-0 lg:space-x-6">
            <div className="w-full lg:w-1/2 grid grid-cols-1 gap-6">
              {adventures.slice(2, 3).map((adventure) => (
                <CommonCard adventure={adventure} key={adventure.id} navigate={navigate} />
              ))}
            </div>
            <div className="w-full lg:w-1/2 grid grid-cols-1 md:grid-cols-2 gap-6">
              {adventures.slice(3, 5).map((adventure) => (
                <CommonCard
                  adventure={adventure}
                  key={adventure.id}
                  small="true"
                  navigate={navigate}
                />
              ))}
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            onClick={() => navigate("/TrendingPackage", { state: { selectedTab, formData } })}
            className="px-6 py-3 font-semibold text-darkBlue border border-darkBlue rounded-full hover:bg-blue-50 hover:scale-105 transition-colors"
          >
            View more Offers
          </button>
        </div>
      </div>
    </div>
  );
};

export default TrendingAdventures;