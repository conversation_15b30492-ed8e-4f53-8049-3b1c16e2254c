import React from 'react';
import PaymentForm from '../../components/ui/PaymentForm';
import Visa from '../../assets/footer/paymentmethod/Visa.svg';
import Master from '../../assets/footer/paymentmethod/Mastercard.svg';
import Amex from '../../assets/footer/paymentmethod/Amex.svg';
import Paypal from '../../assets/footer/paymentmethod/PayPal.svg';
import Googlepay from '../../assets/footer/paymentmethod/Googlepay.svg';
import Applepay from '../../assets/footer/paymentmethod/ApplePay.svg';
import VisaLogo from '../../assets/footer/paymentmethod/Visa-logo.svg';
import AmexLogo from '../../assets/footer/paymentmethod/Amex-logo.svg';
import MasterLogo from '../../assets/footer/paymentmethod/Mastercard-logo.svg';
import PaypalLogo from '../../assets/footer/paymentmethod/PayPal-logo.svg';
import GooglepayLogo from '../../assets/footer/paymentmethod/Googlepay-logo.svg';
import ApplepayLogo from '../../assets/footer/paymentmethod/ApplePay-logo.svg';
import Vector from '../../assets/footer/paymentmethod/Vector.png';

const FlightPayment = ({ onChange }) => {
  const paymentMethods = [
    { src: Visa, alt: 'Visa' },
    { src: Master, alt: 'MasterCard' },
    { src: Amex, alt: 'Amex' },
    { src: Paypal, alt: 'PayPal' },
    { src: Googlepay, alt: 'Google Pay' },
    { src: Applepay, alt: 'Apple Pay' },
    { src: Vector, alt: 'Scan Icon' },
  ];

  const logoMethods = [
    { src: VisaLogo, alt: 'Visa Logo' },
    { src: MasterLogo, alt: 'MasterCard Logo' },
    { src: AmexLogo, alt: 'Amex logo' },
    { src: PaypalLogo, alt: 'PayPal Logo' },
    { src: GooglepayLogo, alt: 'Google Pay Logo' },
    { src: ApplepayLogo, alt: 'Apple Pay Logo' },
  ];

  return (
    <PaymentForm
      title="Flight Payment Options"
      paymentMethods={paymentMethods}
      logoMethods={logoMethods}
      onChange={onChange}
    />
  );
};

export default FlightPayment;