import React, { memo } from 'react';
import { MapPin, Star, Check } from 'lucide-react';

const StarRating = ({ count }) => (
  <>
    {[...Array(5)].map((_, i) => (
      <Star
        key={i}
        className="w-4 h-4"
        fill={i < count ? 'orange' : 'none'}
        stroke={i < count ? 'none' : 'orange'}
      />
    ))}
  </>
);

const HotelDetails = ({ bookingDetails = {} }) => {
  const {
    hotelDetails = {},
    selectedRoom = {},
    searchCriteria = {},
    nights = 1,
    dateRangeText = 'Select dates',
  } = bookingDetails;

  const {
    name = 'Unknown Hotel',
    address = 'Unknown Address',
    city = 'Unknown City',
    country = 'Unknown Country',
    rating = 9.2,
    reviewCount = 9504,
    starCount = 0,
    classificationName = 'Unrated',
    facilities = [],
  } = hotelDetails;

  const { roomTypeName = 'Standard Room', benefits = [] } = selectedRoom;
  const { adults = 2, children = 0, rooms = 1 } = searchCriteria;
  const totalSelectedRooms = selectedRoom.selectedRoomCounts
    ? Object.values(selectedRoom.selectedRoomCounts).reduce((sum, count) => sum + count, 0)
    : rooms;

  const fullAddress = `${address}, ${city}, ${country}`;

  return (
    <div className="rounded-2xl border border-darkBlue w-full p-6 bg-white shadow-sm space-y-6">
      <div className="flex flex-col md:ml-6">
        <h3 className="text-base font-semibold text-darkBlue">{name}</h3>
        <div className="flex items-center mt-2">
          <MapPin className="h-5 w-5 mr-1 text-smokyGray" strokeWidth={1} />
          <span className="text-sm text-gray-600 underline cursor-pointer">{fullAddress}</span>
        </div>
        <div className="flex items-center mt-2">
          {starCount === 0 ? (
            <span className="text-sm text-darkBlue font-medium">{classificationName}</span>
          ) : (
            <StarRating count={starCount} />
          )}
        </div>
      </div>
      <div className="flex flex-col md:ml-6">
        <h1 className="font-medium text-sm">{roomTypeName}</h1>
        <ul className="mt-0 space-y-2">
          {benefits.length > 0 ? (
            benefits.map((benefit, index) => (
              <li key={index} className="flex items-center text-sm text-darkGreen">
                <Check className="w-4 h-4 mr-2" strokeWidth={1} />
                {benefit.text}
                {benefit.condition && (
                  <span className="text-xs text-darkGreen ml-0">({benefit.condition})</span>
                )}
              </li>
            ))
          ) : (
            <li className="text-sm text-gray-600">No additional benefits included.</li>
          )}
        </ul>
      </div>
    </div>
  );
};

export default memo(HotelDetails);