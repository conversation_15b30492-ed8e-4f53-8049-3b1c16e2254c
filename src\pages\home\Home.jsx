import React, { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import TrendingAdventures from "./TrendingAdventures";
import CardAndCity from "./CardAndCity";
import HolidayPlaneCard from "./HolidayPlaneCard";
import TravelCategory from "../../components/common/travelcategory/TravelCategory";
import Feedback from "../../components/common/feedback/Feedback";
import BeachHoliday from "./BeachHoliday";
import SunnyHoliday from "./SunnyHoliday";
import HillCamping from "./HillCamping";
import HeroSection2 from "../../components/common/heroSection/HeroSection2";
import Explore from "./Explore";
import SearchForm2 from "./searchform/SearchForm2";


const Home = () => {
  const location = useLocation();
  // const selectedTab = location.state?.selectedTab || 0;
    const [selectedTab, setSelectedTab] = useState(0);

   const handleTabChange = (tabIndex) => {
    setSelectedTab(tabIndex);
  };

   const getSubheadingText = (tabIndex) => {
    switch (tabIndex) {
      case 0:
        return "BOOK TRENDING PACKAGES WITH";
      case 1:
        return "BOOK FLIGHTS WITH";
      case 2:
        return "BOOK HOTELS WITH";
      case 3:
        return "DISCOVER AMAZING TOURS WITH";
      default:
        return "BOOK FLIGHTS, HOTELS AND TRAVEL PACKAGES WITH";
    }
  };

  return (
    <>
      <div className="">
        <HeroSection2 />
        <div className=" w-full md:-mt-[700px] -mt-[600px] z-40 flex flex-col justify-center gap-4 items-center">
          <div className="text-white text-center z-40 ">
            <p className="text-sm sm:text-base font-inter font-light mb-3">
              {getSubheadingText(selectedTab)}
            </p>
            <h2 className="text-xl sm:text-4xl font-inter font-semibold">
              The Best Travel Guide
            </h2>
          </div>
          <SearchForm2 onTabChange={handleTabChange} />
        </div>
        <div className="space-y-20 md:pt-96 pt-8 mx-2 ">
          <TrendingAdventures
            subtitle="Thinking of travelling somewhere soon? Here are some options to help you get started."
          />
          <CardAndCity
            title={
              selectedTab === 2
                ? "Top Hotels in Popular Cities"
                : "Top city trips with Flight & Hotel"
            }
          />
          {selectedTab !== 2 && (
            <>
              <HolidayPlaneCard />
              <BeachHoliday />
              <SunnyHoliday />
              <HillCamping title="Hill Country Camping" />
              <TravelCategory />
              <Explore />
              <Feedback title="Feedback and Reviews" />
            </>
          )}
        </div>
      </div>
    </>
  );
};

export default Home;