import React, { useState } from "react";
import { CircleDollarSign } from "lucide-react";
import { IoCashOutline } from "react-icons/io5";
import { RiExchangeDollarLine } from "react-icons/ri";

const PriceSummary = ({ bookingDetails }) => {
  const [showDetails, setShowDetails] = useState(true);

  const { searchCriteria, selectedRoom, nights, dateRangeText, hotelDetails } = bookingDetails || {};
  const startDate = new Date(searchCriteria?.dateRange?.startDate || "2025-05-25");
  const endDate = new Date(searchCriteria?.dateRange?.endDate || "2025-05-28");
  const adults = searchCriteria?.adults || 2;
  const children = searchCriteria?.children || 0;
  const rooms = searchCriteria?.rooms || 1;

  const totalTax = selectedRoom?.totalTaxes ? parseFloat(selectedRoom.totalTaxes) : 0;
  const currency =  'CHF';
  const totalCharge = selectedRoom?.totalCharge +totalTax;

  const formatter = new Intl.DateTimeFormat("en-US", {
    weekday: "short",
    day: "2-digit",
    month: "short",
    year: "numeric",
  });

  return (
    <div className="border border-darkBlue rounded-2xl w-full bg-white shadow-sm space-y-2">
      <div className="font-semibold text-base p-4">Your booking details</div>
      <div className="flex flex-col sm:flex-row justify-stretch space-x-0 sm:space-x-6 items-start p-6">
        <div className="flex flex-col space-y-2">
          <p className="text-sm">Check-in</p>
          <p className="text-base font-medium">{formatter.format(startDate)}</p>
          <p className="text-sm font-light">14:00 – 22:00</p>
        </div>
        <div className="h-20 border-l border-border mx-4 hidden sm:block" />
        <div className="flex flex-col space-y-2 mt-4 sm:mt-0">
          <p className="text-sm">Check-out</p>
          <p className="text-base font-medium">{formatter.format(endDate)}</p>
          <p className="text-sm font-light">12:00 – 13:00</p>
        </div>
      </div>
      <div className="flex w-full items-center justify-end px-6">
        <p className="text-sm">
          {nights} {nights === 1 ? "Night" : "Nights"}, {rooms}{" "}
          {rooms === 1 ? "Room" : "Rooms"} for {adults + children}{" "}
          {adults + children === 1 ? "Adult" : "Adults"}
          {children > 0 ? `, ${children} ${children === 1 ? "Child" : "Children"}` : ""}
        </p>
      </div>

      {/* Total Price */}
      <div className="flex bg-blue-50 p-6 w-full">
        <div className="flex justify-between w-full items-center">
          <span
            className="text-2xl -mt-8 font-semibold text-darkBlue"
            style={{ fontFamily: "Inter" }}
          >
            Price
          </span>
          <div className="text-end">
            <p
              className="text-2xl font-semibold text-darkBlue"
              style={{ fontFamily: "Inter" }}
            >
              {currency} {totalCharge.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}
            </p>
            <div
              className="text-sm text-red font-normal"
              style={{ fontFamily: "Inter" }}
            >
              Includes {currency} {totalTax.toFixed(2)} in taxes and charges
            </div>
          </div>
        </div>
      </div>
      <div className="space-y-3 p-6">
        <p className="font-bold text-base">Price information</p>
        <div className="mt-2 space-y-4 text-smokyGray text-sm">
          {/* <div className="flex items-start space-x-4">
            <IoCashOutline className="w-5 h-5 mt-1" />
            <div className="space-y-1">
              <p>
                Includes {currency}{" "}
                {totalTax.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")} in taxes
                and charges
              </p>
              {showDetails && (
                <div className="text-xs text-red">
                  <p>Taxes and Charges: {currency} {totalTax.toFixed(2)}</p>
                </div>
              )}
            </div>
          </div> */}
          <div className="flex items-start space-x-4">
            <CircleDollarSign className="w-9 h-9 mt-1" />
            <p>
              This price is converted to show you the <br />
              approximate cost in {currency}. You'll pay in <strong>USD</strong>.
              The exchange rate may change before you pay.
            </p>
          </div>
          <div className="flex items-start space-x-4">
            <RiExchangeDollarLine className="w-5 h-5 mt-1" />
            <p>
              Bear in mind that your card issuer may charge you <br />a foreign
              transaction fee.
            </p>
          </div>
        </div>
        <button
          type="button"
          className="text-darkBlue text-sm hover:bg-sky-100 px-2 py-1 mt-3"
          onClick={() => setShowDetails((prev) => !prev)}
        >
          {showDetails ? "Hide details" : "Show details"}
        </button>
      </div>
    </div>
  );
};

export default PriceSummary;