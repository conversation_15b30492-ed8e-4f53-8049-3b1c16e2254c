import { useState } from 'react'
import hotelData from './hotel_search_response.json'
import React from 'react'

// Interfaces here...
interface Hotel {
  hotelid: number;
  rooms: Room[];
}

interface Room {
  adults: number;
  children: number;
  extrabeds: number;
  childrenages?: number;
  roomTypes: RoomType[];
}

interface RoomType {
  roomtypecode: number;
  name: string;
  rateBases: RateBasis[];
}

interface RateBasis {
  id: number;
  currencyid: number;
  rateType: number;
  total: number;
}

export default function Test2() {
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null)

  const rawHotels = hotelData.result.hotels?.hotel
  const hotelArray = Array.isArray(rawHotels) ? rawHotels : [rawHotels]

  const transformedHotels: Hotel[] = hotelArray.map(hotel => ({
    hotelid: parseInt(hotel["@hotelid"]),
    rooms: Array.isArray(hotel.rooms.room) ? hotel.rooms.room.map(room => ({
      adults: parseInt(room["@adults"]),
      children: parseInt(room["@children"]),
      extrabeds: parseInt(room["@extrabeds"]),
      childrenages: room["@childrenages"] ? parseInt(room["@childrenages"]) : undefined,
      roomTypes: Array.isArray(room.roomType) ? room.roomType.map(rt => ({
        roomtypecode: parseInt(rt["@roomtypecode"]),
        name: rt.name,
        rateBases: Array.isArray(rt.rateBases.rateBasis) ? rt.rateBases.rateBasis.map(rate => ({
          id: parseInt(rate["@id"]),
          currencyid: parseInt(rate.rateType["@currencyid"]),
          rateType: parseInt(rate.rateType["#text"]),
          total: parseFloat(rate.total)
        })) : []
      })) : []
    })) : []
  }))

  const filteredHotels = transformedHotels.filter(hotel =>
    hotel.hotelid.toString().includes(searchQuery) ||
    hotel.rooms.some(room =>
      room.roomTypes.some(rt =>
        rt.name.toLowerCase().includes(searchQuery.toLowerCase())
      )
    )
  )

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Back button */}
        {selectedHotel && (
          <button
            onClick={() => setSelectedHotel(null)}
            className="mb-4 text-blue-600 hover:underline"
          >
            ← Back to Hotel List
          </button>
        )}

        {/* Search */}
        {!selectedHotel && (
          <div className="mb-8">
            <input
              type="text"
              placeholder="Search hotels by ID or room name..."
              className="w-full p-4 rounded-lg shadow-sm border border-gray-300"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        )}

        {/* Conditional Views */}
        <div className="grid gap-6">
          {!selectedHotel ? (
            filteredHotels.map((hotel) => (
              <HotelSummaryCard key={hotel.hotelid} hotel={hotel} onClick={() => setSelectedHotel(hotel)} />
            ))
          ) : (
            <HotelDetailCard hotel={selectedHotel} />
          )}
        </div>
      </div>
    </div>
  )
}

// Hotel Summary (like Booking.com search page)
function HotelSummaryCard({ hotel, onClick }: { hotel: Hotel, onClick: () => void }) {
  return (
    <div className="bg-white rounded-xl shadow-md p-6 cursor-pointer hover:shadow-lg transition" onClick={onClick}>
      <h2 className="text-xl font-semibold text-blue-800 mb-2">Hotel ID: {hotel.hotelid}</h2>
      <p className="text-gray-600">Rooms Available: {hotel.rooms.length}</p>
    </div>
  )
}

// Hotel Detail (like Booking.com individual hotel page)
function HotelDetailCard({ hotel }: { hotel: Hotel }) {
  return (
    <div className="bg-white rounded-xl shadow-md overflow-hidden">
      <div className="p-6 bg-blue-50 border-b border-blue-100">
        <h2 className="text-xl font-semibold text-blue-800">
          Hotel ID: {hotel.hotelid}
        </h2>
      </div>

      <div className="p-6">
        {hotel.rooms.map((room, index) => (
          <div key={index} className="mb-6">
            <h3 className="text-gray-700 font-medium mb-2">
              Room {index + 1}: {room.adults} Adults, {room.children} Children
              {room.childrenages && ` (Age ${room.childrenages})`}
            </h3>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {room.roomTypes.map((roomType) => (
                <div
                  key={roomType.roomtypecode}
                  className="p-4 border rounded-lg bg-gray-50 hover:bg-white"
                >
                  <h4 className="font-semibold text-red mb-2">
                    {roomType.name}
                  </h4>
                  <div className="space-y-1">
                    {roomType.rateBases.map((rate) => (
                      <div key={rate.id} className="text-sm text-gray-600 flex justify-between">
                        <span>Rate Type {rate.rateType}</span>
                        <span>${rate.total.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

