import React, { useEffect, useMemo } from 'react';
import { useParams, useLocation, Link } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchHotelDetails, fetchRoomAvailability } from '../../../store/hotelSlice';
import WellcomSection from './WellcomSection';
import Booking from './bookingSection/Booking';
import SearchForm2 from '../../home/<USER>/SearchForm2';
import { HotelProvider } from '../context/HotelContext';

const HotelAndFlightView = () => {
  const { hotelId } = useParams();
  const location = useLocation();
  const dispatch = useDispatch();
  const searchCriteria = location.state?.formData || JSON.parse(localStorage.getItem('searchCriteria') || '{}');
  const { hotelDetails, roomData, hotelDetailsLoading, roomLoading, hotelDetailsError, roomError } = useSelector((state) => state.hotels);

  const requestBody = useMemo(() => ({
    fromDate: searchCriteria.dateRange?.startDate.toISOString().split('T')[0],
    toDate: searchCriteria.dateRange?.endDate.toISOString().split('T')[0],
    currencyCode: '394',
    ratingValue: -1,
    minPriceAmount: 0,
    maxPriceAmount: 100000,
    roomCount: parseInt(searchCriteria.rooms) || 1,
    hotelId: parseInt(hotelId),
    roomRequests: [{
      adultsCount: parseInt(searchCriteria.adults) || 2,
      childCount: parseInt(searchCriteria.children) || 0,
      rateBasisCode: -1,
      passengerNationalityCode: 81,
      passengerCountryOfResidenceCode: 72,
    }],
  }), [hotelId, searchCriteria]);

  useEffect(() => {
    localStorage.setItem('searchCriteria', JSON.stringify(searchCriteria));
    dispatch(fetchHotelDetails(hotelId));
    dispatch(fetchRoomAvailability(requestBody));
  }, [dispatch, hotelId, requestBody]);

  if (!searchCriteria.dateRange?.startDate || !searchCriteria.dateRange?.endDate) {
    return (
      <div className="text-center text-red p-4">
        Please select valid check-in and check-out dates.
        <Link to="/" className="underline ml-2">Return to Search</Link>
      </div>
    );
  }

  if (hotelDetailsLoading || roomLoading) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-offWhite">
        <div className="text-center text-darkBlue">
          <h3 className="text-lg font-semibold">Room Availability...</h3>
          <p className="text-sm mt-2">Please wait while we process your request.</p>
        </div>
      </div>
    );
  }

  if (hotelDetailsError || roomError) {
    return (
      <div className="text-center text-red-500 p-4">
        Error: {hotelDetailsError || roomError}
        <button onClick={() => { dispatch(fetchHotelDetails(hotelId)); dispatch(fetchRoomAvailability(requestBody)); }} className="ml-2 underline">Retry</button>
      </div>
    );
  }

  if (!hotelDetails || !roomData) {
    return <div className="text-center text-darkBlue p-4">No data available</div>;
  }

  const facilities = [
    ...(hotelDetails.facilitiesResponse?.amenitieResponses?.map(item => ({ label: item.amenitieItem })) || []),
    ...(hotelDetails.facilitiesResponse?.businessResponses?.map(item => ({ label: item.businessName })) || []),
    ...(hotelDetails.facilitiesResponse?.leisureResponses?.map(item => ({ label: item.leisureName })) || []),
  ];

  const combinedHotelDetails = {
    id: hotelDetails.hotelId,
    name: hotelDetails.hotelName,
    city: hotelDetails.cityData?.cityName,
    country: hotelDetails.cityData?.countryData?.countryName,
    address: hotelDetails.hotelStreetAddress,
    description: hotelDetails.description1,
    description2: hotelDetails.description2,
    rating: hotelDetails.ratingCode ? (hotelDetails.ratingCode / 100).toFixed(1) : 9.2,
    reviewCount: hotelDetails.reviewCount || 0,
    images: hotelDetails.hotelImages?.map(img => img.url) || [],
    geoLat: parseFloat(hotelDetails.geoLat),
    geoLong: parseFloat(hotelDetails.geoLong),
    classificationBody: hotelDetails.classificationBody,
    classificationCode: hotelDetails.classificationCode,
    facilities,
    rooms: roomData[0]?.roomTypes || [],
    hotelPhone: hotelDetails.hotelPhone,
    checkInTime: hotelDetails.checkInTime,
    checkOutTime: hotelDetails.checkOutTime,
  };

  return (
    <HotelProvider hotelDetails={combinedHotelDetails} searchCriteria={searchCriteria}>
      <div className="flex justify-center md:px-8">
        <div className="w-full md:space-y-12 space-y-4">
          <div className="w-full z-10 flex justify-center p-4">
            <SearchForm2 initialData={searchCriteria} />
          </div>
          <div className="w-full md:max-w-[80%] mx-auto md:space-y-10">
            <WellcomSection />
            <Booking />
          </div>
        </div>
      </div>
    </HotelProvider>
  );
};

export default HotelAndFlightView;