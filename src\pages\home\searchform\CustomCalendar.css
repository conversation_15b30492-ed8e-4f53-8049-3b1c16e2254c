/* General Calendar */
.custom-calendar {
  border: none !important;
  background-color: white !important;
  width: 100% !important;
  font: Arial, sans-serif !important;
  border-radius: 2px !important;
}

/* Custom Styling for Hover Effect */
.react-calendar__tile--hover {
  background: #f0faff !important;
  border-radius: 2px !important;
}

.react-calendar {
  border: none !important;
  box-shadow: none !important;
}

.react-calendar__tile {
  background: none !important;
  border-radius: 2px !important;
  font-size: 12px;
  font-weight: lighter;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* Custom hover styling when selecting range */
.react-calendar__tile--hover:not(.react-calendar__tile--active) {
  background: #ddedff !important;
 
}

/* Custom Disabled Dates */
.react-calendar__tile:disabled {
  color: #838282 !important;
  background: none !important;
  cursor: not-allowed;
}

/* Remove hover effect for disabled dates */
.react-calendar__tile:disabled:hover {
  background: #f0f0f0 !important;
}

/* Custom navigation styles */
.custom-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0px 8px;
  font-size: 16px;
  margin-bottom: 10px;
}

.custom-navigation button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #5A5A5A;
  transition: all 0.2s ease-in-out;
}

.custom-navigation button:hover {
  background-color: #f7f9fa;
  border-radius: 2px;
  color: #002b45;
}

/* Year dropdown styling */
.year-dropdown {
  max-height: 200px;
  overflow-y: auto;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.year-dropdown div {
  padding: 8px 16px;
  transition: background-color 0.2s ease;
}

.year-dropdown div:hover {
  background-color: #f0f0f0;
}

.react-calendar__month-view__days {
  border: none !important;
  display: grid !important;
  grid-template-columns: repeat(7, 1fr);
  row-gap: 6px; 
}


/* Remove dot underline from week labels */
.react-calendar__month-view__weekdays abbr {
  text-decoration: none !important;
  font-size: 12px;
  font-weight: lighter;
  color: #6b6868;
  border: none !important;
}

/* Change weekend text color */
.react-calendar__month-view__weekdays__weekday:nth-child(7) abbr,
.react-calendar__month-view__weekdays__weekday:nth-child(6) abbr {
  color: rgb(205, 4, 4) !important;
}

/* Change weekend background color in calendar */
.react-calendar__month-view__days__day:nth-child(7n),
.react-calendar__month-view__days__day:nth-child(7n-1) {
  background-color: none !important;
}

/* Custom style for the selected departure date tile */
.react-calendar__tile.selected-departure {
  background-color: #024575 !important;
  color: #ffffff !important;
   border-radius: 6px !important;
}

/* Selected date styling */
.react-calendar__tile--active {
  background: #024575 !important;
  color: white !important;
  border-radius: 0 0 0 0 !important;
}

/* Special styling for active date when in range */
.react-calendar__tile--active.react-calendar__tile--range:not(.react-calendar__tile--rangeStart):not(.react-calendar__tile--rangeEnd) {
  background:#E6EDF2 !important;
  color: #02446f !important;
  border-radius: 6px !important;
}

/* Custom Styling for Selected Range */
.react-calendar__tile--range {
  background: #E6EDF2 !important;
  color: #024575 !important;
  border-radius: 0 0 0 0 !important; 
  position: relative;
}

/* Custom Styling for Start Date */
.react-calendar__tile--rangeStart {
  background: #024575 !important; 
  color: white !important;
  border-radius: 6px !important; 
  z-index: 1;
}

/* Custom Styling for End Date */
.react-calendar__tile--rangeEnd {
  background: #024575 !important; 
  color: white !important;
  border-radius:6px !important; 
  z-index: 1;
}

/* Today date styling */
.react-calendar__tile--now {
  position: relative;
  background: none !important;
  border-radius: 2px !important;
  color: #000 !important;
}

.react-calendar__tile--now::after {
  content: "";
  position: absolute;
  bottom: 6px; /* distance from text */
  left: 30%; /* start 25% into the tile */
  width: 40%; /* 50% width underline */
  height: 1px; /* thickness of underline */
  background-color: #024575; /* underline color */
  border-radius: 2px; /* radius = rounded underline ends */
}

.react-calendar__tile--hover.react-calendar__tile--range {
  background: #045f9f !important;
  border-radius: 6px !important;
}

/* Hover state for dates in range selection */
.react-calendar__tile--hasActive {
  background: #0c4a84 !important;
  color: white !important;
  border-radius: 6px !important; 
}

/* Special styling for tiles when hovering over them during range selection */
.react-calendar__tile--hover.react-calendar__tile--range {
  background: #045f9f !important;
  border-radius: 6px !important; 
}