import React, { useState } from "react";
import LoadingPage from "./LoadingPage";
import LoadingPage1 from "./LoadingPage1";
import LoadingPage2 from "./LoadingPage2";
import LoadingPage3 from "./LoadingPage3";
import LoadingPage4 from "./LoadingPage4";


const MainLoadingPage = () => {
  const [activePage, setActivePage] = useState(1);

  const renderLoadingPage = () => {
    switch (activePage) {
      case 1:
        return <LoadingPage />;
      case 2:
        return <LoadingPage1 />;
      case 3:
        return <LoadingPage2 />;
      case 4:
        return <LoadingPage3 />;
        case 5:
        return <LoadingPage4 />;
      default:
        return <LoadingPage />;
    }
  };

  return (
    <div className="min-h-screen bg-white flex items-center justify-center">
      {/* Render the selected loading page */}
      {renderLoadingPage()}

      {/* Buttons to switch pages */}
      <div className="absolute bottom-6 flex space-x-4">
        {[1, 2, 3, 4, 5].map((num) => (
          <button
            key={num}
            onClick={() => setActivePage(num)}
            className={`px-4 py-2 rounded-md text-white font-semibold transition-all 
              ${activePage === num ? "bg-blue-600" : "bg-gray hover:bg-gray-500"}`}
          >
            Page {num}
          </button>
        ))}
      </div>
    </div>
  );
};

export default MainLoadingPage;
