import React, { useState, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { fetchHotelIdsByCity, fetchFilterCounts, fetchFilteredHotels, clearHotels } from '../../store/hotelSlice';
import PackageFilter from '../packages/PackageFilter';
import debounce from 'lodash/debounce';
import HotelList from './HotelList';
import HotelLoading from '../trendingadventure/HotelLoading';
import SearchForm from '../home/<USER>/SearchForm';

const TopCityPackage = () => {
  const [isFilterVisible, setFilterVisible] = useState(false);
  const [selectedTags, setSelectedTags] = useState([]);
  const [priceRange, setPriceRange] = useState([1, 1000]);
  const location = useLocation();
  const dispatch = useDispatch();
  const {
    hotels,
    totalHotels,
    filteredHotels,
    totalFilteredHotels,
    filterCounts,
    loading,
    error,
    filterLoading,
    filterError,
    filteredHotelsLoading,
    filteredHotelsError,
  } = useSelector((state) => state.hotels);
  const searchCriteria = location.state?.formData || {};

  // Log Redux state for debugging
  useEffect(() => {
    console.log('Redux State:', { hotels, filteredHotels, totalHotels, totalFilteredHotels });
  }, [hotels, filteredHotels, totalHotels, totalFilteredHotels]);

  // Generate room requests
  const generateRoomRequests = (roomCount, totalAdults, totalChildren) => {
    const roomRequests = [];
    const adults = totalAdults || 2;
    const children = totalChildren || 0;
    const rooms = Math.max(1, roomCount || 1);

    for (let i = 0; i < rooms; i++) {
      const adultsPerRoom =
        Math.floor(adults / rooms) + (i < adults % rooms ? 1 : 0);
      const childrenPerRoom =
        Math.floor(children / rooms) + (i < children % rooms ? 1 : 0);
      roomRequests.push({
        adultsCount: Math.max(1, adultsPerRoom),
        childCount: childrenPerRoom,
        rateBasisCode: -1,
        passengerNationalityCode: '81',
        passengerCountryOfResidenceCode: '72',
      });
    }
    return roomRequests;
  };

  // Prepare request body for getSavedAvailableHotelDetailsByCity
  const requestBody = useMemo(() => {
    if (!searchCriteria.cityCode || !searchCriteria.dateRange?.startDate || !searchCriteria.dateRange?.endDate) {
      console.warn('Invalid search criteria:', searchCriteria);
      return null;
    }
    return {
      fromDate: searchCriteria.dateRange.startDate.toISOString().split('T')[0],
      toDate: searchCriteria.dateRange.endDate.toISOString().split('T')[0],
      currencyCode: '520',
      cityCode: searchCriteria.cityCode,
      isNearbyCities: true,
      ratingValue: -1,
      minPriceAmount: 0,
      maxPriceAmount: 1000000,
      roomCount: searchCriteria.rooms || 1,
      roomRequests: generateRoomRequests(
        searchCriteria.rooms,
        searchCriteria.adults,
        searchCriteria.children
      ),
    };
  }, [searchCriteria]);

  // Fetch hotels
  useEffect(() => {
    if (requestBody) {
      dispatch(fetchHotelIdsByCity(requestBody));
    }
    return () => {
      dispatch(clearHotels());
    };
  }, [dispatch, requestBody]);

  // Fetch filter counts when hotels change
  useEffect(() => {
    if (hotels.length > 0) {
      const hotelIds = hotels.map((hotel) => hotel.hotelId);
      dispatch(fetchFilterCounts(hotelIds));
    }
  }, [dispatch, hotels]);

  // Debounced function to fetch filtered hotels
  const debouncedFetchFilteredHotels = useMemo(
    () =>
      debounce((filterRequest) => {
        console.log('Fetching filtered hotels with request:', filterRequest);
        dispatch(fetchFilteredHotels(filterRequest));
      }, 500),
    [dispatch]
  );

  // Fetch filtered hotels when filters are applied
  useEffect(() => {
    if (hotels.length === 0) return;

    const facilityFilterRequest = {
      amenitieIds: selectedTags
        .filter((tag) => tag.type === 'amenities')
        .map((tag) => tag.id),
      leisureIds: selectedTags
        .filter((tag) => tag.type === 'leisure')
        .map((tag) => tag.id),
      businessIds: selectedTags
        .filter((tag) => tag.type === 'business')
        .map((tag) => tag.id),
    };

    const classificationCodes = selectedTags
      .filter((tag) => tag.type === 'classification')
      .map((tag) => tag.id);

    const filterRequest = {
      hotelIds: hotels.map((hotel) => hotel.hotelId),
      hotelName: '',
      classificationCode: classificationCodes[0] || null,
      minCharge: priceRange[0],
      maxCharge: priceRange[1],
      facilityFilterRequest: {
        amenitieIds: facilityFilterRequest.amenitieIds,
        leisureIds: facilityFilterRequest.leisureIds,
        businessIds: facilityFilterRequest.businessIds,
      },
    };

    console.log('Applying filters:', filterRequest);
    debouncedFetchFilteredHotels(filterRequest);

    return () => debouncedFetchFilteredHotels.cancel();
  }, [hotels, selectedTags, priceRange, debouncedFetchFilteredHotels, dispatch]);

  const toggleTag = (label, id, type) => {
    setSelectedTags((prev) => {
      if (type === 'classification') {
        const otherTags = prev.filter((tag) => tag.type !== 'classification');
        const tagExists = prev.some((tag) => tag.label === label && tag.type === 'classification');
        if (tagExists) {
          return otherTags;
        }
        return [...otherTags, { label, id, type }];
      }
      const tagExists = prev.some((tag) => tag.label === label);
      if (tagExists) {
        return prev.filter((tag) => tag.label !== label);
      }
      return [...prev, { label, id, type }];
    });
  };

  const handlePriceRangeChange = (newValues) => {
    setPriceRange(newValues);
  };

  const toggleFilterSidebar = () => {
    setFilterVisible(!isFilterVisible);
  };

  if (!searchCriteria.cityCode) {
    return (
      <div className="w-full flex justify-center py-6">
        <div className="w-full max-w-[1100px] px-4 lg:px-0 space-y-6">
        <SearchForm initialData={searchCriteria} selectedTab={2} />

          <div className="text-center text-darkBlue">
            Please select a valid destination.
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full flex justify-center py-6">
      <div className="w-full max-w-[1100px] px-4 lg:px-0 space-y-6">
      <SearchForm initialData={searchCriteria} selectedTab={2} />

        <div className="flex flex-col space-y-6">
          <div className="text-left">
            <button
              className="text-sm md:text-base text-darkBlue mt-2 cursor-pointer underline"
              onClick={toggleFilterSidebar}
            >
              Filter
            </button>
          </div>
          <div className="flex flex-col lg:flex-row lg:gap-10">
            <div
              className={`fixed inset-0 md:static w-[460px] overflow-y-auto no-scrollbar mt-11 bg-white p-6 md:p-0 z-40 shadow-md md:shadow-none transform ${
                isFilterVisible ? 'translate-x-0' : '-translate-x-full'
              } transition-transform duration-300 ease-in-out lg:translate-x-0`}
            >
              <button
                className="md:hidden absolute top-8 right-11 text-gray text-lg"
                onClick={toggleFilterSidebar}
              >
                ✕
              </button>
              <PackageFilter
                selectedTags={selectedTags}
                onToggleTag={toggleTag}
                filterCounts={filterCounts}
                filterLoading={filterLoading}
                filterError={filterError}
                priceRange={priceRange}
                onPriceRangeChange={handlePriceRangeChange}
              />
            </div>
            <div className="flex-1">
              {loading ? (
                <HotelLoading />
              ) : filterLoading || filteredHotelsLoading ? (
                <div className="text-center text-darkBlue">Loading...</div>
              ) : error || filteredHotelsError ? (
                <div className="text-center text-red">
                  Error: {error || filteredHotelsError}{' '}
                  <button
                    className="underline"
                    onClick={() => requestBody && dispatch(fetchHotelIdsByCity(requestBody))}
                  >
                    Retry
                  </button>
                </div>
              ) : !Array.isArray(filteredHotels) || filteredHotels.length === 0 ? (
                <div className="text-center text-darkBlue">No hotels found</div>
              ) : (
                <HotelList
                  hotels={filteredHotels}
                  totalHotels={totalFilteredHotels}
                  searchCriteria={searchCriteria}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TopCityPackage;