// import axios from 'axios';
// import React, { useEffect, useState } from 'react'

// function Test() {

//     const [result,setResult] = useState([]);

//     const getFunction = async ()=>{
       
//         const res = await axios.get("https://backend.graycorp.io:9100/eflyer-bookings/api/v1/getFlights",{
//             params: {
//                 "dep_date" : "2025-03-05",
//                 "des_date" : "2025-03-30",
//                 "dep_apt" : "CMB",
//                 "des_apt" : "LON"
//               },
//         })

//         setResult(res.data.contents.flight_Availability_Details)
//     }
// useEffect(() => {
//   getFunction()
// }, [])

// console.log(result)
//   return (
//     <div>
//        {result["BA"] && result["BA"].length > 0 ? (
//         result["BA"].map(res => (
//           <div key={res.tarifId}>
//             <div>Tarif Id: {res.tarifId}</div>
//             <div>Airways: {res.airways}</div>
//             <div>Price Adt: {res.price_per_adtSell}</div>
//             {res.fareDTOList && res.fareDTOList.length > 0 ? (
//               res.fareDTOList.map(fare => (
//                 <div key={fare.fareId}>
//                   <div>FareId: {fare.fareId}</div>
//                 </div>
//               ))
//             ) : (
//               <div>No fares available</div>
//             )}
//           </div>
//         ))
//       ) : (
//         <div>Loading flights...</div>
//       )}
//     </div>
//   )
// }

// export default Test

import { Button } from '@mui/material'
import axios from 'axios'
import React from 'react'
import { useState } from 'react'
import useSwR from "swr"

const api = axios.create({
    baseURL: "https://backend.graycorp.io:9100/eflyer-bookings/api/v1"
})

const dep_date = "2025-03-01"
const des_date = "2025-03-31"
const dep_apt = "CMB"
const des_apt = "LHR"

const fetcher = (url: string,params: { dep_date: string; des_date: string; dep_apt: string; des_apt: string })=> api.get(url,{params})
                .then((res)=> Object.create(res.data.contents.flight_Availability_Details))

const Test = () => {
    
const {data,error,isLoading} = useSwR(["/getFlights",{
    "dep_date":dep_date,
    "des_date":des_date,
    "dep_apt":dep_apt,
    "des_apt":des_apt}],([url,params]) => fetcher(url,params));

    const [airways,setAirways] = useState("BA");

if (isLoading) return <p>Loading...</p>;
  else if (error) return <p>Error: {error.message}</p>;

 else return (
    <div className='flex flex-col justify-start items-start py-10 px-10 gap-10'>
    <div className='flex justify-evenly w-10/12'> 
    <Button variant="contained"  onClick={()=>setAirways("BA")}> BA </Button>
    <Button variant="contained"  onClick={()=>setAirways("QR")}> QR </Button>
    <Button variant="contained"  onClick={()=>setAirways("EY")}> EY </Button>
    <Button variant="contained"  onClick={()=>setAirways("EK")}> EK </Button> 
    </div>
    {data.map((tarif: { tarifId: string | number | boolean | React.ReactElement<any, string | React.JSXElementConstructor<any>> | Iterable<React.ReactNode> | React.ReactPortal | null | undefined; fareDTOList: any[] },index: React.Key | null | undefined)=>(
      <div id='tarif' key={index} className='bg-slate-200 flex flex-col justify-start p-5 w-7/12'>
     <h1 className='font-medium text-lg'>Tarif Id:- {tarif.tarifId}</h1>
     <div className='h-5'></div>
     <div className='flex flex-col justify-around'>
     {tarif.fareDTOList.map((fareDTO) => (
      <div key={fareDTO.fareId} className='flex flex-col justify-between'>
      <div className='flex justify-between py-5'>
      <h1>FareId:- {fareDTO.fareId}</h1>
      <h1>Dep Airport:- {fareDTO.departure_airport_code}</h1>
      <h1>Dest Airport:- {fareDTO.destination_airport_code}</h1>
      <h1>Ticket Dead Line:- {fareDTO.ticket_deadLine_date}</h1>
      </div>
      {fareDTO.flightDTOS.map((flightDTO) => (
        <div key={flightDTO.flightId}>
        <h1>Flight:- {flightDTO.flightId}</h1>
        <h1>{flightDTO.flightId.legDTOS}</h1>
        <div className='h-10'></div>
        <div>
        <h1 className='text-base font-medium'> Leg Details </h1>
          {flightDTO.legDTOS.map((legDTO) => (
            <h1 key={legDTO.legId}>{legDTO.legId}</h1>
          ))}
        </div>
        </div>
      ))}
      </div>
     ))}
     </div>
    </div>
    ))}
    </div>
  )
}

export default Test

