import React, { useEffect, useRef, useState } from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import { ChevronLeft, ChevronRight, ChevronDown } from "lucide-react";
import "./CustomCalendar.css";

const HotelCalendarComponent2 = ({ calClose, setFieldValue, values, dateType, onAfterConfirm }) => {
  const calendarRef = useRef(null);
  const [flexibleDays, setFlexibleDays] = useState(values.flexibleDays || 0);
  const [selectedDate, setSelectedDate] = useState(
    dateType === "checkIn" && values.checkInDate
      ? new Date(values.checkInDate)
      : dateType === "checkOut" && values.checkOutDate
        ? new Date(values.checkOutDate)
        : new Date()
  );
  const [currentDate, setCurrentDate] = useState(
  dateType === "checkIn" && values.checkInDate
    ? new Date(values.checkInDate)
    : dateType === "checkOut" && values.checkOutDate
      ? new Date(values.checkOutDate)
      : new Date()
);
  const [showYearDropdown, setShowYearDropdown] = useState(false);

  const formatLocalDate = (date) => {
    if (!date || isNaN(date.getTime())) return "";
    const year = date.getFullYear();
    const month = (`0${date.getMonth() + 1}`).slice(-2);
    const day = (`0${date.getDate()}`).slice(-2);
    return `${year}-${month}-${day}`;
  };

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const minDate =
    dateType === "checkIn"
      ? today
      : values.checkInDate
        ? new Date(values.checkInDate)
        : today;

  const handleDateSelect = (value) => {
    const newDate = new Date(value);
    setSelectedDate(newDate);
    if (dateType === "checkIn") {
      setFieldValue("checkInDate", formatLocalDate(newDate));
      
    } else {
      setFieldValue("checkOutDate", formatLocalDate(newDate));
    }
    calClose();
    if (dateType === "checkIn" && onAfterConfirm) {
      onAfterConfirm();
    }
  };

  const handleMonthChange = (date) => {
    setCurrentDate(date);
  };

  const customNavigation = () => {
    return (
      <div className="custom-navigation flex justify-between items-center mb-4">
        <div className="flex items-center justify-center space-x-4">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const newDate = new Date(currentDate);
              newDate.setMonth(newDate.getMonth() - 1);
              setCurrentDate(newDate);
              handleMonthChange(newDate);
            }}
            className="p-1 hover:bg-gray-100 rounded-full transition duration-200"
          >
            <ChevronLeft size={20} className="text-[#6b6868]" />
          </button>
          <span className="text-base font-medium text-[#6b6868] w-24 text-center capitalize">
            {currentDate.toLocaleString("default", { month: "long" })}
          </span>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const newDate = new Date(currentDate);
              newDate.setMonth(newDate.getMonth() + 1);
              setCurrentDate(newDate);
              handleMonthChange(newDate);
            }}
            className="p-1 hover:bg-gray-100 rounded-full transition duration-200"
          >
            <ChevronRight size={20} className="text-[#6b6868]" />
          </button>
        </div>
        <div className="relative">
          <div
            className="flex items-center cursor-pointer year-selector"
            onClick={() => setShowYearDropdown(!showYearDropdown)}
          >
            <span className="text-base font-normal mr-1 text-[#6b6868]">
              {currentDate.getFullYear()}
            </span>
            <ChevronDown size={18} className="text-[#6b6868]" />
          </div>
          {showYearDropdown && (
            <div className="year-dropdown absolute right-0 mt-1 bg-white border border-gray rounded-lg shadow-lg z-30 max-h-48 overflow-y-auto">
              {Array.from({ length: 10 }, (_, i) => {
                const year = new Date().getFullYear() + i;
                return (
                  <div
                    key={year}
                    className="px-4 py-2 hover:bg-gray cursor-pointer"
                    onClick={() => {
                      const newDate = new Date(currentDate);
                      newDate.setFullYear(year);
                      setCurrentDate(newDate);
                      setShowYearDropdown(false);
                    }}
                  >
                    {year}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        calClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [calClose]);

  useEffect(() => {
    const handleClickOutsideDropdown = (event) => {
      if (
        showYearDropdown &&
        !event.target.closest(".year-dropdown") &&
        !event.target.closest(".year-selector")
      ) {
        setShowYearDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutsideDropdown);
    return () => document.removeEventListener("mousedown", handleClickOutsideDropdown);
  }, [showYearDropdown]);

  useEffect(() => {
    if (dateType === "checkOut" && values.checkInDate) {
      const checkIn = new Date(values.checkInDate);
      setCurrentDate(checkIn);
    }
  }, [dateType, values.checkInDate]);

  return (
    <div
      className="absolute w-full md:w-96 z-20 bg-white rounded-[8px] flex flex-col p-4 space-y-4 mt-2"
      ref={calendarRef}
      onClick={(e) => e.stopPropagation()}
    >
      {customNavigation()}
      <Calendar
        onChange={handleDateSelect}
        value={selectedDate}
        activeStartDate={currentDate}
        onActiveStartDateChange={({ activeStartDate }) =>
          handleMonthChange(activeStartDate)
        }
        showNavigation={false}
        minDate={minDate}
        formatShortWeekday={(locale, date) =>
          date.toLocaleDateString(locale, { weekday: "short" }).slice(0, 2)
        }
        tileClassName={({ date, view }) => {
          if (view !== "month") return null;

          const d = new Date(date);
          d.setHours(0, 0, 0, 0);

          const checkIn = values.checkInDate ? new Date(values.checkInDate) : null;
          const checkOut = values.checkOutDate ? new Date(values.checkOutDate) : null;
          
          if (checkIn) checkIn.setHours(0, 0, 0, 0);
          if (checkOut) checkOut.setHours(0, 0, 0, 0);

          if (checkIn && d.getTime() === checkIn.getTime()) {
            return "react-calendar__tile--rangeStart selected-departure";
          }

          if (checkOut && d.getTime() === checkOut.getTime()) {
            return "react-calendar__tile--rangeEnd";
          }

          if (checkIn && checkOut && d > checkIn && d < checkOut) {
            return "react-calendar__tile--range";
          }

          return null;
        }}
      />
      <div className="flex flex-col justify-between">
        <div className="flex flex-col p-2 rounded-[2px] space-y-6">
          <div className="flex space-x-2 w-full justify-evenly">
            {[1, 2, 3].map((days) => (
              <label
                key={days}
                onClick={() => {
                  const newValue = flexibleDays === days ? 0 : days;
                  setFlexibleDays(newValue);
                  setFieldValue("flexibleDays", newValue);
                }}
                className={`cursor-pointer flex items-center space-x-1 px-4 py-1 rounded-[2px] 
                  shadow-[0_0_0_0.5px_#024575] 
                  ${flexibleDays === days ? "bg-darkBlue" : "bg-white text-smokyGray"}`}
              >
                <span
                  className={`text-[10px] ${flexibleDays === days ? "text-white" : "text-smokyGray"}`}
                >
                  {days === 0 ? "0 day" : `± ${days} ${days === 1 ? "day" : "days"}`}
                </span>
              </label>
            ))}
          </div>
          <span className="text-sm font-semibold text-center mt-2 text-darkBlue">
            Flexible Dates
          </span>
        </div>
      </div>
    </div>
  );
};

export default HotelCalendarComponent2;