import { TriangleAlert } from 'lucide-react'
import React from 'react'

const SignInPrompt = () => {
    return (
        <div className="w-full mx-auto p-6 border border-darkBlue rounded-2xl bg-blue-50 mb-6">
            <div className="flex items-center gap-4">
                <TriangleAlert className="w-7 h-7 text-darkBlue" strokeWidth={1} />
                <div>
                    <p className="text-sm font-normal text-black mb-2">
                        The names of the participants must match the information in the passport/ID!
                    </p>
                    <p className="text-xs font-light text-black">
                        Please ensure that the spelling is correct - subsequent corrections will incur costs
                    </p>
                </div>
            </div>
        </div>
    )
}

export default SignInPrompt