import React from 'react';

const Travel_summary = ({ bookingDetails }) => {
  if (!bookingDetails) {
    return <div>Error: Booking details not provided.</div>;
  }

  // Use optional chaining to safely access nested properties
  const departureDate = bookingDetails.departure?.date || 'N/A';
  const departureTime = bookingDetails.departure?.time || 'N/A';
  const arrivalDate = bookingDetails.arrival?.date || 'N/A';
  const arrivalTime = bookingDetails.arrival?.time || 'N/A';
  const returnDepartureDate = bookingDetails.returnDeparture?.date || 'N/A';
  const returnDepartureTime = bookingDetails.returnDeparture?.time || 'N/A';
  const returnArrivalDate = bookingDetails.returnArrival?.date || 'N/A';
  const returnArrivalTime = bookingDetails.returnArrival?.time || 'N/A';
  const travelers = bookingDetails.travelers || 'N/A';
  const totalPrice = bookingDetails.totalPrice || 'N/A';

  return (
    <div className="w-full max-w-[498px] mx-auto p-6 bg-white rounded-3xl border border-border">
      <h2 className="text-xl font-normal text-smokyGray mb-6">Your Summary</h2>

      {/* Total Price */}
      <div className="flex bg-offWhite p-3 w-full mb-8">
        <div className="flex justify-evenly w-full items-center">
          <span className="text-base text-smokyGray font-normal">Total Price</span>
          <span className="text-xl text-smokyGray font-medium">
            {`CHF ${totalPrice}`}
          </span>
        </div>
      </div>

      {/* Details Grid */}
      <div className="space-y-4">
        <DetailItem 
          label="Departure" 
          value={`${departureDate} ${departureTime}`} 
        />
        <DetailItem 
          label="Arrival" 
          value={`${arrivalDate} ${arrivalTime}`} 
        />
        <DetailItem 
          label="Return Departure" 
          value={`${returnDepartureDate} ${returnDepartureTime}`} 
        />
        <DetailItem 
          label="Return Arrival" 
          value={`${returnArrivalDate} ${returnArrivalTime}`} 
        />
        <DetailItem 
          label="Travel Participants" 
          value={`${travelers} adults`} 
        />
      </div>
    </div>
  );
};

const DetailItem = ({ label, value }) => (
  <div className="flex items-center justify-between">
    <span className="text-sm text-smokyGray font-medium w-full">{label}</span>
    <div className="bg-white border border-lightGray rounded-lg w-full px-4 py-2 text-sm text-smokyGray">
      {value}
    </div>
  </div>
);

export default Travel_summary;
