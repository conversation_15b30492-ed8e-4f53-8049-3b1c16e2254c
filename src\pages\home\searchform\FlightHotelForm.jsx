
import React, { useEffect, useRef, useState } from "react";
import { Bed, BedDouble, CalendarDays, ChevronLeft, ChevronRight, MapPin, Minus, Plus, Search, UserRound } from "lucide-react";
import { Field, Form } from "formik";
import useSWR from "swr";
import CalendarComponent2 from "./CalendarComponent2";
import "./Calendar.css";
import ButtonCom from "../../../components/ui/button/ButtonCom";

const fetcher = (url) => fetch(url).then((res) => res.json());

const formatDate = (d) => {
  if (!d || !(d instanceof Date) || isNaN(d.getTime())) return "";
  const year = d.getFullYear();
  const month = (d.getMonth() + 1).toString().padStart(2, "0");
  const day = d.getDate().toString().padStart(2, "0");
  return `${year}-${month}-${day}`;
};

const today = new Date();
today.setHours(0, 0, 0, 0);

const FlightHotelForm = ({ values, errors, touched, setFieldValue, navigate, selectedTab }) => {
  const checkInCalendarRef = useRef(null);
  const checkOutCalendarRef = useRef(null);
  const checkOutSectionRef = useRef(null);
  const destinationInputRef = useRef(null);
  const checkInSectionRef = useRef(null);
  const modalRef = useRef(null);
  const [showCheckInCalendar, setShowCheckInCalendar] = useState(false);
  const [showCheckOutCalendar, setShowCheckOutCalendar] = useState(false);
  const [activeSection, setActiveSection] = useState(null);
  const [showTravelersModal, setShowTravelersModal] = useState(false);
  const [destinationSuggestions, setDestinationSuggestions] = useState([]);
  const [promoActive, setPromoActive] = useState(false);
  const [promoCode, setPromoCode] = useState("");
  const [isValidating, setIsValidating] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [appliedPromoCode, setAppliedPromoCode] = useState("");
  const [flexibleDays, setFlexibleDays] = useState(values.flexibleDays || 0);
  const [rangeSelection, setRangeSelection] = useState(
    values.checkInDate && !isNaN(new Date(values.checkInDate).getTime())
      ? [new Date(values.checkInDate)]
      : []
  );

  // Auto-open check-out calendar after check-in selection
  const handleCheckInConfirm = () => {
    setShowCheckInCalendar(false);
    setTimeout(() => {
      setActiveSection("CheckOut");
      setShowCheckOutCalendar(true);
      if (checkOutSectionRef.current) {
        checkOutSectionRef.current.focus();
      }
    }, 0);
  };

  // Promo code validation
  useEffect(() => {
    if (promoCode) {
      setIsValidating(true);
      setIsValid(false);
      const timer = setTimeout(() => {
        const isValidPromo = promoCode === "MD3782";
        setIsValidating(false);
        setIsValid(isValidPromo);
        if (isValidPromo) {
          setTimeout(() => {
            setAppliedPromoCode(promoCode);
            setPromoActive(false);
            setPromoCode("");
          }, 2000);
        }
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      setIsValidating(false);
      setIsValid(false);
    }
  }, [promoCode]);

  // Fetch suggestions using the hotelAutoSearch API with SWR
  const { data: searchResults, error: searchError } = useSWR(
    (values.destination || "").length >= 2
      ? `https://backend.graycorp.io:9603/efly/api/hotelBooking/hotelAutoSearch?name=${encodeURIComponent(values.destination)}`
      : null,
    fetcher,
    { revalidateOnFocus: false }
  );

  // Initialize default values
  useEffect(() => {
    if (!values.destination) setFieldValue("destination", "");
    if (!values.checkInDate || isNaN(new Date(values.checkInDate).getTime())) {
      const todayDate = formatDate(new Date());
      setFieldValue("checkInDate", todayDate);
      setRangeSelection([new Date(todayDate)]);
    }
    if (!values.checkOutDate || isNaN(new Date(values.checkOutDate).getTime())) {
      const todayPlus3 = new Date();
      todayPlus3.setDate(todayPlus3.getDate() + 3);
      setFieldValue("checkOutDate", formatDate(todayPlus3));
      setRangeSelection((prev) => [prev[0] || new Date(todayDate), new Date(todayPlus3)]);
    }
    if (!values.rooms) setFieldValue("rooms", 1);
    if (!values.adults) setFieldValue("adults", 2);
    if (!values.children) setFieldValue("children", 0);
    if (!values.flexibleDays) setFieldValue("flexibleDays", 0);
  }, [values, setFieldValue]);

  // Update destination suggestions
  useEffect(() => {
    if (!searchResults || searchError) {
      setDestinationSuggestions([]);
      return;
    }

    let cities = [];
    let hotels = [];
    let countries = new Set();

    searchResults.forEach((item) => {
      if (item.hotelName) {
        hotels.push({
          type: "hotel",
          hotelName: item.hotelName,
          cityName: item.cityName,
          countryName: item.countryName,
          cityCode: item.cityCode,
        });
      } else {
        cities.push({
          type: "city",
          cityName: item.cityName,
          countryName: item.countryName,
          cityCode: item.cityCode,
        });
      }
      countries.add(item.countryName);
    });

    const countrySuggestions = Array.from(countries).map((countryName, index) => ({
      type: "country",
      countryName,
      cityCode: `COUNTRY-${index}`,
    }));

    const suggestions = [...cities.slice(0, 3), ...hotels.slice(0, 3), ...countrySuggestions.slice(0, 2)];
    setDestinationSuggestions(suggestions);
  }, [searchResults, searchError]);

  const handleDestinationChange = (e) => {
    const input = e.target.value;
    setFieldValue("destination", input);
    setFieldValue("cityCode", null);
  };

  useEffect(() => {
    setActiveSection("Destination");
    if (destinationInputRef.current) {
      destinationInputRef.current.focus();
    }
  }, []);

  const adjustDate = (field, increment) => {
    const date = field === "checkInDate" ? values.checkInDate : values.checkOutDate;
    if (!date || isNaN(new Date(date).getTime())) return;
    const newDate = new Date(date);
    newDate.setDate(newDate.getDate() + (increment ? 1 : -1));

    if (field === "checkInDate" && newDate >= today) {
      setFieldValue("checkInDate", formatDate(newDate));
      setRangeSelection([newDate]);
      if (values.checkOutDate && newDate >= new Date(values.checkOutDate)) {
        const nextDay = new Date(newDate);
        nextDay.setDate(nextDay.getDate() + 1);
        setFieldValue("checkOutDate", formatDate(nextDay));
        setRangeSelection([newDate, nextDay]);
      }
    } else if (field === "checkOutDate" && values.checkInDate && newDate > new Date(values.checkInDate)) {
      setFieldValue("checkOutDate", formatDate(newDate));
      setRangeSelection([new Date(values.checkInDate), newDate]);
    }
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (checkInCalendarRef.current && !checkInCalendarRef.current.contains(event.target)) {
        setShowCheckInCalendar(false);
      }
      if (checkOutCalendarRef.current && !checkOutCalendarRef.current.contains(event.target)) {
        setShowCheckOutCalendar(false);
      }
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShowTravelersModal(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const getSectionBorderStyle = (sectionName) => {
    return activeSection === sectionName ? "border border-gray/35" : "border border-transparent";
  };

  return (
    <Form className="space-y-2 p-2">
      <div className="flex flex-col space-y-[135px] md:space-y-4 px-2 md:px-0 -mt-4">
        <div className="flex flex-col md:flex-row h-14 w-full border border-border rounded-[8px] bg-white items-center justify-between space-y-2 md:space-y-0">
          {/* Destination */}
          <div
            className={`flex flex-col relative w-full ${errors.cityCode && touched.destination ? "animate-blinkTwice" : ""}`}
            onClick={() => setActiveSection("Destination")}
          >
            <div className={`flex justify-between items-center w-full h-14 rounded-[8px] ${getSectionBorderStyle("Destination")}`}>
              <div className="hidden md:block absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
              <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                <MapPin className="text-smokyGray w-6 h-6 flex-shrink-0" strokeWidth={1} />
                <div className="flex flex-col w-full">
                  <label className="text-xs font-light text-smokyGray font-inter leading-tight" htmlFor="destination">
                    Destination
                  </label>
                  <Field
                    name="destination"
                    placeholder="Enter city, country, or hotel"
                    className="text-sm font-light text-black focus:outline-none w-full max-w-52"
                    value={values.destination}
                    onChange={handleDestinationChange}
                    innerRef={destinationInputRef}
                    autoComplete="off"
                  />
                  {destinationSuggestions.length > 0 && (
                    <ul
                      className="absolute z-50 top-16 mt-1 left-0 bg-white border border-border shadow-lg text-sm font-normal rounded-lg w-full max-w-96 overflow-y-auto no-scrollbar"
                      style={{ maxHeight: "330px" }}
                    >
                      {destinationSuggestions.map((item, index) => (
                        <li
                          key={`${item.type}-${item.cityName || item.countryName || item.hotelName}-${index}`}
                          className="px-4 py-3 cursor-pointer hover:bg-darkBlue flex items-center border-b border-border space-x-3 group"
                          onClick={() => {
                            if (item.type === "city") {
                              setFieldValue("destination", `${item.cityName}, ${item.countryName}`);
                              setFieldValue("cityCode", item.cityCode);
                            } else if (item.type === "hotel") {
                              setFieldValue("destination", `${item.hotelName}, ${item.cityName}, ${item.countryName}`);
                              setFieldValue("cityCode", item.cityCode);
                            } else if (item.type === "country") {
                              setFieldValue("destination", item.countryName);
                              setFieldValue("cityCode", item.cityCode);
                            }
                            setDestinationSuggestions([]);
                            setTimeout(() => {
                              setActiveSection("CheckIn");
                              setShowCheckInCalendar(true);
                              if (checkInSectionRef.current) {
                                checkInSectionRef.current.focus();
                              }
                            }, 0);
                          }}
                        >
                          {item.type === "hotel" ? (
                            <Bed className="w-6 h-6 flex-shrink-0 text-black group-hover:text-white" strokeWidth={1} />
                          ) : (
                            <MapPin className="w-6 h-6 flex-shrink-0 text-black group-hover:text-white" strokeWidth={1} />
                          )}
                          <div className="flex flex-col">
                            <span className="text-sm text-black group-hover:text-white">
                              {item.type === "city" && item.cityName}
                              {item.type === "hotel" && item.hotelName}
                              {item.type === "country" && item.countryName}
                            </span>
                            <span className="text-[10px] text-smokyGray font-light group-hover:text-white">
                              {item.type === "city" && item.countryName}
                              {item.type === "hotel" && `${item.cityName}, ${item.countryName}`}
                              {item.type === "country" && ""}
                            </span>
                          </div>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              </div>
            </div>
          </div>
          {/* Check-in */}
          <div
            className="flex flex-col relative w-full"
            ref={checkInSectionRef}
            tabIndex={0}
            onClick={() => setActiveSection("CheckIn")}
          >
            <div
              className={`flex justify-between items-center bg-white w-full h-14 rounded-[8px] ${getSectionBorderStyle("CheckIn")}`}
              onClick={() => {
                setShowCheckInCalendar(!showCheckInCalendar);
                setShowCheckOutCalendar(false);
                setRangeSelection([new Date(values.checkInDate)]);
              }}
            >
              <div className="hidden md:block absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
              <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                <CalendarDays className="text-smokyGray w-6 h-6 flex-shrink-0" strokeWidth={1} />
                <div className="flex flex-col items-start">
                  <span className="text-smokyGray text-xs font-light">Check-in</span>
                  <div className="text-nowrap text-sm font-light text-darkGray">
                    {values.checkInDate || formatDate(new Date())}
                  </div>
                </div>
              </div>
              <div className="flex flex-row space-x-2 items-center h-full mr-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    adjustDate("checkInDate", false);
                  }}
                  disabled={!values.checkInDate || new Date(values.checkInDate) <= today}
                  className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${!values.checkInDate || new Date(values.checkInDate) <= today
                      ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                      : ""
                    }`}
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    adjustDate("checkInDate", true);
                  }}
                  className="flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px]"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
            {errors.checkInDate && touched.checkInDate && (
              <span className="text-red text-xs mt-1 ml-2">{errors.checkInDate}</span>
            )}
            {showCheckInCalendar && (
              <div ref={checkInCalendarRef}>
                <CalendarComponent2
                  type="checkIn"
                  values={values}
                  setFieldValue={setFieldValue}
                  flexibleDays={flexibleDays}
                  setFlexibleDays={setFlexibleDays}
                  setRangeSelection={setRangeSelection}
                  handleCheckInConfirm={handleCheckInConfirm}
                  showCalendar={setShowCheckInCalendar}
                />
              </div>
            )}
          </div>
          {/* Check-out */}
          <div
            className="flex flex-col relative w-full"
            ref={checkOutSectionRef}
            tabIndex={0}
            onClick={() => setActiveSection("CheckOut")}
          >
            <div
              className={`flex justify-between items-center bg-white w-full h-14 rounded-[8px] ${getSectionBorderStyle("CheckOut")}`}
              onClick={() => {
                if (values.checkInDate && !isNaN(new Date(values.checkInDate).getTime())) {
                  setRangeSelection([new Date(values.checkInDate)]);
                  setShowCheckOutCalendar(true);
                  setShowCheckInCalendar(false);
                  setActiveSection("CheckOut");
                }
              }}
            >
              <div className="hidden md:block absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
              <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                <CalendarDays className="text-smokyGray w-6 h-6 flex-shrink-0" strokeWidth={1} />
                <div className="flex flex-col items-start">
                  <span className="text-smokyGray text-xs font-light">Check-out</span>
                  <div className="text-nowrap text-sm font-light text-darkGray">
                    {values.checkOutDate || formatDate(new Date(new Date().setDate(new Date().getDate() + 3)))}
                  </div>
                </div>
              </div>
              <div className="flex flex-row space-x-2 items-center h-full mr-2">
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    adjustDate("checkOutDate", false);
                  }}
                  disabled={
                    !values.checkOutDate ||
                    !values.checkInDate ||
                    new Date(values.checkOutDate) <= new Date(values.checkInDate)
                  }
                  className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${!values.checkOutDate ||
                      !values.checkInDate ||
                      new Date(values.checkOutDate) <= new Date(values.checkInDate)
                      ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                      : ""
                    }`}
                >
                  <ChevronLeft className="w-4 h-4" />
                </button>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    adjustDate("checkOutDate", true);
                  }}
                  className="flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px]"
                >
                  <ChevronRight className="w-4 h-4" />
                </button>
              </div>
            </div>
            {errors.checkOutDate && touched.checkOutDate && (
              <span className="text-red text-xs mt-1 ml-2">{errors.checkOutDate}</span>
            )}
            {showCheckOutCalendar && (
              <div ref={checkOutCalendarRef}>
                <CalendarComponent2
                  type="checkOut"
                  values={values}
                  setFieldValue={setFieldValue}
                  flexibleDays={flexibleDays}
                  setFlexibleDays={setFlexibleDays}
                  setRangeSelection={setRangeSelection}
                  handleCheckInConfirm={handleCheckInConfirm}
                  showCalendar={setShowCheckOutCalendar}
                />
              </div>
            )}
          </div>
        </div>
        <div className="flex flex-col md:flex-row w-full space-y-[75px] md:space-y-0 md:space-x-6">
          <div className="flex flex-col md:flex-row h-14 w-full border border-border rounded-[8px] bg-white items-center justify-between space-y-2 md:space-y-0">
            <div className="flex flex-col relative w-full" onClick={() => setActiveSection("Rooms")}>
              <div className={`flex justify-between items-center w-full h-14 rounded-[8px] ${getSectionBorderStyle("Rooms")}`}>
                <div className="hidden md:block absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                  <BedDouble className="text-smokyGray w-6 h-6 flex-shrink-0" strokeWidth={1} />
                  <div className="flex flex-col w-full">
                    <label className="text-xs font-light text-smokyGray font-inter leading-tight" htmlFor="rooms">
                      Rooms
                    </label>
                    <span className="text-sm font-light text-black">{values.rooms} Room{values.rooms > 1 && "s"}</span>
                  </div>
                </div>
                <div className="flex items-center space-x-2 px-2 ml-auto">
                  <button
                    type="button"
                    onClick={() => setFieldValue("rooms", Math.max(1, values.rooms - 1))}
                    disabled={values.rooms <= 1}
                    className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.rooms <= 1 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : ""
                      }`}
                  >
                    <Minus className="w-3 h-3" />
                  </button>
                  <button
                    type="button"
                    onClick={() => setFieldValue("rooms", Math.min(5, values.rooms + 1))}
                    disabled={values.rooms >= 5}
                    className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.rooms >= 5 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : ""
                      }`}
                  >
                    <Plus className="w-3 h-3" />
                  </button>
                </div>
              </div>
            </div>
            <div className="flex flex-col relative w-full" onClick={() => setActiveSection("Travelers")}>
              <div
                className={`flex justify-between items-center bg-white w-full h-14 rounded-[8px] ${getSectionBorderStyle("Travelers")}`}
                onClick={() => setShowTravelersModal(true)}
              >
                <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                  <UserRound className="text-smokyGray w-6 h-6 flex-shrink-0" strokeWidth={1} />
                  <div className="flex flex-col w-full">
                    <label className="text-xs font-light text-smokyGray font-inter leading-tight" htmlFor="rooms">
                      Travelers
                    </label>
                    <span className="text-sm font-light text-black focus:outline-none">
                      {values.adults + values.children} Persons
                    </span>
                  </div>
                </div>
              </div>
              {showTravelersModal && (
                <div
                  className="absolute z-50 mt-16 right-0 sm:left-auto bg-white border border-border shadow-lg p-4 rounded-lg w-full"
                  ref={modalRef}
                >
                  <div className="flex flex-col space-y-4">
                    <span className="flex justify-center text-base text-black border-b border-border pb-4">
                      Travelers
                    </span>
                    <div className="flex items-center justify-between">
                      <button
                        type="button"
                        onClick={() => setFieldValue("adults", Math.max(1, values.adults - 1))}
                        disabled={values.adults <= 1}
                        className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.adults <= 1 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : ""
                          }`}
                      >
                        <Minus className="w-3 h-3" />
                      </button>
                      <div className="text-center">
                        <span className="text-sm font-light text-darkGray">{values.adults} Adult</span>
                        <div className="text-smokyGray text-[10px] font-light">Ages 12+</div>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          const total = values.adults + values.children;
                          if (total < 9) setFieldValue("adults", values.adults + 1);
                        }}
                        disabled={values.adults + values.children >= 9}
                        className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.adults + values.children >= 9 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : ""
                          }`}
                      >
                        <Plus className="w-3 h-3" />
                      </button>
                    </div>
                    <div className="flex items-center justify-between">
                      <button
                        type="button"
                        onClick={() => setFieldValue("children", Math.max(0, values.children - 1))}
                        disabled={values.children <= 0}
                        className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.children <= 0 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : ""
                          }`}
                      >
                        <Minus className="w-3 h-3" />
                      </button>
                      <div className="text-center">
                        <span className="text-sm font-light text-darkGray">{values.children} Child</span>
                        <div className="text-smokyGray text-[10px] font-light">Ages 2-11</div>
                      </div>
                      <button
                        type="button"
                        onClick={() => {
                          const total = values.adults + values.children;
                          if (total < 9) setFieldValue("children", values.children + 1);
                        }}
                        disabled={values.adults + values.children >= 9 || values.children >= 6}
                        className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.adults + values.children >= 9 || values.children >= 6
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                          }`}
                      >
                        <Plus className="w-3 h-3" />
                      </button>
                    </div>
                    <button
                      type="button"
                      onClick={() => setShowTravelersModal(false)}
                      className="w-full bg-darkBlue text-base font-light text-white py-2 rounded-[8px]"
                    >
                      Confirm
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
          <div className="flex w-full md:w-[30%]">
            <ButtonCom
              variant="secondary"
              size="lg"
              width="full"
              icon="search"
              iconPosition="left"
              type="submit"
            >
              Search
            </ButtonCom>
          </div>
        </div>
      </div>
      {/* Promo Code & Error */}
      <div className="w-full flex justify-between items-end">
        <div className="font-extralight text-xs text-white">
          {errors.cityCode && touched.destination && (
            <div className="animate-blinkTwice transition-opacity duration-1000">{errors.cityCode}</div>
          )}
        </div>
        <div className="text-end">
          {promoActive ? (
            <div className="relative flex items-start justify-end">
              <input
                type="text"
                value={promoCode}
                onChange={(e) => setPromoCode(e.target.value)}
                onBlur={() => {
                  if (!promoCode) setPromoActive(false);
                }}
                className="w-full max-w-72 h-11 border border-border rounded-[8px] text-sm font-light text-darkGray mr-2 p-4 focus:outline-none"
                placeholder={promoCode ? "" : "Enter Promo Code"}
              />
              <div className="flex items-center justify-center border border-border bg-orange rounded-[8px] h-11 w-11 p-2">
                {isValidating ? (
                  <ChevronRight className="h-6 w-6 text-white" />
                ) : isValid ? (
                  <Check className="h-6 w-6 text-white" />
                ) : (
                  <ChevronRight className="h-6 w-6 text-white" />
                )}
              </div>
            </div>
          ) : (
            <span
              className="font-extralight text-xs text-white cursor-pointer"
              onClick={() => setPromoActive(true)}
            >
              {appliedPromoCode ? `Promo Code Added: ${appliedPromoCode}` : "+ Add Promo Code"}
            </span>
          )}
        </div>
      </div>
    </Form>
  );
};

export default FlightHotelForm;