import React, { useState, useEffect } from "react";
import backgroundImage from "../../../assets/heroSection/hero.png";
import WhiteLogo from "../../../assets/footer/whitelogo.png";
import ButtonCom from "../../ui/button/ButtonCom";
import SearchForm from "../../../pages/home/<USER>/SearchForm";

const HeroSection = () => {
  const [timeLeft, setTimeLeft] = useState(calculateTimeLeft());

  function calculateTimeLeft() {
    const targetDate = new Date("April 12, 2025 12:00:00");
    const now = new Date();
    const difference = targetDate - now;
    if (difference > 0) {
      return {
        days: Math.floor(difference / (1000 * 60 * 60 * 24)),
        hours: Math.floor((difference / (1000 * 60 * 60)) % 24),
        minutes: Math.floor((difference / (1000 * 60)) % 60),
        seconds: Math.floor((difference / 1000) % 60),
      };
    } else {
      return { days: 0, hours: 0, minutes: 0, seconds: 0 };
    }
  }

  useEffect(() => {
    const timer = setInterval(() => {
      setTimeLeft(calculateTimeLeft());
    }, 1000);
    return () => clearInterval(timer);
  }, []);

  const currentDate = new Date();
  const options = { day: 'numeric', month: 'long', year: 'numeric' };
  const currentDayMonthYear = currentDate.toLocaleDateString('en-GB', options);

  return (
    <>
      {/* Hero Section */}
      <div className="w-full h-fit  min-h-screen flex justify-center ">
        {/* Static overlay image */}
        <div className="absolute inset-0 z-0 overflow-hidden">
          <img
            src={backgroundImage}
            alt="Mountains"
            className="w-full h-full object-fill"
          />
        </div>
        <div className="absolute inset-0 bg-darkBlue bg-opacity-50"></div>
        <div className="absolute top-[117px] left-0 w-full z-[60] flex justify-center ">
          <SearchForm />
        </div>
      </div>

      {/* Desktop & Tablet View */}
      <div className="hidden sm:block">
        <div className="absolute bottom-0 w-full h-20 bg-darkBlue bg-opacity-70">
          <OfferSectionContent
            timeLeft={timeLeft}
            currentDayMonthYear={currentDayMonthYear}
            className="max-w-[1140px] flex flex-wrap mx-auto items-center justify-between px-4 sm:px-6 md:px-8 lg:px-16 "
          />
        </div>
      </div>

      {/* Mobile View */}
      <div className="sm:hidden relative">
        <div className="absolute inset-0 z-0">
          <img src={backgroundImage} alt="Mountains" className="w-full h-full object-cover" />
          <div className="absolute inset-0 bg-darkBlue bg-opacity-85"></div>
        </div>
        <div className="relative z-10 px-4 flex-col flex items-center justify-center space-y-4 py-6 -mt-20">
          <OfferSectionContent timeLeft={timeLeft} currentDayMonthYear={currentDayMonthYear} />
        </div>
      </div>

    </>
  );
};

const OfferSectionContent = ({ timeLeft, currentDayMonthYear }) => (
  <div className="w-full max-w-[80%] flex flex-row mx-auto items-center justify-between px-4  md:px-0 py-0">
    <div
      className="hidden sm:flex  bg-no-repeat">
      <img
        src={WhiteLogo}
        alt="Efly Logo"
        className="w-[120px] h-[80px] cursor-pointer bg-opacity-100 "
      />
    </div>

    <div className="w-full md:w-auto flex flex-col md:flex-row justify-center items-center text-base space-y-2 md:space-y-0 md:space-x-6 lg:space-x-8">
      <div className="w-full md:w-auto flex flex-col text-base items-center md:items-end px-2 mt-2">
        <h1 className="text-orange text-xs ">Travel to</h1>
        <p className="text-white text-base md:text-xl font-semibold uppercase">Sri Lanka</p>
      </div>
      {/* <div className="flex flex-col items-center md:items-end w-full md:w-auto px-2">
        <h1 className="text-orange text-sm uppercase">Offer ends soon</h1>
        <div className="text-white text-lg md:text-2xl  font-semibold font-roboto">
          <div className="flex justify-center w-full space-x-3">
            <span className="flex-1 text-center">{timeLeft.days}</span>
            <span className="mx-3">:</span>
            <span className="flex-1 text-center">{timeLeft.hours}</span>
            <span className="mx-3">:</span>
            <span className="flex-1 text-center">{timeLeft.minutes}</span>
            <span className="mx-3">:</span>
            <span className="flex-1 text-center">{timeLeft.seconds}</span>
          </div>
        </div>

        <div className="text-white text-[9px] font-extralight font-roboto ">
          <div className="flex justify-center  w-full">
            <span className="flex-1 mr-8 text-center">Days</span>
            <span className="flex-1 mr-8 text-center">Hours</span>
            <span className="flex-1 mr-6 text-center">Minutes</span>
            <span className="flex-1 mr-0 text-center">Seconds</span>
          </div>
        </div>
      </div> */}

      <div className="bg-white p-4 w-36 h-14 rounded-[2px] flex flex-col items-center justify-center">
        <p className="text-darkBlue text-xs font-medium mt-1">STARTS FROM</p>
        <p className="text-darkBlue text-xs font-semibold mr-2">CHF
          <span className="text-darkBlue text-2xl font-bold ml-2">500</span></p>
      </div>
      <div className="w-full md:w-auto flex flex-col text-base items-center md:items-start px-2">
        <h1 className="text-orange text-xs ">Travel on</h1>
        <p className="text-white text-base md:text-xl font-semibold uppercase">{currentDayMonthYear} </p>
      </div>
    </div>
    <div className="flex flex-col items-center w-full md:w-auto px-2 mt-2">
      <button className="bg-darkBlue px-4 md:px-8 py-2 md:h-[50px] text-white hover:bg-orange transition-colors text-base md:text-xl hover:scale-105 font-semibold">
        BOOK NOW
      </button>
      <p className="text-xs text-white font-extralight mt-1">T&C Apply</p>
    </div>
  </div>
);

export default HeroSection;
