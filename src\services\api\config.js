export const API_CONFIG = {
  HOTEL: {
    BASE_URL: 'https://backend.graycorp.io:9603/efly/api/hotelBooking',
    TIMEOUT: 30000,
  },
  COMMON_HEADERS: {
    accept: '*/*',
    'Content-Type': 'application/json',
  },
};

// API Endpoints
export const ENDPOINTS = {
  HOTEL: {
    SEARCH: '/searchhotels',
    FILTER_COUNTS: '/getHotelCountsByFacilities',
    FILTER_HOTELS: '/filterAvailableHotels',
    HOTEL_DETAILS: '/getHotelDetailsByHotelId',
    ROOMS: '/getRooms',
    ROOMS_WITH_BLOCKING: '/getRoomsWithBlocking',
    SAVE_BOOKING: '/savebooking',
    BOOK_ITINERARY_NO_CONFIRM: '/bookitineraryWithConfirmNo',
    BOOK_ITINERARY_CONFIRM: '/bookitineraryWithConfirmYes',
    AUTO_SEARCH: '/hotelAutoSearch',
    SAVED_HOTEL_DETAILS: '/getSavedAvailableHotelDetailsByHotelId',
  },
};

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Network error occurred. Please check your connection.',
  TIMEOUT_ERROR: 'Request timeout. Please try again.',
  VALIDATION_ERROR: 'Validation failed. Please check your input.',
  GENERIC_ERROR: 'An unexpected error occurred. Please try again.',
  NO_DATA: 'No data available.',
  BOOKING_FAILED: 'Booking failed. Please try again.',
  PRICING_FAILED: 'Unable to get pricing. Please try again.',
};

// Retry Configuration
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000,
};
