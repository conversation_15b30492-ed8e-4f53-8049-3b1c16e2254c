import React, { useState, useEffect, useRef } from "react";
import { useLocation } from "react-router-dom";
import useS<PERSON> from "swr";
import BookingFlight from "./BookingFlight";
import SearchForm from "../home/<USER>/SearchForm";
import axios from "axios";
import LoadingPage1 from "./LoadingPage1";

const api = axios.create({
  baseURL: "https://backend.graycorp.io:9100/eflyer-bookings/api/v1",
});

const FlightAvailabityView = () => {
  const location = useLocation();
  const { dep_date, des_date, dep_apt, des_apt, passenger_type } = location.state || {};
  const [sessionId, setSessionId] = useState(null);

  // Refs for scrolling
  const loadingRef = useRef(null);
  const flightDataRef = useRef(null);
  
  const fetcher = async ([url, params]) => {
    const response = await api.get(url, { params });
    // Capture session ID from response headers if present
    console.log("API Response Headers:", response.headers);
    console.log("API Response Data:", response.data);
    const sessionId = response.headers["session_id"] || "";
    console.log("Extracted Session ID:", sessionId);
    return { data: response.data.contents.flight_Availability_Details, sessionId };
  };

  const { data: flightData, error, isLoading } = useSWR(
    dep_date && dep_apt && des_apt
      ? ["/getFlights", { dep_date, des_date, dep_apt, des_apt, passenger_type }]
      : null,
    fetcher,
    {
      onSuccess: (response) => {
        if (response.sessionId) setSessionId(response.sessionId);
      },
      onError: (err) => console.error("Flight search error:", err),
    }
  );

  const totalPassengers = passenger_type
    ? passenger_type.split(",").reduce((acc, part) => {
        const [_, count] = part.split(":");
        return acc + parseInt(count, 10);
      }, 0)
    : 1;

  const parsePassengerType = (passengerType) => {
    const types = passengerType.split(",");
    let adults = 0,
      children = 0,
      babies = 0;
    types.forEach((type) => {
      const [key, value] = type.split(":");
      if (key === "Adt") adults = parseInt(value, 10);
      if (key === "Chd") children = parseInt(value, 10);
      if (key === "Inf") babies = parseInt(value, 10);
    });
    return { adults, children, babies };
  };

  const { adults, children, babies } = parsePassengerType(passenger_type);

  useEffect(() => {
    if (isLoading && loadingRef.current) {
      loadingRef.current.scrollIntoView({
        behavior: "smooth",
        top:"20"
      });
    }
  }, [isLoading]);

  // Scroll to BookingFlight when data is loaded
  useEffect(() => {
    if (!isLoading && flightData && flightDataRef.current) {
      flightDataRef.current.scrollIntoView({
        behavior: "smooth",
        block: "start", 
      });
    }
  }, [isLoading, flightData]);


  // Added useEffect to log the sessionId when it changes
  useEffect(() => {
    if (sessionId) {
      console.log("Session ID passed to BookingFlight:", sessionId);
    }
  }, [sessionId]);

  if (!dep_date || !dep_apt || !des_apt) {
    return <p>Missing search parameters. Please go back and search again.</p>;
  }

  if (error) {
    return (
      <p>
        Error fetching flights: {error.message}. Please try again or contact
        support.
      </p>
    );
  }

  return (
    <div className="w-full flex justify-center">
     {isLoading ? (
  <div 
    ref={loadingRef} 
    style={{
      position: "fixed", 
      top: "0px",
      left: 0,
      width: "100%",
      zIndex: 1000
    }}
  >
    <LoadingPage1 />
  </div>
) : (
      <div className="w-full max-w-[1100px] px-4 lg:px-0 space-y-8 py-8">
        <section id="search-form" className="pt-6 flex" >
          <SearchForm initialData={location.state} />
        </section>
        <div className="flex flex-col space-y-10 lg:space-y-0 lg:flex-row lg:space-x-10" ref={flightDataRef}>
          
            <div className="flex flex-col space-y-10 lg:space-y-0 lg:flex-row lg:space-x-10">
              <div className="flex flex-col space-y-4 w-full" >
                <BookingFlight
                  data={flightData?.data}
                  totalPassengers={totalPassengers}
                  dep_apt={dep_apt}
                  des_apt={des_apt}
                  adults={adults}
                  children={children}
                  babies={babies}
                  sessionId={sessionId} // Pass sessionId to BookingFlight
                />
              </div>
            </div>
         
        </div>
      </div>
       )}
    </div>
  );
};

export default FlightAvailabityView;


// import React from "react";
// import useSWR from "swr";
// import axios from "axios";

// // Custom fetcher that uses axios and extracts both data and sessionId
// const fetcher = (url) =>
//   axios.get(url).then((res) => ({
//     data: res.data,
//     sessionId: res.headers["session_id"] // axios converts header names to lower-case
//   }));

// function FlightAvailabityView() {
//   // API endpoint and query parameters
//   const apiUrl = "https://backend.graycorp.io:9100/eflyer-bookings/api/v1/getFlights";
//   const params = new URLSearchParams({
//     dep_date: "2025-04-25",
//     des_date: "2025-05-23",
//     dep_apt: "CMB",
//     des_apt: "FRA",
//     passenger_type: "Adt:1"
//   });

//   // Use SWR for data fetching
//   const { data, error } = useSWR(`${apiUrl}?${params.toString()}`, fetcher);

//   if (error) return <div>Error fetching flight data.</div>;
//   if (!data) return <div>Loading...</div>;

//   return (
//     <div>
//       <h1>Flight Data</h1>
//       {data.sessionId && <p>Session ID: {data.sessionId}</p>}
//       <pre>{JSON.stringify(data.data, null, 2)}</pre>
//     </div>
//   );
// }

// export default FlightAvailabityView;