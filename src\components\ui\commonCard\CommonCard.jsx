// CommonCard.jsx
import React from "react";
import { useNavigate } from "react-router-dom";
import { BedDouble, Plane, Star, ThumbsUp } from "lucide-react";
import PropTypes from "prop-types";
import { useMemo } from "react";

// Move this hook outside the component
const useMarginTopClass = (adventure) => {
  return useMemo(() => {
    if (!adventure) return "mt-8";

    const hotelSpecialIds = [4, 5, 5060795, 427935, 954195, 1037285, 5393575];
    const flightSpecialIds = [4, 5];

    if (adventure.type === "flight") {
      return flightSpecialIds.includes(adventure.id) ? "mt-14" : "mt-20";
    }

    return hotelSpecialIds.includes(adventure.id) ? "mt-10" : "mt-8";
  }, [adventure]);
};

const getStarRating = (hotelData) => {
  if (hotelData.rating) return Math.min(5, Math.max(0, hotelData.rating));
  if (hotelData.classificationCode) {
    switch (hotelData.classificationCode) {
      case 563: return 5;
      case 562: return 4;
      case 561: return 3;
      case 560: return 2;
      case 559: return 1;
      default: return 0;
    }
  }
  if (hotelData.classificationName) {
    const starCount = (hotelData.classificationName.match(/\*/g) || []).length;
    return Math.min(5, starCount);
  }
  return 0;
};

const CommonCard = ({
  adventure = {
    id: "1",
    type: "hotel",
    title: "",
    hotelName: "",
    location: "",
    cityData: { cityName: "" },
    dates: "",
    duration: "",
    package: "",
    originalPrice: "",
    discountedPrice: "",
    discount: "",
    image: "",
    thumbUrl: "",
    rating: 0,
    classificationCode: 0,
    classificationName: "",
    isTop: false,
    isLimitedTime: false,
    isTrending: false,
    hotelId: 0
  },
  small = false,
  navigate
}) => {
  const marginTopClass = useMarginTopClass(adventure);
  const starRating = useMemo(() => getStarRating(adventure), [adventure]);
  const internalNavigate = useNavigate();

  const handleBookNow = () => {
    const formData = adventure.searchCriteria || {};
    (navigate || internalNavigate)(`/hotel-and-flight/${adventure.id}`, { state: { formData } });
  };

  const handleDiscover = () => {
    handleBookNow(); // Same navigation as Book Now
  };

  const renderPriceSection = () => {
    if (!adventure) return null;

    const showDiscountedPrice = [4, 5, 5060795, 427935, 954195, 1037285, 5393575].includes(adventure.id);
    const showPerPerson = [1, 2, 3].includes(adventure.id);

    return (
      <div className="w-1/2 text-right">
        {showPerPerson && (
          <p className="text-sm hidden lg:block ">Per person from</p>
        )}
        {adventure.originalPrice && (
          <span className="text-lg text-right">
            {adventure.originalPrice}
          </span>
        )}
        {(showDiscountedPrice || showPerPerson) && (
          <p className="text-sm font-semibold text-nowrap mt-2">
            {adventure.discountedPrice}
          </p>
        )}
      </div>
    );
  };

  if (!adventure) return <div className="w-full h-fit bg-gray-100 rounded-xl animate-pulse" />;

  const imageSrc = adventure.thumbUrl || adventure.image || "https://via.placeholder.com/400x300";

  return (
    <div className={`relative w-full overflow-hidden rounded-xl bg-white shadow-lg group ${small ? "max-w-md" : "max-w-4xl"}`}>
      <div className={`relative ${small ? "h-[329px]" : "h-[329px]"}`}>
        <img
          src={imageSrc}
          alt={adventure.title || adventure.hotelName || "Adventure image"}
          className="h-full w-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
          onError={(e) => {
            e.target.src = "https://via.placeholder.com/400x300";
          }}
        />
        <div className="absolute inset-0 bg-gradient-to-b from-black/20 to-black/40 group-hover:bg-black/20">
          <div className="flex items-center justify-between px-1 py-2 lg:px-4 lg:py-4">
            <div className="flex gap-2 items-center">
              <div className="rounded-full bg-black/30 px-3 py-1 text-white text-sm font-medium flex flex-row gap-x-4">
                {adventure.type === "flight" ? (
                  <>
                    <Plane className="h-5 w-5" />
                    <span>Flight</span>
                  </>
                ) : (
                  <>
                    <BedDouble className="h-5 w-5" />
                    <span>Hotel</span>
                  </>
                )}
              </div>

              {adventure.type === "hotel" && adventure.isTop && (
                <span className="rounded-full bg-orange px-3 py-1 text-sm font-medium text-white flex flex-row gap-x-2">
                  <ThumbsUp />
                  Top
                </span>
              )}

              {adventure.type === "flight" && adventure.isLimitedTime && (
                <span className="rounded-full bg-black/30 px-3 py-1 text-white text-sm font-medium">
                  Limited Time Only
                </span>
              )}

              {adventure.type === "flight" && adventure.isTrending && (
                <span className="rounded-full bg-black/30 px-3 py-1 text-white text-sm font-medium">
                  Trending
                </span>
              )}
            </div>

            {adventure.type === "hotel" && starRating > 0 && (
              <div className="flex">
                {[...Array(starRating)].map((_, index) => (
                  <Star key={index} className="h-5 w-5 fill-white text-white" />
                ))}
              </div>
            )}
          </div>

          <div
            className={`relative w-full px-6 text-white bg-black bg-opacity-10 py-8
                       transform translate-y-20 opacity-80 group-hover:translate-y-0 group-hover:opacity-100
                       transition-all duration-700 ease-in-out ${marginTopClass}`}
          >
            <h2 className="text-base font-semibold font-inter text-xs border-white border-opacity-20 truncate uppercase">
              {adventure.title || adventure.hotelName || "Untitled Adventure"}
            </h2>
            <p className="text-base font-normal font-inter text-[20px]">
              {adventure.location || adventure.cityData?.cityName || "Location not specified"}
            </p>

            <div className="flex flex-wrap items-center justify-between">
              <div className="space-y-0 w-1/2">
                <p className="text-nowrap font-normal font-inter text-[12px]">{adventure.dates || "Dates not specified"}</p>
                <p className="font-normal font-inter text-[12px]">{adventure.duration || ""}</p>
                <p className="font-normal font-inter text-[12px]">{adventure.package || ""}</p>
              </div>
              {renderPriceSection()}
            </div>

            <div className="my-2 space-y-2 hidden duration-500 group-hover:block">
              <div className="w-full flex justify-center">
                <button
                  onClick={handleBookNow}
                  className="w-3/4 rounded-3xl bg-[#024577] py-3 text-center font-semibold text-white transition-colors bg-opacity-40 hover:scale-105"
                >
                  Book Now
                </button>
              </div>
              <button
                onClick={handleDiscover}
                className="w-full text-center font-semibold text-white underline hover:scale-105"
              >
                Discover
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

CommonCard.propTypes = {
  adventure: PropTypes.object,
  small: PropTypes.bool,
  navigate: PropTypes.func
};

export default CommonCard;