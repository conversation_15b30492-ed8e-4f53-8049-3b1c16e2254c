import axios from 'axios';
import xmlbuilder from 'xmlbuilder';

// Axios instance for EFLY API
const api = axios.create({
  baseURL: 'https://backend.graycorp.io:9100/eflyer-bookings/api/v1',
});

// Staging URL for pricing requests (proxied for development)
const pricingApi = axios.create({
  baseURL: '/api', // Use proxy path instead of direct URL
});

let sessionId = null;

// Set session ID (replace with actual auth logic)
const setSessionId = (id) => {
  sessionId = id;
};

// Search Flights
export const searchFlights = async (params) => {
  try {
    const response = await api.get('/getFlights', { params });
    return response.data.contents.flight_Availability_Details;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Failed to fetch flights');
  }
};

// Request Pricing
export const requestPricing = async (tarifId, flightIds) => {
  const xmlPayload = xmlbuilder
    .create('pricingRequest', { encoding: 'UTF-8' })
    .att('xmlns:shared', 'http://ypsilon.net/shared')
    .att('tarifId', tarifId)
    .att('ptl', 'true')
    .ele('shared:flightIds')
    .ele(flightIds.map((id) => ({ flightId: id })))
    .end({ pretty: true });

  const headers = {
    Accept: 'application/xml',
    'api-version': '3.92',
    accessmode: 'agency',
    accessid: 'eflycha eflycha',
    authmode: 'pwd',
    session: sessionId || 'd115038fvyev7qypum0i29hctur01v', // Replace with dynamic session
    Authorization: 'Basic ZWZseWNoOlFSWlRDdWM5OExfSUpDVmRZRGxrZnVXQVEyd0pXbSFE',
  };

  try {
    const response = await pricingApi.post('/', xmlPayload, { headers });
    return parseXMLResponse(response.data); // Implement parsing logic
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Pricing request failed');
  }
};

// Book Flight
export const bookFlight = async (bookingData) => {
  const headers = {
    session_id: sessionId || 'd11503nwarsd9an0pnlk1c9ilfkxdl', // Replace with dynamic session
    'Content-Type': 'application/json',
  };

  try {
    const response = await api.post('/bookFlight', bookingData, { headers });
    return response.data;
  } catch (error) {
    throw new Error(error.response?.data?.message || 'Booking failed');
  }
};

// Parse XML Response (placeholder)
const parseXMLResponse = (xml) => {
  // Add real XML parsing logic here (e.g., using xml2js)
  return { totalPrice: 'CHF 1234.56' }; // Placeholder
};

export default {
  searchFlights,
  requestPricing,
  bookFlight,
  setSessionId,
};