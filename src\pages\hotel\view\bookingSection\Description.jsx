import React, { useState } from 'react';
import { MapPin } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { Link } from 'react-router-dom';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import { motion } from 'framer-motion';
import FacilitiesIcon from '../FacilitiesIcon';
import ButtonCom from '../../../../components/ui/button/ButtonCom';
import { useHotelContext } from '../../../../context/HotelContext';
import { calculateNightsAndTravelers, defaultFacilities, defaultRoomFeatures } from '../../../../utils/hotelUtils';

delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
});

const Description = () => {
  const { hotelDetails, searchCriteria } = useHotelContext();
  const [showFullNotes, setShowFullNotes] = useState(false);
  const { nights, dateRangeText } = calculateNightsAndTravelers(searchCriteria.dateRange, searchCriteria);

  const facilities = hotelDetails.facilities?.length > 0 ? hotelDetails.facilities : defaultFacilities;
  const roomFeatures = hotelDetails.rooms?.length > 0
    ? hotelDetails.rooms.map((room) => ({ label: room.roomTypeName }))
    : defaultRoomFeatures;

  const mealPlans = hotelDetails.rooms?.[0]?.rateBases?.[0]?.availableDates?.flatMap((date) =>
    date.mealIncludes?.map((meal) => meal.mealName || meal.mealType) || []
  ) || [];
  const mealText = mealPlans.length > 0 ? `Meals: ${[...new Set(mealPlans)].join(', ')}` : 'Room Only; meals available for additional charge';

  const cheapestRoom = hotelDetails.rooms?.reduce((cheapest, roomType) => {
    if (!roomType.rateBases) return cheapest;
    const cheapestRate = roomType.rateBases.reduce((minRate, rate) => {
      if (!rate.totalCharge || rate.totalCharge >= minRate.totalCharge) return minRate;
      return { ...rate, roomTypeName: roomType.roomTypeName, roomTypeCode: roomType.roomTypeCode || `ROOM_${roomType.id || 0}` };
    }, { totalCharge: Infinity, roomTypeName: null, roomTypeCode: null });
    return cheapestRate.totalCharge < cheapest.totalCharge ? cheapestRate : cheapest;
  }, { totalCharge: Infinity, roomTypeName: null, roomTypeCode: null });

  const selectedRoom = {
    roomTypeName: cheapestRoom.roomTypeName || 'Standard Room',
    totalCharge: cheapestRoom.totalCharge !== Infinity ? cheapestRoom.totalCharge : 0,
    rateBaseId: cheapestRoom.rateBaseId || 0,
    roomTypeCode: cheapestRoom.roomTypeCode || 'ROOM_DEFAULT',
    benefits: cheapestRoom.cancellationRules?.map((rule) => {
      if (rule.type === 'free_cancellation') return { text: 'Free Cancellation', condition: rule.toDateDetails, included: true };
      if (rule.type === 'penalty_period' && rule.cancelCharge > 0) return { text: `Cancellation Fee: CHF ${rule.formattedCancelCharge}`, condition: rule.fromDateDetails, included: false };
      if (rule.type === 'no_show' && rule.noShowPolicy) return { text: `No-Show Fee: CHF ${rule.formattedCharge}`, condition: rule.fromDateDetails, included: false };
      return null;
    }).filter(Boolean) || [],
    cancellationRules: cheapestRoom.cancellationRules || [],
    availableDates: cheapestRoom.availableDates || [],
    rateBaseName: cheapestRoom.rateBaseName || 'Room Only',
    currencyCode: cheapestRoom.currencyCode || 'USD',
    tariffNotes: cheapestRoom.tariffNotes || '',
    totalTaxes: cheapestRoom.totalTaxes || '0.00',
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="w-full flex flex-col lg:flex-row lg:space-x-10 px-6 py-3 md:px-0 md:py-0"
    >
      <div className="flex flex-col space-y-10 lg:w-4/6 text-left">
        <p className="text-smokyGray text-sm">
          You might be eligible for a Genius discount at {hotelDetails.name}.{' '}
          <a href="#" className="text-darkBlue underline">sign in</a>.
        </p>
        <div className="space-y-2">
          <h6 className="font-semibold text-sm">Get the celebrity treatment with world-class service at {hotelDetails.name}</h6>
          <motion.div
            initial={{ height: showFullNotes ? 'auto' : '4.5rem' }}
            animate={{ height: showFullNotes ? 'auto' : '4.5rem' }}
            transition={{ duration: 0.3 }}
            className="text-xs font-light"
          >
            <div dangerouslySetInnerHTML={{ __html: hotelDetails.description }} />
            {hotelDetails.description.length > 300 && (
              <button onClick={() => setShowFullNotes(!showFullNotes)} className="text-darkBlue text-xs mt-1 hover:underline">
                {showFullNotes ? 'Show Less' : 'Read More'}
              </button>
            )}
          </motion.div>
        </div>
        <div className="space-y-4">
          <h6 className="font-medium text-sm">Most Popular Facilities</h6>
          <div className="flex flex-wrap gap-y-4 gap-x-6 text-sm">
            {facilities.map(({ label }, index) => (
              <span key={index} className="flex items-center space-x-3 text-[13px] font-light">
                <FacilitiesIcon label={label} />
                <span>{label}</span>
              </span>
            ))}
          </div>
        </div>
        {/* Map remains the same */}
      </div>
      <div className="flex flex-col w-full h-auto lg:w-2/6 mt-4 lg:mt-0 bg-lightBlue p-6 rounded-[8px] text-sm space-y-4">
        <h2 className="font-semibold">Property Highlights</h2>
        <div className="space-y-2">
          <h3 className="font-medium">Perfect for {nights}-night stay!</h3>
          <div className="flex items-start space-x-2 text-xs text-smokyGray">
            <MapPin className="w-5 mt-0.5 flex-shrink-0" strokeWidth={1} />
            <p>Top location: Highly rated by recent guests (8.9)</p>
          </div>
        </div>
        <Link to="/finaldetails" state={{ bookingDetails: { hotelDetails, searchCriteria, selectedRoom, nights, dateRangeText } }}>
          <ButtonCom variant="primary" size="md" width="full" rounded="md">Reserve</ButtonCom>
        </Link>
      </div>
    </motion.div>
  );
};

export default Description;