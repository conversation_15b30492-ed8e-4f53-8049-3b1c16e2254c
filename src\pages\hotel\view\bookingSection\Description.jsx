import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>, Star } from 'lucide-react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-leaflet';
import { Link } from 'react-router-dom';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';
import FacilitiesIcon from '../FacilitiesIcon';
import ButtonCom from '../../../../components/ui/button/ButtonCom';

// Leaflet icon configuration
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon-2x.png',
  iconUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-icon.png',
  shadowUrl: 'https://unpkg.com/leaflet@1.9.4/dist/images/marker-shadow.png',
});

const Description = ({ hotelDetails, searchCriteria }) => {
  // Normalize searchCriteria with default values
  const normalizedSearchCriteria = {
    adults: searchCriteria?.adults || 2,
    children: searchCriteria?.children || 0,
    rooms: searchCriteria?.rooms || 1,
    dateRange: searchCriteria?.dateRange || {
      startDate: new Date('2025-09-01'),
      endDate: new Date('2025-09-05'),
    },
    cityCode: searchCriteria?.cityCode || 7674,
    destination: searchCriteria?.destination || 'PARIS, FRANCE',
    flexibleDays: searchCriteria?.flexibleDays || 0,
    promoCode: searchCriteria?.promoCode || null,
  };

  // Log searchCriteria for debugging
  console.log('Description - Received searchCriteria:', searchCriteria);
  console.log('Description - Normalized searchCriteria:', normalizedSearchCriteria);

  // Star rating calculation (same as WellcomSection.jsx)
  const getStarCount = () => {
    if (hotelDetails.classificationBody) {
      const starsFromName = hotelDetails.classificationBody.classificationName?.match(/\*+/)?.[0]?.length;
      if (starsFromName) return starsFromName;

      const starsFromRating = hotelDetails.classificationBody.rating?.match(/(\d+)\s*Star/i)?.[1];
      if (starsFromRating) return parseInt(starsFromRating, 10);

      const classificationId = hotelDetails.classificationBody.classificationId;
      if (classificationId) {
        switch (classificationId) {
          case 559: return 1;
          case 560: return 2;
          case 561: return 3;
          case 562: return 4;
          case 563: return 5;
          default: return 0;
        }
      }
    }

    if (hotelDetails.classificationCode) {
      switch (hotelDetails.classificationCode) {
        case 559: return 1;
        case 560: return 2;
        case 561: return 3;
        case 562: return 4;
        case 563: return 5;
        default: return 0;
      }
    }

    return 0;
  };

  const getClassificationName = () => {
    if (hotelDetails.classificationBody?.classificationName) {
      return hotelDetails.classificationBody.classificationName.replace(/\*+$/, '').trim();
    }
    if (hotelDetails.classificationBody?.rating) {
      return hotelDetails.classificationBody.rating;
    }
    return hotelDetails.classificationName || 'Unrated';
  };

  const starCount = getStarCount();
  const classificationName = getClassificationName();

  // Update hotelDetails with starCount and classificationName
  const processedHotelDetails = {
    ...hotelDetails,
    starCount,
    classificationName,
  };

  const facilities = hotelDetails?.facilities?.length > 0
    ? hotelDetails.facilities
    : [
      { label: 'Room' },
      { label: 'Free wifi' },
      { label: 'Pool with view' },
      { label: 'Balcony' },
      { label: 'Air conditioning' },
      { label: 'Mini Bar' },
      { label: 'Soundproofing' },
      { label: 'Roof Top pool' },
    ];

  const roomFeatures = hotelDetails?.rooms?.length > 0
    ? hotelDetails.rooms.map((room) => ({
      label: room.roomTypeName,
    }))
    : [
      { label: 'Garden view' },
      { label: 'Pool view' },
      { label: 'City view' },
      { label: 'Free private parking available at the hotel' },
    ];

  const mealPlans = hotelDetails?.rooms?.[0]?.rateBases?.[0]?.availableDates?.flatMap((date) =>
    date.mealIncludes?.map((meal) => meal.mealName || meal.mealType) || []
  ) || [];
  const uniqueMealPlans = [...new Set(mealPlans)];
  const mealText = uniqueMealPlans.length > 0
    ? `Meals: ${uniqueMealPlans.join(", ")}`
    : "Room Only; meals available for additional charge";

  const hotelFeatures = [
    hotelDetails?.hotelPhone ? { label: `Phone: ${hotelDetails.hotelPhone}` } : null,
    hotelDetails?.checkInTime ? { label: `Check-in: ${hotelDetails.checkInTime}` } : null,
    hotelDetails?.checkOutTime ? { label: `Check-out: ${hotelDetails.checkOutTime}` } : null,
  ].filter(Boolean);

  const [showFullNotes, setShowFullNotes] = useState(false);

  const toggleNotes = () => {
    setShowFullNotes((prev) => !prev);
  };

  const calculateNightsAndTravelers = () => {
    let nights = 1;
    let dateRangeText = 'Select dates';
    try {
      const startDate = new Date(normalizedSearchCriteria.dateRange.startDate);
      const endDate = new Date(normalizedSearchCriteria.dateRange.endDate);
      if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
        if (endDate >= startDate) {
          nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
          const formatter = new Intl.DateTimeFormat('en-US', {
            month: 'short',
            day: '2-digit',
            year: 'numeric',
          });
          dateRangeText = `${formatter.format(startDate)} - ${formatter.format(endDate)}`;
        } else {
          console.warn('Invalid date range: endDate is before startDate');
          dateRangeText = 'Invalid dates';
        }
      }
    } catch (error) {
      console.error('Error calculating nights:', error);
    }
    return { nights, dateRangeText };
  };

  const { nights, dateRangeText } = calculateNightsAndTravelers();

  const cheapestRoom = hotelDetails.rooms?.reduce((cheapest, roomType) => {
    if (!roomType.rateBases) return cheapest;
    const cheapestRate = roomType.rateBases.reduce((minRate, rate) => {
      if (!rate.totalCharge || rate.totalCharge >= minRate.totalCharge) return minRate;
      return {
        ...rate,
        roomTypeName: roomType.roomTypeName,
        roomTypeCode: roomType.roomTypeCode || `ROOM_${roomType.id || 0}`,
        allocationDetails: rate.allocationDetails || "",
      };
    }, { totalCharge: Infinity, roomTypeName: null, roomTypeCode: null, allocationDetails: "" });
    if (cheapestRate.totalCharge < cheapest.totalCharge) {
      return cheapestRate;
    }
    return cheapest;
  }, { totalCharge: Infinity, roomTypeName: null, roomTypeCode: null, allocationDetails: "" });

  const selectedRoom = {
    roomTypeName: cheapestRoom.roomTypeName || 'Standard Room',
    totalCharge: cheapestRoom.totalCharge !== Infinity ? cheapestRoom.totalCharge : 0,
    rateBaseId: cheapestRoom.rateBaseId || 0,
    roomTypeCode: cheapestRoom.roomTypeCode || "ROOM_DEFAULT",
    allocationDetails: cheapestRoom.allocationDetails || "",
    isBookable: cheapestRoom.isBookable !== undefined ? cheapestRoom.isBookable : true,
    benefits: cheapestRoom.cancellationRules?.map((rule) => {
      if (rule.type === "penalty_period" && rule.cancelCharge === 0) {
        return {
          text: "Free Cancellation",
          condition: rule.toDateDetails || "Before check-in",
          included: true,
        };
      } else if (rule.type === "penalty_period" && rule.cancelCharge > 0) {
        return {
          text: `Cancellation Fee: CHF ${rule.formattedCancelCharge}`,
          condition: rule.fromDateDetails
            ? `From ${rule.fromDateDetails}${rule.toDateDetails ? ` to ${rule.toDateDetails}` : ''}`
            : "During penalty period",
          included: false,
        };
      } else if (rule.type === "no_show" && rule.noShowPolicy && !rule.cancelRestricted) {
        return {
          text: "Pay at Property",
          included: true,
        };
      } else if (rule.type === "no_show" && rule.noShowPolicy) {
        return {
          text: `No-Show Fee: CHF ${rule.formattedCharge}`,
          condition: rule.fromDateDetails || "On check-in date",
          included: false,
        };
      }
      return null;
    }).filter(Boolean) || [],
    leftToSell: cheapestRoom.leftToSell || 0,
    selectedRoomCounts: {},
    cancellationRules: cheapestRoom.cancellationRules || [],
    availableDates: cheapestRoom.availableDates || [],
    rateBaseName: cheapestRoom.rateBaseName || 'Room Only',
    currencyCode: cheapestRoom.currencyCode || 'USD',
    tariffNotes: cheapestRoom.tariffNotes || '',
    totalTaxes: cheapestRoom.totalTaxes || '0.00',
  };

  console.log('Description - bookingDetails:', {
    hotelDetails: processedHotelDetails,
    searchCriteria: normalizedSearchCriteria,
    selectedRoom,
  });

  return (
    <div className="w-full flex flex-col lg:flex-row lg:space-x-10 px-6 py-3 md:px-0 md:py-0">
      <div className="flex flex-col space-y-10 lg:w-4/6 text-left">
        <p className="text-smokyGray text-sm">
          You might be eligible for a Genius discount at {hotelDetails?.name || 'this hotel'}. To check if a Genius discount is available for your selected dates{' '}
          <a href="#" className="text-darkBlue underline">
            sign in
          </a>.
        </p>

        <div className="space-y-2">
          <h6 className="font-semibold text-sm">Get the celebrity treatment with world-class service at {hotelDetails?.name || 'this hotel'}</h6>
          <div className="flex flex-col space-y-6 text-xs font-light">
            <div className={`${showFullNotes ? '' : 'line-clamp-3'} transition-all duration-300`}>
              <div dangerouslySetInnerHTML={{ __html: hotelDetails?.description || 'No description available.' }} />
            </div>
            {hotelDetails?.description.length > 300 && (
              <button
                onClick={toggleNotes}
                className="text-darkBlue text-xs mt-1 hover:underline"
              >
                {showFullNotes ? 'Show Less' : 'Read More'}
              </button>
            )}
          </div>
        </div>

        <div className="space-y-4">
          <h6 className="font-medium text-sm">Most Popular Facilities</h6>
          <div className="flex flex-wrap gap-y-4 gap-x-6 text-sm">
            {facilities.map(({ label }, index) => (
              <span
                key={index}
                className="flex items-center space-x-3 text-[13px] font-light"
              >
                <FacilitiesIcon label={label} />
                <span>{label}</span>
              </span>
            ))}
          </div>
        </div>
        <div className="rounded-[8px] h-[246px] inset-0 w-full z-0">
          {hotelDetails.geoLat && hotelDetails.geoLong ? (
            <MapContainer
              center={[hotelDetails.geoLat, hotelDetails.geoLong]}
              zoom={15}
              style={{ height: '100%', width: '100%', borderRadius: '2px' }}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='© <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
              <Marker position={[hotelDetails.geoLat, hotelDetails.geoLong]}>
                <Popup>
                  <div className="space-y-2 text-darkBlue text-xs relative">
                    <h3 className="font-medium">{hotelDetails.name}</h3>
                    <p>{hotelDetails.address}</p>
                  </div>
                </Popup>
              </Marker>
            </MapContainer>
          ) : (
            <div className="w-full h-full bg-gray-200 flex items-center justify-center text-sm">
              Map unavailable: Location data missing
            </div>
          )}
        </div>
      </div>

      <div className="flex flex-col w-full h-auto lg:w-2/6 mt-4 lg:mt-0 bg-lightBlue p-6 rounded-[8px] text-sm space-y-4">
        <h2 className="font-semibold">Property Highlights</h2>

        <div className="space-y-2">
          <h3 className="font-medium">Perfect for {nights}-night stay!</h3>
          <div className="flex items-start space-x-2 text-xs text-smokyGray">
            <MapPin className="w-5 mt-0.5 flex-shrink-0" strokeWidth={1} />
            <p>Top location: Highly rated by recent guests (8.9)</p>
          </div>
          <p className="text-xs text-smokyGray ml-7">Popular with groups of friends</p>
        </div>

        <div className="space-y-2">
          <h3 className="font-medium">Meal Plans</h3>
          <p className="text-xs text-smokyGray ml-7">
            {mealText}
          </p>
        </div>

        <div className="space-y-2">
          <h3 className="font-medium">Rooms with</h3>
          <div className="space-y-2">
            {roomFeatures.map((feature, index) => (
              <div
                key={index}
                className="border border-border px-3 py-1 rounded-[8px] flex items-center space-x-2 text-xs font-light"
              >
                <FacilitiesIcon label={feature.label} />
                <span className="text-xs text-smokyGray">{feature.label}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="font-medium">Hotel Information</h3>
          <div className="space-y-2">
            {hotelFeatures.map((feature, index) => (
              <div
                key={index}
                className="border border-border px-3 py-1 rounded-[8px] flex items-center space-x-2 text-xs font-light"
              >
                <FacilitiesIcon label={feature.label} />
                <span className="text-xs text-smokyGray">{feature.label}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="space-y-2">
          <h3 className="font-medium">Loyal customer</h3>
          <p className="text-xs text-smokyGray ml-7">
            There are more repeat guests here than most other properties.
          </p>
        </div>

        <Link
          to="/finaldetails"
          state={{
            bookingDetails: {
              hotelDetails: processedHotelDetails,
              searchCriteria: normalizedSearchCriteria,
              selectedRoom,
              nights,
              dateRangeText,
            },
          }}
        >
          <ButtonCom variant="primary" size="md" width="full" rounded="md">
            Reserve
          </ButtonCom>
        </Link>
      </div>
    </div>
  );
};

export default Description;