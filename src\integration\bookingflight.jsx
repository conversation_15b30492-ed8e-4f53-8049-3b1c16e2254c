import { useState } from "react";
import { Formik, Field, Form, ErrorMessage } from "formik";
import * as Yup from "yup";
import { ChevronDown, ChevronUp, Clock, Bookmark, Moon, Star, ArrowLeftRight } from "lucide-react";
import OutboundFlight from "../assets/view/OutboundFlight.svg";
import ReturnFlight from "../assets/view/ReturnFlight.svg";
import Line from "../assets/view/Arrow.svg";
import QatarAirwaysLogo from "../assets/view/Qatar_Airways_Logo.svg";
import { useSelector } from "react-redux";

const BookingFlight = () => {
  const { flights, dep_date, des_date, loading, error } = useSelector(
    (state) => state.flightDetails
  );

  const initialValues = {
    outboundFlight: "",
    returnFlight: "",
  };

  const validationSchema = Yup.object({
    outboundFlight: Yup.string().required("Please select an outbound flight"),
    returnFlight: Yup.string().required("Please select a return flight"),
  });

  const handleSubmit = (values) => {
    console.log("Selected Flights:", values);
  };

  const outboundFlights = flights[dep_date] || [];
  const returnFlights = flights[des_date] || [];

  const [expandedFlight, setExpandedFlight] = useState(null);

  const toggleFlightDetails = (flightId) => {
    setExpandedFlight(expandedFlight === flightId ? null : flightId);
  };

  const renderExpandedFlightDetails = (flightItem) => {
    return (
      <div className="p-6 w-full bg-white text-sm border border-border rounded-3xl font-inter">
      <div className="relative flex flex-col w-full max-w-3xl mx-auto">
        {/* Travel Time Label at Top */}
        <div className="flex items-center justify-start gap-2 mb-4 ml-20">
          <Clock size={14} className="text-smokyGray" />
          <span className="text-smokyGray text-base">Travel Time {flightItem.duration}</span>
        </div>

        {/* Main Flight Journey Container */}
        <div className="flex relative">
          <div className="w-24 text-end text-sm flex flex-col">
            {/* Origin details */}
            <div className="mb-40 mt-2">
              <div className="text-smokyGray">Monday</div>
              <div className="text-smokyGray">{flightItem.departure_date}</div>
              <div className="text-smokyGray font-semibold">{flightItem.departure_time}</div>
              <div className="text-smokyGray mt-1">{flightItem.fno}</div>
            </div>
            {/* Destination details */}
            <div>
              <div className="text-smokyGray">Monday</div>
              <div className="text-smokyGray">{flightItem.arrival_date}</div>
              <div className="text-smokyGray font-semibold">{flightItem.arrival_time}</div>
            </div>
          </div>

          {/*Connection Points and Line */}
          <div className="relative w-12 mx-1">
            {/* Airline Logo */}
            <div className="absolute top-32 left-10 z-10">
              <div className="lg:w-16 lg:h-16 w-14 h-14 lg:mt-4 mt-12 rounded-full border border-border flex items-center justify-center bg-white">
                <img
                  src={flightItem.airlineCode || QatarAirwaysLogo}
                  alt="Airline Logo"
                  className="w-11 h-11 object-contain"
                />
              </div>
            </div>

            {/* Connection Line, Origin Point (Filled), Destination Point (Outlined) */}
            <div className="absolute top-2 left-1/2 w-0.5 h-64 bg-border transform -translate-x-1/2"></div>
            <div className="absolute top-2 left-1/2 w-4 h-4 rounded-full bg-darkBlue transform -translate-x-1/2"></div>
            <div className="absolute bottom-12 left-1/2 w-4 h-4 rounded-full border border-darkBlue bg-white transform -translate-x-1/2"></div>
          </div>

          {/* Airport Names and Flight Details */}
          <div className="flex-1 flex flex-col text-sm">
            {/* Origin Airport */}
            <div className="mb-2 mt-2">
              <span className="text-smokyGray font-medium">{flightItem.departure_airport || 'Colombo (CMB), intl'}</span>
            </div>

            {/* Flight Details Grid */}
            <div className="lg:grid lg:grid-cols-3 gap-x-4 gap-y-2 lg:my-6 text-sm">
              <div className="flex items-center font-light">
                <Clock size={14} className="mr-2 text-smokyGray" />
                <span className="text-smokyGray ">Duration: {flightItem.duration}</span>
              </div>
              <div className="flex items-center font-light">
                <Bookmark size={14} className="mr-2 text-smokyGray" />
                <span className="text-smokyGray">Lowcost: {flightItem.cost}</span>
              </div>
              <div className="flex items-center font-light">
                <Star size={14} className="mr-2 text-smokyGray" />
                <span className="text-smokyGray">Class: {flightItem.class || 'Economy'}</span>
              </div>
              <div className="flex items-center font-light">
                <ArrowLeftRight size={14} className="mr-2 text-smokyGray" />
                <span className="text-smokyGray">Distance: {flightItem.distance} km</span>
              </div>
              <div className="flex items-center font-light">
                <Moon size={14} className="mr-2 text-smokyGray" />
                <span className="text-smokyGray">{flightItem.overnight ? 'Overnight' : 'Daytime'}</span>
              </div>
            </div>

            {/* Destination Airport */}
            <div className="mt-auto mb-12">
              <span className="text-smokyGray font-medium">{flightItem.arrival_airport || 'Dubai (DXB)'}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    );
  };
  

  return (
    <div className="flex flex-col space-y-10 w-full">
      <div className="overflow-x-scroll md:overflow-visible scrollbar-hide">
        <div className="rounded-lg md:rounded-3xl border border-border text-smokyGray p-2 md:p-4 lg:p-8 w-fit md:w-full">
          <Formik
            initialValues={initialValues}
            validationSchema={validationSchema}
            onSubmit={handleSubmit}
          >
            {({ isSubmitting }) => (
              <Form className="space-y-8">
                {/* Outbound */}
                <div className="flex flex-row justify-between items-center p-2 md:p-4 md:pr-10 bg-[#FAFAFA] rounded-2xl">
                  <div className="flex items-center space-x-4">
                    <img src={OutboundFlight} alt="Outbound Flight" />
                    <div className="">Outbound</div>
                  </div>
                  <div className="w-[70px] h-[70px] border border-border rounded-full p-2 flex items-center justify-center">
                    <img
                      src={outboundFlights[0]?.airlineCode || QatarAirwaysLogo}
                      alt="Airline Logo"
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>

                <div className="flex flex-col space-y-10">
                  {outboundFlights.map((flightItem) => (
                    <div key={flightItem.fno} className="w-full">
                      <div className="flex flex-row justify-between items-center md:pl-4 space-x-4 mb-2">
                        <label className="flex items-center justify-between cursor-pointer w-full">
                          <div className="flex flex-row justify-between items-center w-full">
                            <Field
                              type="radio"
                              name="outboundFlight"
                              value={flightItem.fno}
                              className="w-5 h-5 accent-darkBlue"
                            />
                            <div className="flex flex-col items-start ml-4">
                              <span className="text-sm text-smokyGray">{flightItem.departure_date}</span>
                              <span className="font-medium text-smokyGray">{flightItem.departure_time}</span>
                              <span className="text-sm text-smokyGray">{flightItem.fno}</span>
                            </div>
                            <div className="flex flex-col items-center mx-4">
                              <span className="text-xs text-smokyGray">{flightItem.duration}</span>
                              <img src={Line} alt="Line" className="w-32" />
                              <span className="text-xs text-smokyGray">{flightItem.stops || 'Direct'}</span>
                            </div>
                            <div className="flex flex-col items-end">
                              <span className="text-sm text-smokyGray">{flightItem.arrival_date}</span>
                              <span className="font-medium text-smokyGray">{flightItem.arrival_time}</span>
                            </div>
                          </div>
                        </label>
                        <div
                          onClick={() => toggleFlightDetails(flightItem.fno)}
                          className="cursor-pointer bg-gray-100 p-2 rounded-full"
                        >
                          {expandedFlight === flightItem.fno ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                        </div>
                      </div>
                      {expandedFlight === flightItem.fno && renderExpandedFlightDetails(flightItem)}
                    </div>
                  ))}
                </div>
                <ErrorMessage name="outboundFlight" component="div" className="text-red text-sm" />

                {/* Return Flight */}
                <div className="flex flex-row justify-between items-center p-2 md:p-4 md:pr-10 bg-[#FAFAFA] rounded-2xl">
                  <div className="flex items-center space-x-4">
                    <img src={ReturnFlight} alt="Return Flight" />
                    <div className="">Return Flight</div>
                  </div>
                  <div className="w-[70px] h-[70px] border border-border rounded-full p-2 flex items-center justify-center">
                    <img
                      src={returnFlights[0]?.airlineCode || QatarAirwaysLogo}
                      alt="Airline Logo"
                      className="w-full h-full object-contain"
                    />
                  </div>
                </div>

                <div className="flex flex-col space-y-10">
                  {returnFlights.map((flightItem) => (
                    <div key={flightItem.fno} className="w-full">
                      <div className="flex flex-row justify-between items-center md:pl-4 space-x-4 mb-2">
                        <label className="flex items-center justify-between cursor-pointer w-full">
                          <div className="flex flex-row justify-between items-center w-full">
                            <Field
                              type="radio"
                              name="returnFlight"
                              value={flightItem.fno}
                              className="w-5 h-5 accent-darkBlue"
                            />
                            <div className="flex flex-col items-start ml-4">
                              <span className="text-sm text-smokyGray">{flightItem.departure_date}</span>
                              <span className="font-medium text-smokyGray">{flightItem.departure_time}</span>
                              <span className="text-sm text-smokyGray">{flightItem.fno}</span>
                            </div>
                            <div className="flex flex-col items-center mx-4">
                              <span className="text-xs text-smokyGray">{flightItem.duration}</span>
                              <img src={Line} alt="Line" className="w-32" />
                              <span className="text-xs text-smokyGray">{flightItem.stops || 'Direct'}</span>
                            </div>
                            <div className="flex flex-col items-end">
                              <span className="text-sm text-smokyGray">{flightItem.arrival_date}</span>
                              <span className="font-medium text-smokyGray">{flightItem.arrival_time}</span>
                            </div>
                          </div>
                        </label>
                        <div
                          onClick={() => toggleFlightDetails(flightItem.fno)}
                          className="cursor-pointer bg-gray-100 p-2 rounded-full"
                        >
                          {expandedFlight === flightItem.fno ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                        </div>
                      </div>
                      {expandedFlight === flightItem.fno && renderExpandedFlightDetails(flightItem)}
                    </div>
                  ))}
                </div>
                <ErrorMessage name="returnFlight" component="div" className="text-red text-sm" />

                {returnFlights[0]?.availableSeat && (
                  <div className="text-orange">
                    {returnFlights[0]?.availableSeat} seats Available
                  </div>
                )}

                <div className="flex flex-col md:flex-row justify-between items-center">
                  <div className="flex flex-col md:flex-row md:items-center md:space-x-4 mb-4 md:mb-0">
                    <span className="text-smokyGray text-xl md:text-3xl font-medium text-nowrap">
                      LKR 160000
                    </span>
                    <div className="flex flex-col">
                      <span className="text-sm">Price Per Person</span>
                      <div className="">
                        Total price 2 x Adult
                        <span className="font-medium ml-2">LKR 320000</span>
                      </div>
                    </div>
                  </div>
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-darkBlue text-2xl hover:bg-blue-600 text-white font-medium py-4 px-6 rounded-2xl w-full md:w-auto"
                  >
                    Book
                  </button>
                </div>
              </Form>
            )}
          </Formik>
        </div>
      </div>
    </div>
  );
};

export default BookingFlight;