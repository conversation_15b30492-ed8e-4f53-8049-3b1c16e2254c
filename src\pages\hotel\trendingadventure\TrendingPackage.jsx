import React, { useState, useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { setPriceRange, fetchHotelIdsByCity, fetchFilterCounts, fetchFilteredHotels, clearHotels } from '../../../store/hotelSlice';
import TrendingFilter from './TrendingFilter';
import HotelView from './HotelView';
import HotelList from '../../topcitypackage/HotelList';
import ShortBy from '../../../components/ui/shortby';
import HotelLoading from './HotelLoading';
import debounce from 'lodash/debounce';
import { BsFilterRight } from 'react-icons/bs';
import SearchForm2 from '../../home/<USER>/SearchForm2';

const TrendingPackage = () => {
  const priceRange = useSelector((state) => state.hotels.priceRange);
  const [selectedTags, setSelectedTags] = useState([]);
  const [isFilterVisible, setFilterVisible] = useState(false);
  const [isListView, setIsListView] = useState(false);
  const location = useLocation();
  const dispatch = useDispatch();
  const {
    hotels,
    totalHotels,
    filteredHotels,
    totalFilteredHotels,
    filterCounts,
    loading,
    error,
    filterLoading,
    filterError,
    filteredHotelsLoading,
    filteredHotelsError,
  } = useSelector((state) => state.hotels);
  const searchCriteria = location.state?.formData || {};

   // Log Redux state for debugging
  useEffect(() => {
    console.log('Redux State:', { hotels, filteredHotels, totalHotels, totalFilteredHotels });
  }, [hotels, filteredHotels, totalHotels, totalFilteredHotels]);

  // Generate room requests
  const generateRoomRequests = (roomCount, totalAdults, totalChildren) => {
    const roomRequests = [];
    const adults = totalAdults || 2;
    const children = totalChildren || 0;
    const rooms = Math.max(1, roomCount || 1);

    for (let i = 0; i < rooms; i++) {
      const adultsPerRoom =
        Math.floor(adults / rooms) + (i < adults % rooms ? 1 : 0);
      const childrenPerRoom =
        Math.floor(children / rooms) + (i < children % rooms ? 1 : 0);
      roomRequests.push({
        adultsCount: Math.max(1, adultsPerRoom),
        childCount: childrenPerRoom,
        rateBasisCode: -1,
        passengerNationalityCode: '81',
        passengerCountryOfResidenceCode: '72',
      });
    }
    return roomRequests;
  };

  // Prepare request body for getSavedAvailableHotelDetailsByCity
  const requestBody = useMemo(() => {
    if (!searchCriteria.cityCode) return null;
    return {
      fromDate: searchCriteria.dateRange.startDate.toISOString().split('T')[0],
      toDate: searchCriteria.dateRange.endDate.toISOString().split('T')[0],
      currencyCode: '394',
      cityCode: searchCriteria.cityCode,
      isNearbyCities: true,
      roomCount: searchCriteria.rooms || 1,
      roomRequests: generateRoomRequests(
        searchCriteria.rooms,
        searchCriteria.adults,
        searchCriteria.children
      ),
    };
  }, [searchCriteria]);

  // Fetch hotels
  useEffect(() => {
    if (requestBody) {
      dispatch(fetchHotelIdsByCity(requestBody));
    }
    return () => {
      dispatch(clearHotels());
    };
  }, [dispatch, requestBody]);

  // Fetch filter counts when hotels change
  useEffect(() => {
    if (hotels.length > 0) {
      const hotelIds = hotels.map((hotel) => hotel.hotelId);
      dispatch(fetchFilterCounts(hotelIds));
    }
  }, [dispatch, hotels]);

  // Debounced function to fetch filtered hotels
  const debouncedFetchFilteredHotels = useMemo(
    () =>
      debounce((filterRequest) => {
        console.log('Fetching filtered hotels with request:', filterRequest);
        dispatch(fetchFilteredHotels(filterRequest));
      }, 500),
    [dispatch]
  );

  // Fetch filtered hotels when filters are applied
  useEffect(() => {
    if (hotels.length === 0) return;

    const facilityFilterRequest = {
      amenitieIds: selectedTags
        .filter((tag) => tag.type === 'amenities')
        .map((tag) => tag.id),
      leisureIds: selectedTags
        .filter((tag) => tag.type === 'leisure')
        .map((tag) => tag.id),
      businessIds: selectedTags
        .filter((tag) => tag.type === 'business')
        .map((tag) => tag.id),
    };

    const classificationCodes = selectedTags
      .filter((tag) => tag.type === 'classification')
      .map((tag) => tag.id);

    if (priceRange[0] >= priceRange[1]) {
      console.error("Invalid price range");
      return;
    }

    const filterRequest = {
      hotelIds: hotels.map((hotel) => hotel.hotelId),
      hotelName: '',
      classificationCode: classificationCodes[0] || null,
      facilityFilterRequest: {
        amenitieIds: facilityFilterRequest.amenitieIds,
        leisureIds: facilityFilterRequest.leisureIds,
        businessIds: facilityFilterRequest.businessIds,
      },
    };

    console.log("Verifying prices:", hotels.map(h => h.cheapestRoomCharge));
    console.log('Applying filters:', filterRequest);
    debouncedFetchFilteredHotels(filterRequest);

    return () => debouncedFetchFilteredHotels.cancel();
  }, [hotels, selectedTags, priceRange, debouncedFetchFilteredHotels, dispatch]);

  const toggleTag = (label, id, type) => {
    setSelectedTags((prev) => {
      if (type === 'classification') {
        const otherTags = prev.filter((tag) => tag.type !== 'classification');
        const tagExists = prev.some((tag) => tag.label === label && tag.type === 'classification');
        if (tagExists) {
          return otherTags;
        }
        return [...otherTags, { label, id, type }];
      }
      const tagExists = prev.some((tag) => tag.label === label);
      if (tagExists) {
        return prev.filter((tag) => tag.label !== label);
      }
      return [...prev, { label, id, type }];
    });
  };

  const handlePriceRangeChange = (newValues) => {
    dispatch(setPriceRange(newValues));
  };

  const toggleFilterSidebar = () => {
    setFilterVisible(!isFilterVisible);
  };

  const toggleView = () => {
    setIsListView((prev) => !prev);
  };

  if (!searchCriteria.cityCode) {
    return (
      <div className="w-full flex justify-center py-6 sm:py-10">
        <div className="w-full max-w-[95%] md:max-w-[80%] px-4">
          <div className="absolute top-0 left-0 w-full z-[60] flex justify-center">
            <div className="w-full max-w-[95%] md:max-w-[80%] px-4">
              <SearchForm2 initialData={searchCriteria} />
            </div>
          </div>
          <div className="text-center text-darkBlue mt-20 sm:mt-24">
            Please select a valid destination.
          </div>
        </div>
      </div>
    );
  }

  const Header = () => (
    <div className="flex flex-col sm:flex-row sm:items-center mb-4">
      <p className="font-medium text-lg">
        {loading ? (
          'Searching for hotels...'
        ) : error || filteredHotelsError ? (
          <span className="text-red-500">
            Error loading hotels.{' '}
            <button
              className="underline"
              onClick={() => requestBody && dispatch(fetchHotelIdsByCity(requestBody))}
            >
              Retry
            </button>
          </span>
        ) : totalFilteredHotels === 0 ? (
          'No Hotels Found'
        ) : (
          `${totalFilteredHotels} Hotels Found`
        )}
      </p>
      <div className="flex md:hidden w-full justify-end">
        <button
          className="flex items-center text-sm text-darkBlue ml-auto"
          onClick={toggleFilterSidebar}
          aria-label="Toggle filter sidebar"
        >
          <BsFilterRight className="w-5 h-5 mr-1" />
          Filter
        </button>
      </div>
      <div className="md:ml-auto flex items-center space-x-4">
       {/* <button
          className="text-sm md:text-base text-darkBlue cursor-pointer underline flex items-center"
          onClick={toggleView}
          aria-label={isListView ? 'Switch to Grid View' : 'Switch to List View'}
        >
          {isListView ? (
            <>
              <LayoutGrid className="w-5 h-5 mr-1" />
              Grid View
            </>
          ) : (
            <>
              <LayoutList className="w-5 h-5 mr-1" />
              List View
            </>
          )}
        </button> */}
        {/* <ShortBy /> */}
      </div>
    </div>
  );

  return (
    <div className="w-full flex justify-center px-4 py-6">
      <div className="w-full md:space-y-20 space-y-4 ">
        {/* Search Bar */}
          <div className="w-full ">
            <SearchForm2 initialData={searchCriteria} />
          </div>
        {/* Main Content */}
        <div className="flex flex-col space-y-4 mt-0 w-full md:max-w-[80%] mx-auto">
          <div className="flex flex-col lg:flex-row lg:gap-6 xl:gap-10">
            {/* Filter Sidebar */}
            <div
              className={`${
                isFilterVisible ? 'block' : 'hidden'
              } lg:block  lg:w-64 xl:w-80 overflow-y-auto no-scrollbar mt-11 bg-white p-4 lg:p-0 shadow-md lg:shadow-none`}
            >
              <button
                className="md:hidden absolute top-1  text-gray text-lg"
                onClick={toggleFilterSidebar}
                aria-label="Close filter sidebar"
              >
                ✕
              </button>
              <TrendingFilter
                selectedTags={selectedTags}
                onToggleTag={toggleTag}
                filterCounts={filterCounts}
                filterLoading={filterLoading}
                filterError={filterError}
                priceRange={priceRange}
                onPriceRangeChange={handlePriceRangeChange}
              />
            </div>
            {/* Hotel View Section */}
            <div className="w-full space-y-6">
              <Header />
              {selectedTags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {selectedTags.map((tag) => (
                    <span
                      key={`${tag.type}-${tag.id}`}
                      className="px-4 py-1 bg-blue-100 text-darkBlue rounded-[8px] text-sm"
                    >
                      {tag.label}
                      <button
                        onClick={() => toggleTag(tag.label, tag.id, tag.type)}
                        className="ml-2 text-darkBlue hover:text-red-500 font-bold"
                      >
                        ✕
                      </button>
                    </span>
                  ))}
                </div>
              )}
              <div className="flex-1">
                {loading ? (
                  <HotelLoading />
                ) : filterLoading || filteredHotelsLoading ? (
                  <div className="text-center text-darkBlue">Loading...</div>
                ) : error || filteredHotelsError ? (
                  <div className="text-center text-red">
                    Error: {error || filteredHotelsError}
                  </div>
                ) : !Array.isArray(filteredHotels) || filteredHotels.length === 0 ? (
                  <div className="text-center text-darkBlue">No hotels found</div>
                ) : (
                  <HotelView hotels={filteredHotels} />
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TrendingPackage;