import { <PERSON><PERSON>ronR<PERSON>, MapPin, Sun } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import React, { useMemo } from "react";
import CityCard2 from "../../assets/adventure/HotelImage1.svg";
import CityCard3 from "../../assets/adventure/HotelImage4.svg";
import CityCard4 from "../../assets/adventure/HotelImage5.svg";
import CityCard5 from "../../assets/adventure/HotelImage2.svg";
import HotelImage10 from "../../assets/loading/hotel images/img8.jpg";
import HotelImage6 from "../../assets/loading/hotel images/img9.jpg";
import HotelImage7 from "../../assets/loading/hotel images/img10.jpg";
import HotelImage8 from "../../assets/loading/hotel images/img6.jpg";
import HotelImage9 from "../../assets/loading/hotel images/img7.jpg";
import useSWR from "swr";
import ButtonCom from "../../components/ui/button/ButtonCom";

const hotelImages = [HotelImage6, HotelImage7, HotelImage8, HotelImage9, HotelImage10];

const formatDisplayDate = (dateString) => {
  if (!dateString) return "";
  return new Date(dateString).toLocaleDateString("en-US", {
    month: "short",
    day: "numeric",
    year: "numeric",
  });
};

const CityCards = ({ cardCity }) => {
  if (!cardCity) return null;

  return (
    <Link
      to={`/hotel-and-flight/${cardCity.hotelId || cardCity.id}`}
      state={{
        formData: {
          destination: `${cardCity.location}, ${cardCity.country}`,
          cityCode: cardCity.cityCode || null,
          dateRange: {
            startDate: cardCity.searchCriteria?.dateRange?.startDate || new Date(),
            endDate: cardCity.searchCriteria?.dateRange?.endDate || new Date(new Date().setDate(new Date().getDate() + 3))
          },
          adults: cardCity.searchCriteria?.adults || 2,
          rooms: cardCity.searchCriteria?.rooms || 1,
          children: cardCity.searchCriteria?.children || 0
        },
        selectedTab: 2
      }}
      className="relative w-full overflow-hidden rounded-xl bg-white shadow-lg group block"
    >
      <div className="relative h-[278px]">
        <img
          src={cardCity.image}
          alt={cardCity.title}
          className="h-full w-full object-cover transition-transform duration-500 ease-in-out group-hover:scale-110"
          loading="lazy"
        />
        <div className="absolute top-0 left-0 w-full flex items-center justify-end px-4 py-4 z-10">
          <div className="flex flex-row gap-1 items-center text-white bg-black/50 px-2 py-1 rounded-full">
            <div className="text-xs">From</div>
            <div className="font-inter">{cardCity.title}</div>
            <ChevronRight className="h-4 w-4 text-white" />
          </div>
        </div>

        <div className="absolute bottom-0 w-full pb-4 text-white bg-black bg-opacity-10 transform translate-y-0 opacity-100 group-hover:bg-opacity-20 transition-all duration-300 ease-in-out">
          <div className="px-6 pb-4 pt-1 text-white space-y-2">
            <h2 className="text-xs font-normal font-inter truncate" title={cardCity.hotelName || cardCity.location}>
              {cardCity.hotelName || cardCity.location}
            </h2>
            <div className="flex items-center gap-2 text-xs">
              <span className="font-inter font-light">{cardCity.country}</span>
              <span>•</span>
              <span className="font-inter font-light">{cardCity.offers} offers</span>
            </div>
            <div className="flex items-center gap-2 text-xs">
              <Sun className="w-4 h-4" />
              <span className="font-inter font-light">{cardCity.temperature} °C</span>
              <span>•</span>
              <span className="font-inter font-light">{cardCity.duration}</span>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

const citiesConfig = [
  { id: 1, cityCode: 4644, name: "Kandy", country: "Sri Lanka" },
  { id: 2, cityCode: 7674, name: "Paris", country: "France" },
  { id: 3, cityCode: 364, name: "Dubai", country: "UAE" },
  { id: 4, cityCode: 4594, name: "Colombo", country: "Sri Lanka" },
  { id: 5, cityCode: 20344, name: "Shanghai", country: "China" },
  { id: 6, cityCode: 1644, name: "Chennai", country: "India" },
  { id: 7, cityCode: 14864, name: "London", country: "UK" },
];

const staticCardCities = [
  {
    id: 1,
    title: "108765",
    location: "Bali",
    temperature: 22,
    duration: "3 nights - 2 Adults",
    country: "Indonesia",
    offers: 109,
    image: CityCard5,
  },
  {
    id: 2,
    title: "108766",
    location: "Bali",
    temperature: 22,
    duration: "3 nights - 2 Adults",
    country: "Indonesia",
    offers: 109,
    image: CityCard2,
  },
  {
    id: 3,
    title: "108767",
    location: "Bali",
    temperature: 22,
    duration: "3 nights - 2 Adults",
    country: "Indonesia",
    offers: 109,
    image: CityCard3,
  },
  {
    id: 4,
    title: "108768",
    location: "Bali",
    temperature: 22,
    duration: "3 nights - 2 Adults",
    country: "Indonesia",
    offers: 109,
    image: CityCard4,
  },
];

const staticHotelCities = [
  {
    hotelId: 1037285,
    title: "CHF 450.00",
    location: "Paris",
    temperature: 15,
    duration: "4 nights - 2 Adults",
    country: "France",
    offers: 50,
    image: HotelImage6,
    hotelName: "Royal Mansart",
    classification: "4 Star",
    roomType: "Standard Room",
    originalPrice: "CHF 550.00",
    discount: "20% Off",
  },
  {
    hotelId: 1047388,
    title: "CHF 600.00",
    location: "Dubai",
    temperature: 35,
    duration: "4 nights - 2 Adults",
    country: "UAE",
    offers: 60,
    image: HotelImage7,
    hotelName: "Atlantis, The Palm",
    classification: "5 Star",
    roomType: "Deluxe Room",
    originalPrice: "CHF 720.00",
    discount: "20% Off",
  },
  {
    hotelId: 90888,
    title: "CHF 300.00",
    location: "Kandy",
    temperature: 25,
    duration: "4 nights - 2 Adults",
    country: "Sri Lanka",
    offers: 40,
    image: HotelImage8,
    hotelName: "Earl's Regency",
    classification: "4 Star",
    roomType: "Standard Room",
    originalPrice: "CHF 360.00",
    discount: "20% Off",
  },
];

const HotelSection = ({
  selectedTab,
  cityName,
  dateRangeDisplay,
  nights,
  adultsCount,
  hotels,
  cityCode,
  startDate,
  endDate,
  searchCriteria,
  onRetry
}) => {
  const cityInfo = citiesConfig.find(city => city.cityCode === cityCode) || {};
  const formattedDestination = `${cityInfo.name}, ${cityInfo.country}`;

  return (
    <div className="space-y-8">
      {selectedTab === 2 && (
        <div className="flex flex-col">
          <div className="flex items-center gap-2">
            <div className="flex flex-col">
              <div className="flex items-start gap-2">
                <MapPin className="text-smokyGray w-6 h-6 flex-shrink-0 mt-1" strokeWidth={1} />
                <div className="flex flex-col">
                  <input
                    type="text"
                    value={cityName}
                    readOnly
                    className="border-b-2 font-inter cursor-pointer focus:outline-none"
                  />
                  <p className="text-xs font-light font-inter text-gray-600">From</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {hotels.slice(0, 4).map((cardCity) => (
          <CityCards
            key={cardCity.hotelId || cardCity.id}
            cardCity={{
              ...cardCity,
              cityCode: cityCode,
              searchCriteria: {
                ...searchCriteria,
                dateRange: {
                  startDate: new Date(startDate),
                  endDate: new Date(endDate)
                },
                adults: adultsCount,
                rooms: searchCriteria.rooms || 1,
                children: searchCriteria.children || 0
              }
            }}
          />
        ))}
      </div>
      <div className="flex justify-center">
        <ButtonCom
          variant="outline"
          size="md"
          rounded="full"
          className="transition-all hover:scale-105"
        >
          <Link
            to="/TrendingPackage"
            state={{
              formData: {
                ...searchCriteria,
                destination: formattedDestination,
                cityCode: cityCode,
                dateRange: {
                  startDate: new Date(startDate),
                  endDate: new Date(endDate),
                },
                adults: adultsCount,
                rooms: searchCriteria.rooms || 1,
                children: searchCriteria.children || 0,
              },
              selectedTab: 2,
            }}
            className="block w-full h-full text-darkBlue"
          >
            View more Offers
          </Link>
        </ButtonCom>
      </div>
    </div>
  );
};

const ErrorMessage = ({ error, onRetry }) => {
  return (
    <div className="w-full flex flex-col items-center justify-center py-20 space-y-4">
      <div className="text-center text-red-500">
        <p className="text-lg font-medium">Failed to load hotel data</p>
        <p className="text-sm mt-2">{error.message || "Please try again later"}</p>
      </div>
      <button
        onClick={onRetry}
        className="px-6 py-2 bg-darkBlue text-white rounded-full hover:bg-blue-700 transition-colors"
      >
        Try Again
      </button>
    </div>
  );
};

const CityHotelSection = ({ city, searchCriteria, baseRequestBody, nights, adultsCount, dateRangeDisplay }) => {
  const fetcher = async (url, options = {}) => {
    try {
      const res = await fetch(url, {
        method: options.method || "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "*/*",
          // Add API key if required: "Authorization": `Bearer ${process.env.REACT_APP_API_KEY}`,
        },
        body: options.body ? JSON.stringify(options.body) : undefined,
      });

      if (!res.ok) {
        throw new Error(`HTTP error ${res.status}: ${res.statusText}`);
      }

      return await res.json();
    } catch (error) {
      console.error("API call failed:", error);
      throw error;
    }
  };

  const useHotelData = (cityCode) => {
    const requestBody = {
      ...baseRequestBody,
      cityCode,
      hotelIds: null,
      isNearbyCities: false,
    };

    const isValidRequest = requestBody.fromDate && requestBody.toDate && requestBody.cityCode;

    const { data, error, isLoading, mutate } = useSWR(
      isValidRequest
        ? ["https://backend.graycorp.io:9603/efly/api/hotelBooking/getSavedAvailableHotelDetailsByHotelId", requestBody]
        : null,
      ([url, body]) => fetcher(url, { method: "POST", body }),
      {
        revalidateOnFocus: false,
        shouldRetryOnError: true,
        errorRetryCount: 3,
        errorRetryInterval: 1000,
      }
    );

    const handleRetry = () => {
      mutate();
    };

    const transformHotelData = (hotel, index) => ({
      hotelId: hotel.hotelId,
      title: `CHF ${((hotel.cheapestRoomCharge || 0) * nights).toFixed(2)}`,
      priceValue: (hotel.cheapestRoomCharge || 0) * nights,
      location: hotel.cityData?.cityName || city.name,
      temperature: Math.floor(Math.random() * 10) + (city.name === "Dubai" ? 30 : city.name === "Paris" ? 15 : 20),
      duration: `${nights} nights - ${adultsCount} Adults`,
      country: hotel.cityData?.countryData?.countryName || city.country,
      offers: Math.floor(Math.random() * 50) + 20,
      image: hotel.thumbUrl || hotelImages[index % hotelImages.length],
      hotelName: hotel.hotelName,
      classification: hotel.classificationName || `${Math.floor(hotel.classificationCode / 1000) || 3} Star`,
      roomType: hotel.cheapestRoomTypeName || "Standard Room",
      originalPrice: `CHF ${((hotel.cheapestRoomCharge || 0) * nights * 1.2).toFixed(2)}`,
      discount: "20% Off",
    });

    const hotels = useMemo(() => {
      if (data && Array.isArray(data)) {
        try {
          return data
            .filter((hotel) => (hotel.cheapestRoomCharge || 0) * nights < 1500)
            .map(transformHotelData)
            .sort((a, b) => a.priceValue - b.priceValue);
        } catch (e) {
          console.error("Error processing hotel data:", e);
          return staticHotelCities.filter(hotel => hotel.location === city.name);
        }
      }
      return staticHotelCities.filter(hotel => hotel.location === city.name);
    }, [data, nights, adultsCount, city.name]);

    return { hotels, error, isLoading, handleRetry };
  };

  const { hotels, error, isLoading, handleRetry } = useHotelData(city.cityCode);

  if (isLoading) {
    return (
      <div className="w-full flex justify-center py-20">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-lg">Loading hotels for {city.name}...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return <ErrorMessage error={error} onRetry={handleRetry} />;
  }

  return (
    <HotelSection
      key={city.id}
      selectedTab={2}
      cityName={city.name}
      dateRangeDisplay={dateRangeDisplay}
      nights={nights}
      adultsCount={adultsCount}
      hotels={hotels}
      cityCode={city.cityCode}
      startDate={baseRequestBody.fromDate}
      endDate={baseRequestBody.toDate}
      searchCriteria={searchCriteria}
    />
  );
};

const CardAndCity = ({ title }) => {
  const location = useLocation();
  const searchCriteria = location.state?.formData || {};
  const selectedTab = location.state?.selectedTab ?? 0;

  const startDate = "2025-09-01";
  const endDate = "2025-09-05";
  const formattedStart = formatDisplayDate(startDate);
  const formattedEnd = formatDisplayDate(endDate);
  const dateRangeDisplay = `${formattedStart} to ${formattedEnd}`;

  const nights = useMemo(() => {
    const from = new Date(startDate);
    const to = new Date(endDate);
    return Math.max(1, Math.floor((to - from) / (1000 * 60 * 60 * 24)));
  }, [startDate, endDate]);

  const adultsCount = searchCriteria.adults || 2;

  const baseRequestBody = {
    fromDate: startDate,
    toDate: endDate,
    currencyCode: "520",
    hotelIds: null,
    isNearbyCities: false,
    roomCount: searchCriteria.rooms || 1,
    roomRequests: [
      {
        adultsCount,
        childCount: searchCriteria.children || 0,
        passengerNationalityCode: 81,
        passengerCountryOfResidenceCode: 72,
      },
    ],
  };

  return (
    <div className="w-full flex justify-center px-4 mb-12">
      <div className="md:max-w-[80%] w-full space-y-8">
        <Link to={selectedTab === 2 ? "/TrendingPackage" : "/topcitypackage"}>
          <h2 className="text-3xl font-medium text-center text-gray-800">{title}</h2>
        </Link>

        {selectedTab === 2 ? (
          citiesConfig
            .filter((city) => ![14864, 1644, 20344, 4594].includes(city.cityCode))
            .map((city) => (
              <CityHotelSection
                key={city.id}
                city={city}
                searchCriteria={searchCriteria}
                baseRequestBody={baseRequestBody}
                nights={nights}
                adultsCount={adultsCount}
                dateRangeDisplay={dateRangeDisplay}
              />
            ))
        ) : (
          <HotelSection
            key="static-cards"
            selectedTab={selectedTab}
            cityName="Popular Destinations"
            dateRangeDisplay={dateRangeDisplay}
            nights={nights}
            adultsCount={adultsCount}
            hotels={staticCardCities}
            cityCode={null}
            startDate={baseRequestBody.fromDate}
            endDate={baseRequestBody.toDate}
            searchCriteria={searchCriteria}
          />
        )}
      </div>
    </div>
  );
};

export default CardAndCity;