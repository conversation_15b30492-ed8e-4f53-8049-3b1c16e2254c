import React, { useState, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { setPriceRange } from '../../../store/hotelSlice';
import { RxStarFilled } from 'react-icons/rx';
import Slider from 'rc-slider';
import { Tooltip } from 'react-tooltip';

const classificationMapping = {
  559: { code: 559, stars: 1, label: '1 Star' },
  560: { code: 560, stars: 2, label: '2 Stars' },
  561: { code: 561, stars: 3, label: '3 Stars' },
  562: { code: 562, stars: 4, label: '4 Stars' },
  563: { code: 563, stars: 5, label: '5 Stars' },
  48055: { code: 48055, stars: 0, label: 'Serviced Apartment' },
  55835: { code: 55835, stars: 0, label: 'Unrated' },
};

const FilterTag = ({ label, count, selected, onClick, disabled }) => (
  <label
    className={`flex items-center justify-between w-full px-2 py-1 rounded-[8px] transition-colors ${selected ? '' : ''
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${count === 0 ? 'hidden' : ''}`}
  >
    <div className="flex items-center space-x-3 w-full">
      <input
        type="checkbox"
        checked={selected}
        onChange={onClick}
        disabled={disabled || count === 0}
        className="h-4 w-4 text-darkBlue border-border rounded "
      />
      <span className="text-xs flex-1 font-light truncate">{label}</span>
      <span className="text-xs font-light">{count}</span>
    </div>
  </label>
);

const FilterSection = ({ title, isOpen, onToggle, children, isLastSection, itemCount, visibleCount, onViewAll }) => {
  const [showAll, setShowAll] = useState(false);

  const handleToggleView = () => {
    setShowAll((prev) => !prev);
    if (onViewAll) onViewAll(!showAll);
  };


  const needsViewAll = itemCount > visibleCount;

  return (
    <div className={`py-4 ${!isLastSection ? 'border-b border-borderGray' : ''}`}>
      <button onClick={onToggle} className="w-full flex justify-between items-center mb-2">
        <span className="text-smokyGray font-medium text-sm">{title}</span>
      </button>
      <div className="mt-3 space-y-2">
        {children}
        {needsViewAll && (
          <button
            onClick={handleToggleView}
            className="text-darkBlue text-sm font-medium hover:underline mt-2"
          >
            {showAll ? 'Hide' : 'View all'}
          </button>
        )}
      </div>
    </div>
  );
};

const TrendingFilter = ({
  selectedTags,
  onToggleTag,
  filterCounts,
  filterLoading,
  filterError,
}) => {
  const dispatch = useDispatch();
  const priceRange = useSelector((state) => state.hotels.priceRange);
  const [openSections, setOpenSections] = useState({
    price: true,
    starRatings: true,
    previousFilters: true,
    filters: true,
    amenitieFilters: false,
  });
  const [showAllItems, setShowAllItems] = useState({
    previousFilters: false,
    leisureFilters: false,
    amenitieFilters: false,
  });

  const toggleSection = (section) => {
    setOpenSections((prev) => ({ ...prev, [section]: !prev[section] }));
  };

  const allHotels = useSelector((state) => state.hotels.originalHotels);

  const [sliderMin, sliderMax] = useMemo(() => {
    const validPrices = allHotels
      .map((h) => h.cheapestRoomCharge)
      .filter((p) => typeof p === 'number' && !isNaN(p));

    if (validPrices.length === 0) return [1, 10000];

    const min = Math.min(...validPrices);
    const max = Math.max(...validPrices);
    return [Math.floor(min), Math.ceil(max)];
  }, [allHotels]);


  const filterGroups = useMemo(() => {
    if (!Array.isArray(filterCounts)) {
      return { previousFilters: [], leisureFilters: [], amenitieFilters: [], starRatings: [] };
    }

    const business = [];
    const leisure = [];
    const amenities = [];
    const classification = [];

    filterCounts.forEach(({ filter, filterCount }) => {
      if (!filter) return;

      if (filter.businessName) {
        business.push({ label: filter.businessName, count: filterCount, id: filter.businessId, type: 'business' });
      } else if (filter.leisureName) {
        leisure.push({ label: filter.leisureName, count: filterCount, id: filter.leisureId, type: 'leisure' });
      } else if (filter.amenitieItem) {
        amenities.push({ label: filter.amenitieItem, count: filterCount, id: filter.amenitieId, type: 'amenities' });
      } else if (filter.classificationId in classificationMapping) {
        const { label, stars, code } = classificationMapping[filter.classificationId];
        if (stars >= 0) {
          classification.push({
            label,
            count: filterCount,
            stars,
            id: filter.classificationId,
            code,
            type: 'classification',
          });
        }
      }
    });

    classification.sort((a, b) => b.stars - a.stars);

    return {
      previousFilters: [...business, ...leisure, ...amenities].slice(0, 5),
      leisureFilters: [...business, ...leisure, ...amenities].slice(5, 13),
      amenitieFilters: amenities,
      starRatings: classification,
    };
  }, [filterCounts]);

  const handleTagClick = (option) => {
    onToggleTag(option.label, option.code || option.id, option.type);
  };

  const renderFilterTags = (filters, category) => {
    const visible = showAllItems[category]
      ? filters
      : filters.slice(0, category === 'leisureFilters' ? 8 : 5);
    return (
      <div className="flex flex-wrap gap-2">
        {visible.map((opt) => (
          <FilterTag
            key={opt.id}
            label={opt.label}
            count={opt.count}
            selected={selectedTags.some((t) => t.label === opt.label)}
            onClick={() => handleTagClick(opt)}
          />
        ))}
      </div>
    );
  };

  const handleViewAll = (category, showAll = true) => {
    setShowAllItems((prev) => ({ ...prev, [category]: showAll }));
  };

  if (filterLoading) return <div className="text-center text-darkBlue">Loading filters...</div>;
  if (filterError) return <div className="text-center text-red">Error loading filters: {filterError}</div>;

  return (
    <div className="w-full rounded-[8px] space-y-4 flex ">
      <div className="md:w-[300px] w-full max-w-[320px] border border-border rounded-[8px] px-4 shadow-md ">
        {/* Price Range */}
        <FilterSection
          title="Price Range Per Person"
          isOpen={openSections.price}
          onToggle={() => toggleSection('price')}
          isLastSection={false}
          itemCount={1}
          visibleCount={1}
        >
          <div className="px-4 pb-6">
            {/* Min and Max text above the slider */}
            <div className="flex justify-between mb-1 ">
              <div className="text-xs font-light -ml-[6px] ">min</div>
              <div className="text-xs font-light -mr-[6px]">max</div>
            </div>

            {/* Slider with CHF labels */}
            <Slider
              range
              marks={{
                [sliderMin]: (
                  <div className="text-xs leading-tight mt-1 ml-8">
                    <span className="font-light ">CHF </span>
                    <span className="font-medium text-darkBlue">{sliderMin}</span>
                  </div>
                ),
                [sliderMax]: (
                  <div className="text-xs leading-tight text-right mt-1 mr-11">
                    <span className="font-light">CHF </span>
                    <span className="font-medium text-darkBlue">{sliderMax}</span>
                  </div>
                ),
              }}
              min={sliderMin}
              max={sliderMax}
              value={priceRange}
              onChange={(newValues) => dispatch(setPriceRange(newValues))}
              handleRender={({ props }) => {
                const value = props['aria-valuenow'];
                return (
                  <>
                    <div {...props} data-tooltip-id="slider-tooltip" data-tooltip-content={`CHF${value}`} />
                    <Tooltip id="slider-tooltip" variant="dark" place="top" />
                  </>
                );
              }}
              className="custom-slider mb-3"
            />
          </div>
        </FilterSection>

        <FilterSection
          title="Star Ratings"
          isOpen={openSections.starRatings}
          onToggle={() => toggleSection('starRatings')}
          isLastSection={false}
          itemCount={filterGroups.starRatings.length}
          visibleCount={filterGroups.starRatings.length}
        >
          {filterGroups.starRatings.map((rating) =>
            rating.count === 0 ? null : ( // Hides if count is 0
              <div key={rating.id} className="flex items-center justify-between p-1 rounded-[8px]">
                <label className="flex items-center space-x-3 cursor-pointer w-full">
                  <input
                    type="checkbox"
                    name="rating"
                    className="form-checkbox h-4 w-4 text-darkBlue"
                    checked={selectedTags.some((tag) => tag.label === rating.label)}
                    onChange={() => handleTagClick(rating)}
                    disabled={rating.count === 0}
                  />
                  <div className="flex items-center justify-between w-full">
                    <div className="flex items-center space-x-1">
                      {rating.stars > 0 ? (
                        Array(rating.stars).fill(0).map((_, i) => (
                          <RxStarFilled key={i} className="w-5 h-5 text-darkBlue" />
                        ))
                      ) : (
                        <span className="text-xs flex-1 font-light truncate">{rating.label}</span>
                      )}
                    </div>
                    <span className="font-light text-xs">{rating.count}</span>
                  </div>
                </label>
              </div>
            )
          )}
        </FilterSection>

        <FilterSection
          title="Business Filters"
          isOpen={openSections.previousFilters}
          onToggle={() => toggleSection('previousFilters')}
          isLastSection={false}
          itemCount={filterGroups.previousFilters.length}
          visibleCount={5}
          onViewAll={(showAll) => handleViewAll('previousFilters', showAll)}
        >
          {renderFilterTags(filterGroups.previousFilters, 'previousFilters')}
        </FilterSection>

        <FilterSection
          title="Leisure Filters"
          isOpen={openSections.leisureFilters}
          onToggle={() => toggleSection('leisureFilters')}
          isLastSection={false}
          itemCount={filterGroups.leisureFilters.length}
          visibleCount={8}
          onViewAll={(showAll) => handleViewAll('leisureFilters', showAll)}
        >
          {renderFilterTags(filterGroups.leisureFilters, 'leisureFilters')}
        </FilterSection>

        <FilterSection
          title="Amenitie Filters"
          isOpen={openSections.amenitieFilters}
          onToggle={() => toggleSection('amenitieFilters')}
          isLastSection={true}
          itemCount={filterGroups.amenitieFilters.length}
          visibleCount={6}
          onViewAll={(showAll) => handleViewAll('amenitieFilters', showAll)}

        >
          {renderFilterTags(filterGroups.amenitieFilters, 'amenitieFilters')}
        </FilterSection>
      </div>
    </div>
  );
};

export default TrendingFilter;