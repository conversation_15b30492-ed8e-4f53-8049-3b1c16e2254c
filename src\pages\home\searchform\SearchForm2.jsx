import React, { useEffect, useRef, useState } from "react";
import { Formik } from "formik";
import { useNavigate, useLocation } from "react-router-dom";
import * as Yup from "yup";
import FlightHotelForm from "./FlightHotelForm";
import FlightForm from "./FlightForm";
import HotelForm from "./HotelForm";
import TourForm from "./TourForm";
import { MdAirplanemodeActive } from "react-icons/md";
import { BedDouble, Navigation } from "lucide-react";
import backgroundImage from "../../../assets/heroSection/hero-img.png";

const ValidationSchema = Yup.object().shape({
  destination: Yup.string().required("Destination is required"),
  cityCode: Yup.string().required("Select valid destination"),
  rooms: Yup.number().min(1, "At least 1 room required").required("Required"),
  persons: Yup.number().min(1, "At least 1 person required").required("Required"),
});

const SearchForm2 = ({ initialData = {}, onTabChange }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const calendarRef = useRef(null);
  const formContainerRef = useRef(null);

  const isHomePage = location.pathname === "/";
  const initialTab = location.state?.selectedTab || 0;
  const initialFormData = location.state?.formData || {
    ...initialData,
    departure: initialData?.dep_apt,
    destination: initialData?.des_apt,
    checkInDate: initialData?.checkInDate,
    checkOutDate: initialData?.checkOutDate,
  };

  const [searchStatusVal, setSearchStatusVal] = useState(initialTab);

  // Utility to format dates as YYYY-MM-DD
  const formatDate = (date) => {
    const d = new Date(date);
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}-${String(d.getDate()).padStart(2, "0")}`;
  };

  // Default dates
  const defaultCheckInDate = formatDate(new Date(Date.now() + 3 * 24 * 60 * 60 * 1000));
  const defaultCheckOutDate = formatDate(new Date(Date.now() + 8 * 24 * 60 * 60 * 1000));

  // Initial values per form
  const initialValuesMap = {
    0: {
      // FlightHotelForm
      destination: initialFormData.destination || initialFormData.des_apt || "",
      cityCode: initialFormData.cityCode || null,
      departure: initialFormData.departure || initialFormData.dep_apt || "",
      rooms: initialFormData.rooms || 1,
      adults: initialFormData.adults || 2,
      children: initialFormData.children || 0,
      persons: (initialFormData.adults || 2) + (initialFormData.children || 0),
      checkInDate: initialFormData.checkInDate || defaultCheckInDate,
      checkOutDate: initialFormData.checkOutDate || defaultCheckOutDate,
    },
    1: {
      // FlightForm
      travelClass: "Economy",
      baggage: "Carry-on Baggage",
      tripType: initialFormData.tripType || "Return",
      adults: initialFormData.adults || 1,
      children: initialFormData.children || 0,
      babies: initialFormData.babies || 0,
      returnDate: initialFormData.endDate || "",
      departureDate: initialFormData.startDate || "",
      flights: [
        {
          departure: initialFormData.departure || initialFormData.dep_apt || "",
          destination: initialFormData.destination || initialFormData.des_apt || "",
          flyfrom: "",
          flexibleDays: 0,
          departureDate: initialFormData.startDate || "",
          returnDate: initialFormData.endDate || "",
        },
        {
          destination: initialFormData.destination || initialFormData.des_apt || "",
          flyTo: "",
          flexibleDays: 0,
          destinationDate: "",
        },
      ],
    },
    2: {
      // HotelForm
      destination: initialFormData.destination || initialFormData.des_apt || "",
      cityCode: initialFormData.cityCode || null,
      rooms: initialFormData.rooms || 1,
      adults: initialFormData.adults || 2,
      children: initialFormData.children || 0,
      persons: (initialFormData.adults || 2) + (initialFormData.children || 0),
      checkInDate: initialFormData.checkInDate || defaultCheckInDate,
      checkOutDate: initialFormData.checkOutDate || defaultCheckOutDate,
    },
    3: {
      // TourForm
      destination: initialFormData.destination || initialFormData.des_apt || "",
      cityCode: initialFormData.cityCode || null,
      rooms: initialFormData.rooms || 1,
      adults: initialFormData.adults || 2,
      children: initialFormData.children || 0,
      persons: (initialFormData.adults || 2) + (initialFormData.children || 0),
      checkInDate: initialFormData.checkInDate || defaultCheckInDate,
      checkOutDate: initialFormData.checkOutDate || defaultCheckOutDate,
    },
  };

  const handleTabClick = (index) => {
    setSearchStatusVal(index);
    onTabChange(index);
    const routes = ["/", "/", "/", "/tour"];
    navigate(routes[index] || "/", {
      state: { selectedTab: index, formData: initialFormData },
    });
  };

  const searchTabs = [
    {
      component: (
        <div className="text-sm md:text-base">
          {/* Mobile Layout */}
          <div className="flex flex-col items-center space-y-1 md:hidden">
            <div className="flex flex-row items-center justify-center space-x-4">
              <MdAirplanemodeActive className="h-5 w-5 rotate-90" />
              <span className="">+</span>
              <BedDouble className="h-5 w-5" />
            </div>
            <div className="flex flex-row items-center justify-center space-x-8 ">
              <span>Flight</span>
              <span>Hotel</span>
            </div>
          </div>
          {/* Desktop Layout */}
          <div className="hidden md:flex items-center space-x-2">
            <MdAirplanemodeActive className="h-5 w-5 rotate-90" />
            <span>Flight</span>
            <span>+</span>
            <BedDouble className="h-5 w-5" />
            <span>Hotel</span>
          </div>
        </div>
      ),
    },
    {
      component: (
        <div className="flex flex-col md:flex-row items-center text-sm md:text-base space-y-1 md:space-y-0 md:space-x-1 lg:space-x-2">
          <MdAirplanemodeActive className="h-5 w-5 rotate-90" />
          <span>Flight</span>
        </div>
      ),
    },
    {
      component: (
        <div className="flex flex-col md:flex-row items-center text-sm md:text-base space-y-1 md:space-y-0 md:space-x-1 lg:space-x-2">
          <BedDouble className="h-5 w-5" />
          <span>Hotel</span>
        </div>
      ),
    },
    {
      component: (
        <div className="flex flex-col md:flex-row items-center text-sm md:text-base space-y-1 md:space-y-0 md:space-x-1 lg:space-x-2">
          <Navigation className="h-5 w-5" />
          <span>Tour</span>
        </div>
      ),
    },
  ];

  const formComponents = [FlightHotelForm, FlightForm, HotelForm, TourForm];
  const FormComponent = formComponents[searchStatusVal] || FlightForm;

  return (
    <div ref={formContainerRef} className=" w-full z-[60] flex  justify-center">
      <div
        className={`relative w-full md:max-w-[80%] rounded-[8px] border border-border drop-shadow-md md:px-6 md:pb-6 space-y-4 ${isHomePage ? "bg-opacity-80" : "bg-opacity-100"
          }`}
      >
        <div
          className={`absolute inset-0 -z-10 rounded-[8px] bg-darkBlue pointer-events-none ${isHomePage ? "bg-opacity-80" : "bg-opacity-100"
            }`}
        ></div>
        <div className="flex flex-row w-full items-center relative overflow-x-auto md:overflow-hidden md:space-x-14 space-x-8 px-4 pb-2">
          {searchTabs.map((item, index) => (
            <React.Fragment key={index}>
              <div
                onClick={() => handleTabClick(index)}
                className={`flex cursor-pointer transition-all ${searchStatusVal === index ? "text-orange py-4" : "hover:text-primaryColor text-white"
                  }`}
              >
                {item.component}
              </div>
            </React.Fragment>
          ))}
        </div>
        <Formik
          initialValues={initialValuesMap[searchStatusVal]}
          validationSchema={searchStatusVal === 1 ? null : ValidationSchema}
          enableReinitialize={true}
        >
          {({ values, setFieldValue, errors, touched }) => (
            <FormComponent
              dateRange={{
                startDate: values.checkInDate ? new Date(values.checkInDate) : new Date(defaultCheckInDate),
                endDate: values.checkOutDate ? new Date(values.checkOutDate) : new Date(defaultCheckOutDate),
                key: "selection",
              }}
              setDateRange={(range) => {
                setFieldValue("checkInDate", formatDate(range.startDate));
                setFieldValue("checkOutDate", formatDate(range.endDate));
              }}
              values={values}
              errors={errors}
              touched={touched}
              setFieldValue={setFieldValue}
              navigate={navigate}
              selectedTab={searchStatusVal}
            />
          )}
        </Formik>
      </div>
    </div>
  );
};

export default SearchForm2;