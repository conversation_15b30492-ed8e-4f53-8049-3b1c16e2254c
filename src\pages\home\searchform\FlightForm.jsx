import React, { useRef, useState, useEffect } from "react";
import { Formik, Form, Field, FieldArray } from "formik";
import { useNavigate } from "react-router-dom";
import { Search, BriefcaseBusiness, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, Minus, Plus, Check, Loader2 } from "lucide-react";
import { HiOutlineLocationMarker } from "react-icons/hi";
import { HiOutlineCalendar } from "react-icons/hi";
import { CiUser } from "react-icons/ci";
import { PiSeatThin } from "react-icons/pi";
import { DepartureCityAutocomplete, DestinationCityAutocomplete } from "./CityAutocomplete";
import "react-date-range/dist/styles.css";
import "react-date-range/dist/theme/default.css";
import PassengersAttachment from "./PassengersAttachment";
import TravelClassAttachment from "./TravelClassAttachment";
import CalenderComponent from "./CalenderComponent";
import airports from "../../../data/airports.json";
import BaggagesAttachment from "./BaggagesAttachment";
import ButtonCom from "../../../components/ui/button/ButtonCom";

// Get today's date at midnight for consistent comparison
const today = new Date();
today.setHours(0, 0, 0, 0);

const FlightForm = ({ initialFlightData }) => {
  const flyToRef = useRef(null);
  const flyFromRef = useRef(null);
  const departureSectionRef = useRef(null);
  const navigate = useNavigate();
  const [tripType, setTripType] = useState(initialFlightData?.tripType || "Return");
  const [showPassengersModal, setShowPassengersModal] = useState(false);
  const [showTravelClassModal, setShowTravelClassModal] = useState(false);
  const [showBaggagesModal, setShowBaggagesModal] = useState(false);
  const [showDepartureCalendar, setShowDepartureCalendar] = useState(null);
  const [showReturnCalendar, setShowReturnCalendar] = useState(false);
  const [activeSection, setActiveSection] = useState(null);
  const [promoActive, setPromoActive] = useState(false);
  const [promoCode, setPromoCode] = useState('');
  const [isValidating, setIsValidating] = useState(false);
  const [isValid, setIsValid] = useState(false);
  const [appliedPromoCode, setAppliedPromoCode] = useState('');

  // Auto-focus Fly From on mount (when Flight tab is selected)
  useEffect(() => {
    setActiveSection("flyFrom");
    if (flyFromRef.current) {
      const input = flyFromRef.current.querySelector('input');
      if (input) input.focus();
    }
  }, []);

  // Simulate validation process
  useEffect(() => {
    if (promoCode) {
      setIsValidating(true);
      setIsValid(false);

      const timer = setTimeout(() => {
        const isValidPromo = promoCode === "MD3782";
        setIsValidating(false);
        setIsValid(isValidPromo);

        if (isValidPromo) {
          setTimeout(() => {
            setAppliedPromoCode(promoCode);
            setPromoActive(false);
            setPromoCode('');
          }, 2000);
        }
      }, 1000);

      return () => clearTimeout(timer);
    } else {
      setIsValidating(false);
      setIsValid(false);
    }
  }, [promoCode]);

  const getSectionBorderStyle = (sectionName) => {
    return activeSection === sectionName
      ? 'border border-gray/35'
      : 'border border-transparent';
  };

  console.log("initialFlightData:", initialFlightData);
  const initialValues = {
    travelClass: "Economy",
    baggage: "Carry-on Baggage",
    tripType: tripType,
    adults: initialFlightData?.adults || 1,
    children: initialFlightData?.children || 0,
    babies: initialFlightData?.babies || 0,
    returnDate: initialFlightData?.endDate || "",
    departureDate: initialFlightData?.startDate || "",
    flights: [
      {
        departure: initialFlightData?.departure || "",
        destination: initialFlightData?.destination || "",
        flyfrom: "",
        flexibleDays: 0,
        departureDate: initialFlightData?.startDate || "",
        returnDate: initialFlightData?.endDate || "",
      },
      {
        destination: initialFlightData?.destination || "",
        flyTo: "",
        flexibleDays: 0,
        destinationDate: "",
      },
    ],
  };
  // const onSubmit = (values) => {
  //   const { flights, tripType } = values;
  //   const dep_apt = flights[0].departure;
  //   const des_apt = flights[0].destination;
  //   const formattedDepartureDate = flights[0].departureDate
  //     ? new Date(flights[0].departureDate).toISOString().split("T")[0]
  //     : null;
  //   const formattedReturnDate = tripType === "Return" && flights[0].returnDate
  //     ? new Date(flights[0].returnDate).toISOString().split("T")[0]
  //     : null;

  //   if (!formattedDepartureDate || !dep_apt || !des_apt) {
  //     console.error("Missing required search parameters");
  //     return;
  //   }

  //   navigate("/available-flights", {
  //     state: {
  //       dep_date: formattedDepartureDate,
  //       des_date: formattedReturnDate,
  //       dep_apt,
  //       des_apt,
  //       selectedTab: 1,
  //       formData: {
  //         departure: dep_apt,
  //         destination: des_apt,
  //         startDate: formattedDepartureDate,
  //         endDate: formattedReturnDate,
  //       },
  //     },
  //   });
  // };

  const onSubmit = (values) => {
    console.log("Form values on submit:", values);
    const { flights, tripType, adults, children, babies } = values;
    const depIata = flights[0].departure;
    const desIata = flights[0].destination;

    const depAirport = airports.find((a) => a.iata === depIata);
    const desAirport = airports.find((a) => a.iata === desIata);

    const depFull = depAirport
      ? `${depAirport.city} (${depAirport.iata})`
      : depIata;
    const desFull = desAirport
      ? `${desAirport.city} (${desAirport.iata})`
      : desIata;

    //   const depFull = depAirport
    //   ? `${depAirport.city}, ${depAirport.country} (${depAirport.iata} ${depAirport.name})`
    //   : depIata;
    // const desFull = desAirport
    //   ? `${desAirport.city}, ${desAirport.country} (${desAirport.iata} ${desAirport.name})`
    //   : desIata;

    const formattedDepartureDate = flights[0].departureDate
      ? new Date(flights[0].departureDate).toISOString().split("T")[0]
      : null;
    const formattedReturnDate =
      tripType === "Return" && flights[0].returnDate
        ? new Date(flights[0].returnDate).toISOString().split("T")[0]
        : null;

    if (!formattedDepartureDate || !depIata || !desIata) {
      console.error("Missing required search parameters");
      console.log("formattedDepartureDate:", formattedDepartureDate);
      console.log("depIata:", depIata);
      console.log("desIata:", desIata);
    }
    const passengerDetails = `Adt:${adults},Chd:${children},Inf:${babies}`;

    navigate("/available-flights", {
      state: {
        dep_date: formattedDepartureDate,
        des_date: formattedReturnDate,
        dep_apt: depIata,
        des_apt: desIata,
        dep_apt_full: depFull,
        des_apt_full: desFull,
        passenger_type: `Adt:${adults},Chd:${children},Inf:${babies}`,
        selectedTab: 1,
        formData: {
          departure: depIata,
          destination: desIata,
          startDate: formattedDepartureDate,
          endDate: formattedReturnDate,
          adults,
          children,
          babies,
        },
      },
    });
  };

  return (
    <Formik
      initialValues={initialValues}
      onSubmit={onSubmit}
      enableReinitialize={true}
    >
      {({ values, setFieldValue, errors, touched, resetForm }) => {
        const persons = values.adults + values.children + values.babies;
        return (
          <Form className="flex flex-col space-y-4 p-2 md:px-0">
            {/* Trip Type */}
            <div className="flex flex-row justify-start items-center text-xs md:text-sm space-x-2 md:space-x-4 md:-mt-6">
              {["Return", "One Way", "Multi City"].map((type) => (
                <label
                  className="flex items-center text-white font-light"
                // key={type}
                // className={`flex items-center px-3 md:px-6 py-1 border-[0.3px] font-light border-darkBlue rounded-[5px] ${tripType === type
                //     ? 'bg-darkBlue text-white'
                //     : 'bg-transparent text-darkBlue'
                //   } cursor-pointer text-nowrap`}
                // onClick={() => {
                //   setTripType(type);
                //   setFieldValue("tripType", type);
                //   resetForm();
                // }}
                >
                  <input
                    type="radio"
                    name="tripType"
                    value={type}
                    checked={tripType === type}
                    onChange={() => {
                      setTripType(type);
                      setFieldValue("tripType", type);
                      resetForm();
                    }}
                    className="mr-2 accent-darkBlue w-4 h-4 cursor-pointer"
                  />
                  {type}
                </label>
              ))}
            </div>

            <div className="flex flex-col space-y-4 px-2 md:px-0 -mt-4">
              {/* dynamic flight inputs */}
              {tripType === "Multi City" ? (
                <FieldArray
                  name="flights"
                  render={(arrayHelpers) => (
                    <React.Fragment>
                      {values.flights && values.flights.length > 0 && (
                        <React.Fragment>
                          {values.flights.map((flight, index) => (
                            <div key={index}>
                              <div
                                className="flex flex-col space-y-2 md:flex-row md:h-14 w-full md:border md:border-border md:rounded-[8px] md:bg-white justify-between md:space-y-0 mb-2 md:mb-0"
                              >
                                {/* Fly From */}
                                <div
                                  className="flex flex-col relative w-full"
                                  ref={flyFromRef}
                                  onClick={() => setActiveSection('flyFrom')}
                                >
                                  <div className={`flex justify-between items-center w-full bg-white h-14 rounded-[8px]  ${getSectionBorderStyle('flyFrom')}`}>
                                    <div className="absolute top-2 bottom-2 right-0 w-[0.5px] bg-border md:block hidden"></div>
                                    <div className="flex flex-row items-center space-x-4 ml-3">
                                      <HiOutlineLocationMarker className="text-smokyGray w-7 h-7" strokeWidth={1} />
                                      <div className="flex flex-col items-start">
                                        <span className="text-smokyGray text-xs font-light">
                                          Fly From
                                        </span>
                                        <Field
                                          name={`flights[0].departure`}
                                          component={DepartureCityAutocomplete}
                                          className="text-sm font-light text-black focus:outline-none w-full"
                                          onSelectFlyTo={() => {
                                            if (flyToRef.current) {
                                              const input = flyToRef.current.querySelector('input');
                                              if (input) {
                                                input.focus();
                                                setActiveSection('flyTo');
                                              }
                                            }
                                          }}
                                          autoComplete="off"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Fly To */}
                                <div
                                  className="flex flex-col relative w-full"
                                  ref={flyToRef}
                                  onClick={() => setActiveSection('flyTo')}
                                >
                                  <div className={`flex justify-between items-center w-full h-14 bg-white rounded-[8px]  ${getSectionBorderStyle('flyTo-' + index)}`}>
                                    <div className="absolute top-2 bottom-2 right-0 w-[0.5px] bg-border md:block hidden"></div>
                                    <div className="flex flex-row items-center space-x-4 ml-3">
                                      <HiOutlineLocationMarker className="text-smokyGray w-7 h-7" strokeWidth={1} />
                                      <div className="flex flex-col items-start">
                                        <span className="text-smokyGray text-xs font-light">
                                          Fly To
                                        </span>
                                        <Field
                                          name={`flights[0].destination`}
                                          component={DestinationCityAutocomplete}
                                          className="text-sm font-light text-black focus:outline-none w-full"
                                          onSelect={() => {
                                            setActiveSection(`departure-0`);
                                            if (departureSectionRef.current) {
                                              departureSectionRef.current.focus();
                                            }
                                          }}
                                          autoComplete="off"
                                        />
                                      </div>
                                    </div>
                                  </div>
                                </div>

                                {/* Departure */}
                                <div
                                  className="flex flex-col relative w-full"
                                  ref={departureSectionRef}
                                  tabIndex={0}
                                  onClick={() => setActiveSection(`departure-${index}`)}
                                >
                                  <div
                                    className={`flex justify-between items-center w-full h-14 bg-white rounded-[8px] ${getSectionBorderStyle(`departure-${index}`)}`}
                                    onClick={() => setShowDepartureCalendar(showDepartureCalendar === index ? null : index)}
                                  >
                                    <div className="flex flex-row items-center space-x-4 ml-3">
                                      <HiOutlineCalendar className="text-smokyGray w-7 h-7" strokeWidth={1} />
                                      <div className="flex flex-col items-start">
                                        <span className="text-smokyGray text-xs font-light">Departing</span>
                                        <div className="text-nowrap text-sm font-light text-darkGray">
                                          {values.flights[index].departureDate
                                            ? values.flights[index].departureDate.slice(0, 10)
                                            : "-"}
                                        </div>
                                      </div>
                                    </div>
                                    <div className="flex flex-row space-x-2 items-center h-full mr-2">
                                      <button
                                        type="button"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          if (values.flights[index].departureDate) {
                                            const prevDate = new Date(values.flights[index].departureDate);
                                            prevDate.setDate(prevDate.getDate() - 1);
                                            if (prevDate >= today) {
                                              setFieldValue(`flights[${index}].departureDate`, prevDate.toISOString().split("T")[0]);
                                            }
                                          }
                                        }}
                                        className="flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px]"
                                      >
                                        <ChevronLeft className="w-4 h-4" />
                                      </button>
                                      <button
                                        type="button"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          const currentDate = values.flights[index].departureDate || new Date().toISOString().split("T")[0];
                                          const nextDate = new Date(currentDate);
                                          nextDate.setDate(nextDate.getDate() + 1);
                                          setFieldValue(`flights[${index}].departureDate`, nextDate.toISOString().split("T")[0]);
                                        }}
                                        className="flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px]"
                                      >
                                        <ChevronRight className="w-4 h-4" />
                                      </button>
                                    </div>
                                  </div>
                                  {showDepartureCalendar === index && (
                                    <CalenderComponent
                                      values={values}
                                      setFieldValue={setFieldValue}
                                      calClose={() => setShowDepartureCalendar(null)}
                                      tripType={tripType}
                                      dateType="departure"
                                      flightIndex={index}
                                      onAfterConfirm={() => {
                                        if (index < values.flights.length - 1) {
                                          const nextFlightInput = document.querySelector(`input[name="flights[${index + 1}].departure"]`);
                                          if (nextFlightInput) nextFlightInput.focus();
                                        }
                                      }}
                                    />
                                  )}
                                </div>
                              </div>
                              {/* Remove Button */}
                              <div className="flex justify-center w-full md:w-auto">
                                {index >= 2 && (
                                  <button
                                    type="button"
                                    onClick={() => arrayHelpers.remove(index)}
                                    className="text-red text-xs py-2 md:py-0"
                                  >
                                    Remove
                                  </button>
                                )}
                              </div>
                            </div>
                          ))}
                        </React.Fragment>
                      )}

                      {/* Add New Flight Button */}
                      <div className="flex flex-col items-center mt-4 w-full">
                        <button
                          type="button"
                          className="flex flex-row items-center space-x-4"
                          onClick={() =>
                            arrayHelpers.push({
                              departure: "",
                              destination: "",
                              passengers: 1,
                            })
                          }
                        >
                          <span className="flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px]">
                            <Plus className="w-3 h-3" />
                          </span>
                          <span className="text-darkGray font-medium">
                            Add Flight
                          </span>
                        </button>
                      </div>
                    </React.Fragment>
                  )}
                />
              ) : (
                <div>
                  {values.flights && values.flights.length > 0 && (
                    <div>
                      <div
                        key={0}
                        className="flex flex-col md:flex-row h-14 w-full border border-border rounded-[8px] bg-white justify-between space-y-2 md:space-y-0 mb-[185px] md:mb-0"
                      >
                        {/* Fly From */}
                        <div
                          className="flex flex-col relative w-full"
                          ref={flyFromRef}
                          onClick={() => setActiveSection('flyFrom')}
                        >
                          <div className={`flex justify-between items-center w-full h-14 rounded-[8px] ${getSectionBorderStyle('flyFrom')}`}>
                            <div className="absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                            <div className="flex flex-row items-center space-x-4 ml-3">
                              <HiOutlineLocationMarker className="text-smokyGray w-7 h-7" strokeWidth={1} />
                              <div className="flex flex-col items-start">
                                <span className="text-smokyGray text-xs font-light">
                                  Fly From
                                </span>
                                <Field
                                  name={`flights[0].departure`}
                                  component={DepartureCityAutocomplete}
                                  className="text-sm font-light text-black focus:outline-none"
                                  onSelectFlyTo={() => {
                                    if (flyToRef.current) {
                                      const input = flyToRef.current.querySelector('input');
                                      if (input) {
                                        input.focus();
                                        setActiveSection('flyTo');
                                      }
                                    }
                                  }}
                                  autoComplete="off"
                                />
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Fly To */}
                        <div
                          className="flex flex-col relative w-full"
                          ref={flyToRef}
                          onClick={() => setActiveSection('flyTo')}
                        >
                          <div className={`flex justify-between items-center bg-white w-full h-14 rounded-[8px] ${getSectionBorderStyle('flyTo')}`}>
                            <div className="absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                            <div className="flex flex-row items-center space-x-4 ml-3">
                              <HiOutlineLocationMarker className="text-smokyGray w-7 h-7" strokeWidth={1} />
                              <div className="flex flex-col items-start">
                                <span className="text-smokyGray text-xs font-light">
                                  Fly To
                                </span>
                                <Field
                                  name={`flights[0].destination`}
                                  component={DestinationCityAutocomplete}
                                  className="text-sm font-light text-black focus:outline-none"
                                  onSelect={() => {
                                    setActiveSection('departure');
                                    setShowDepartureCalendar(true);
                                    setTimeout(() => {
                                      if (departureSectionRef.current) {
                                        departureSectionRef.current.focus();
                                      }
                                    }, 0);
                                  }}
                                  autoComplete="off"
                                />
                              </div>
                            </div>
                          </div>
                        </div>

                        <div
                          className="flex flex-col relative w-full"
                          ref={departureSectionRef}
                          tabIndex={0}
                          onClick={() => setActiveSection('departure')}
                        >
                          {/* Departure */}
                          <div
                            className={`flex justify-between items-center bg-white w-full h-14 rounded-[8px] ${getSectionBorderStyle('departure')}`}
                            onClick={() => setShowDepartureCalendar(!showDepartureCalendar)}
                          >
                            <div className="flex flex-row items-center space-x-4 ml-3">
                              <HiOutlineCalendar className="text-smokyGray w-7 h-7" strokeWidth={1} />
                              <div className="flex flex-col items-start">
                                <span className="text-smokyGray text-xs font-light">
                                  Departing
                                </span>
                                <div className="text-nowrap text-sm font-light text-darkGray">
                                  {values.flights?.[0]?.departureDate
                                    ? values.flights[0].departureDate.slice(0, 10)
                                    : "-"}
                                </div>
                              </div>
                            </div>

                            <div className="flex flex-row space-x-2 items-center h-full mr-2">
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (values.flights?.[0]?.departureDate) {
                                    const prevDate = new Date(values.flights[0].departureDate);
                                    prevDate.setDate(prevDate.getDate() - 1);
                                    setFieldValue("flights[0].departureDate", prevDate.toISOString().split("T")[0]);
                                  }
                                }}
                                className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${tripType !== "Return" ? "bg-opacity-10 cursor-not-allowed" : ""
                                  }`}
                              >
                                <ChevronLeft className="w-4 h-4" />
                              </button>

                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  const currentDepartureDate =
                                    values.flights?.[0]?.departureDate || new Date().toISOString().split("T")[0];
                                  const nextDateObj = new Date(currentDepartureDate);
                                  nextDateObj.setDate(nextDateObj.getDate() + 1);
                                  const newDepartureDate = nextDateObj.toISOString().split("T")[0];
                                  setFieldValue("flights[0].departureDate", newDepartureDate);

                                  if (tripType === "Return" && values.returnDate && new Date(values.returnDate) <= nextDateObj) {
                                    const newReturnObj = new Date(nextDateObj);
                                    newReturnObj.setDate(newReturnObj.getDate());
                                    setFieldValue("returnDate", newReturnObj.toISOString().split("T")[0]);
                                  }
                                  if (values.flights?.[0]?.departureDate) {
                                    const newDeparture = new Date(values.flights[0].departureDate);
                                    newDeparture.setDate(newDeparture.getDate() + 1);

                                    setFieldValue("flights[0].departureDate", newDeparture.toISOString().split("T")[0]);

                                    if (tripType === "Return") {
                                      const currentReturn = new Date(values.flights[0].returnDate || newDeparture);
                                      if (currentReturn <= newDeparture) {
                                        const newReturn = new Date(newDeparture);
                                        newReturn.setDate(newDeparture.getDate() + 1);
                                        setFieldValue("flights[0].returnDate", newReturn.toISOString().split("T")[0]);
                                      }
                                    }
                                  }
                                }}
                                className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${tripType !== "Return" ? "bg-opacity-30 cursor-not-allowed" : ""
                                  }`}
                              >
                                <ChevronRight className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                          {errors.departureDate && touched.departureDate && (
                            <span className="text-red text-xs mt-1 ml-2">
                              {errors.departureDate}
                            </span>
                          )}

                          {showDepartureCalendar && (
                            <CalenderComponent
                              values={values}
                              setFieldValue={setFieldValue}
                              calClose={() => setShowDepartureCalendar(false)}
                              tripType={tripType}
                              dateType="departure"
                              onAfterConfirm={() => {
                                if (tripType === "Return") {
                                  setShowReturnCalendar(true);
                                  setActiveSection('return');
                                }
                              }}
                            />
                          )}
                        </div>
                        {/* Return */}
                        <div
                          className="flex flex-col relative w-full"
                          onClick={() => setActiveSection('return')}
                        >
                          <div
                            className={`flex justify-between items-center bg-white w-full h-14 rounded-[8px] ${getSectionBorderStyle('return')
                              } ${tripType !== "Return" ? "bg-opacity-20 bg-black/10 cursor-not-allowed" : ""}`}
                            onClick={tripType === "Return" ? () => setShowReturnCalendar(!showReturnCalendar) : undefined}
                          >
                            <div className="flex flex-row items-center space-x-4 ml-3">
                              <HiOutlineCalendar className="text-smokyGray w-7 h-7" strokeWidth={1} />
                              <div className="flex flex-col items-start">
                                <span className="text-smokyGray text-xs font-light">Returning</span>
                                <div className="text-nowrap text-sm font-light text-darkGray">
                                  {values.flights?.[0]?.returnDate
                                    ? values.flights[0].returnDate.slice(0, 10)
                                    : "-"}
                                </div>
                              </div>
                            </div>

                            <div className="flex flex-row space-x-2 items-center h-full mr-2">
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (tripType === "Return" && values.flights?.[0]?.returnDate) {
                                    const prevDate = new Date(values.flights[0].returnDate);
                                    prevDate.setDate(prevDate.getDate() - 1);
                                    if (prevDate >= new Date(values.flights[0].departureDate)) {
                                      setFieldValue("flights[0].returnDate", prevDate.toISOString().split("T")[0]);
                                    }
                                  }
                                }}
                                className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${tripType !== "Return" ? "bg-opacity-30 cursor-not-allowed" : ""
                                  }`}
                              >
                                <ChevronLeft className="w-4 h-4" />
                              </button>

                              <button
                                type="button"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  if (tripType === "Return") {
                                    const currentReturnDate =
                                      values.flights?.[0]?.returnDate || values.flights[0].departureDate || new Date().toISOString().split("T")[0];
                                    const nextDate = new Date(currentReturnDate);
                                    nextDate.setDate(nextDate.getDate() + 1);
                                    setFieldValue("flights[0].returnDate", nextDate.toISOString().split("T")[0]);
                                  }
                                }}
                                className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${tripType !== "Return" ? "bg-opacity-30 cursor-not-allowed" : ""
                                  }`}
                              >
                                <ChevronRight className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                          {errors.returnDate && touched.returnDate && (
                            <span className="text-red text-xs mt-1 ml-2">
                              {errors.returnDate}
                            </span>
                          )}
                          {showReturnCalendar && tripType === "Return" && (
                            <CalenderComponent
                              values={values}
                              setFieldValue={setFieldValue}
                              calClose={() => setShowReturnCalendar(false)}
                              tripType={tripType}
                              dateType="return"
                            />
                          )}
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
              <div className="flex flex-col md:flex-row w-full space-y-[135px] md:space-y-0 md:space-x-6">
                <div className="flex flex-col md:flex-row h-14 w-full border border-border rounded-[8px] bg-white items-center justify-between space-y-2 md:space-y-0">
                  {tripType === "Multi City" ? (
                    <div
                      className="flex flex-col relative w-full"
                      onClick={() => setActiveSection('Passengers')}
                    >
                      {/* Passengers */}
                      <div
                        className={`flex justify-between items-center w-full h-14 rounded-[8px] ${getSectionBorderStyle('Passengers')}`}
                        onClick={() => setShowPassengersModal(true)}
                      >
                        <div className="absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                        <div className="flex flex-row items-center space-x-4 ml-3">
                          <CiUser className="text-smokyGray w-7 h-7" />
                          <div className="flex flex-col">
                            <span className="text-smokyGray text-xs font-light">
                              Passengers
                            </span>
                            <div className="text-sm font-light text-darkGray flex flex-row space-x-2">
                              <span>{persons}</span>
                              <span>Persons</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-row space-x-2 items-center mr-2">
                          <div
                            type="button"
                            disabled={values.adults === 1}
                            onClick={(e) => {
                              if (values.adults > 1) {
                                e.stopPropagation();
                                setFieldValue("adults", values.adults - 1);
                              }
                            }}
                            className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.adults === 1 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : "cursor-pointer"
                              }`}
                          >
                            <Minus className="w-3 h-3" />
                          </div>
                          <button
                            type="button"
                            disabled={values.adults === 6 || persons === 9}
                            onClick={(e) => {
                              e.stopPropagation();
                              setFieldValue("adults", values.adults + 1);
                            }}
                            className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.adults === 6 || persons === 9 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : "cursor-pointer"
                              }`}
                          >
                            <Plus className="w-3 h-3" />
                          </button>
                          {/* <button
                          type="button"
                          onClick={() =>
                            setShowPassengersModal(!showPassengersModal)
                          }
                          className="text-smokyGray"
                          aria-label="Toggle passenger options"
                        >
                          {showPassengersModal ? <ChevronUp /> : <ChevronDown />}
                        </button> */}
                        </div>
                      </div>

                      {showPassengersModal && (
                        <PassengersAttachment
                          values={values}
                          setFieldValue={setFieldValue}
                          onClose={() => setShowPassengersModal(false)}
                        />
                      )}
                    </div>
                  ) : (
                    <div
                      className="flex flex-col relative w-full"
                      onClick={() => setActiveSection('Passengers')}
                    >
                      {/* Passengers */}
                      <div
                        className={`flex justify-between items-center w-full h-14 rounded-[8px] ${getSectionBorderStyle('Passengers')}`}
                        onClick={() => setShowPassengersModal(true)}
                      >
                        <div className="absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                        <div className="flex flex-row items-center space-x-4 ml-3">
                          <CiUser className="text-smokyGray w-7 h-7" />
                          <div className="flex flex-col">
                            <span className="text-smokyGray text-xs font-light">
                              Passengers
                            </span>
                            <div className="text-sm font-light text-darkGray flex flex-row space-x-2">
                              <span>{persons}</span>
                              <span>Persons</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex flex-row space-x-2 items-center">
                          <div
                            type="button"
                            disabled={values.adults === 1}
                            onClick={(e) => {
                              if (values.adults > 1) {
                                e.stopPropagation();
                                setFieldValue("adults", values.adults - 1);
                              }
                            }}
                            className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.adults === 1 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : "cursor-pointer"
                              }`}
                          >
                            <Minus className="w-3 h-3" />
                          </div>
                          <button
                            type="button"
                            disabled={values.adults === 6 || persons === 9}
                            onClick={(e) => {
                              e.stopPropagation();
                              setFieldValue("adults", values.adults + 1);
                            }}
                            className={`flex justify-center items-center w-7 h-7 bg-adjustcolor text-smokyGray rounded-[8px] ${values.adults === 6 || persons === 9 ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60" : "cursor-pointer"
                              }`}
                          >
                            <Plus className="w-3 h-3" />
                          </button>
                          <button
                            type="button"
                            onClick={() => setShowPassengersModal(!showPassengersModal)}
                            className="text-smokyGray"
                            aria-label="Toggle passenger options"
                          >
                          </button>
                        </div>
                      </div>

                      {showPassengersModal && (
                        <PassengersAttachment
                          values={values}
                          setFieldValue={setFieldValue}
                          onClose={() => setShowPassengersModal(false)}
                        />
                      )}
                    </div>
                  )}

                  {/* Travel Class */}
                  <div
                    className="flex flex-col relative w-full"
                    onClick={() => setActiveSection('TravelClass')}
                  >
                    <div
                      className={`flex justify-between items-center w-full h-14 bg-white rounded-[8px] ${getSectionBorderStyle('TravelClass')}`}
                      onClick={() => setShowTravelClassModal(true)}
                    >
                      <div className="absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                      <div className="flex flex-row items-center space-x-4 ml-3">
                        <PiSeatThin className="text-smokyGray w-7 h-7" strokeWidth={1} />
                        <div className="flex flex-col">
                          <span className="text-smokyGray text-xs font-light">
                            Travel Class
                          </span>
                          <div className="text-sm font-light text-darkGray">
                            {values.travelClass}
                          </div>
                        </div>
                      </div>
                    </div>
                    {showTravelClassModal && (
                      <TravelClassAttachment
                        values={values}
                        setFieldValue={setFieldValue}
                        onClose={() => setShowTravelClassModal(false)}
                      />
                    )}
                  </div>
                  {/* Baggages */}
                  <div
                    className="flex flex-col relative w-full"
                    onClick={() => setActiveSection('Baggages')}
                  >
                    <div
                      className={`flex justify-between items-center bg-white w-full h-14 rounded-[8px] ${getSectionBorderStyle('Baggages')}`}
                      onClick={() => setShowBaggagesModal(true)}
                    >
                      <div className="flex flex-row items-center space-x-4 ml-3">
                        <BriefcaseBusiness className="text-smokyGray w-7 h-7" strokeWidth={1} />
                        <div className="flex flex-col">
                          <span className="text-smokyGray text-xs font-light">
                            Baggage
                          </span>
                          <div className="text-sm font-light text-darkGray">
                            {values.baggage}
                          </div>
                        </div>
                      </div>
                    </div>
                    {showBaggagesModal && (
                      <BaggagesAttachment
                        values={values}
                        setFieldValue={setFieldValue}
                        onClose={() => setShowBaggagesModal(false)}
                      />
                    )}
                  </div>
                </div>
                <div className="flex  justify-center w-full md:w-[300px]">
                  {/* Search Button */}
                  <ButtonCom
                    variant="secondary"
                    size="lg"
                    width="full"
                    icon="search"
                    iconPosition="left"
                    type="submit"
                  >
                    Search
                  </ButtonCom>
                </div>
              </div>
            </div>
            {/* Promo Code Section */}
            <div className="w-full flex text-end items-end justify-end">
              {promoActive ? (
                <div className="relative flex w-full text-end items-start justify-end">
                  <input
                    type="text"
                    value={promoCode}
                    onChange={(e) => setPromoCode(e.target.value)}
                    onBlur={() => {
                      if (!promoCode) setPromoActive(false);
                    }}
                    className="w-full max-w-72 h-11 border border-border rounded-[8px] text-sm font-light text-darkGray mr-2 p-4 focus:outline-none"
                    placeholder={promoCode ? "" : "Enter Promo Code"}
                  />
                  <div className="flex items-center justify-center border border-border bg-orange rounded-[8px] h-11 w-11 p-2">
                    {isValidating ? (
                      <ChevronRight className="h-6 w-6 text-white" />
                    ) : isValid ? (
                      <Check className="h-6 w-6 text-white" />
                    ) : promoCode ? (
                      <ChevronRight className="h-6 w-6 text-white" />
                    ) : (
                      <ChevronRight className="h-6 w-6 text-white" />
                    )}
                  </div>
                </div>
              ) : (
                <span
                  className="font-extralight text-xs text-white cursor-pointer text-end"
                  onClick={() => setPromoActive(true)}
                >
                  {appliedPromoCode ? `Promo Code Added : ${appliedPromoCode}` : "+ Add Promo Code"}
                </span>
              )}
            </div>
          </Form>
        );
      }}
    </Formik>
  );
};

export default FlightForm;