import React, { useEffect, useRef, useState } from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import {
  ChevronLeft,
  ChevronRight,
  ChevronDown
} from "lucide-react";
import "./CustomCalendar.css";

const CalendarComponent = ({
  calClose,
  setFieldValue,
  values,
  tripType,
  dateType,
  flightIndex = 0,
  onAfterConfirm
}) => {

  const departureDate = values.flights[flightIndex].departureDate
    ? new Date(values.flights[flightIndex].departureDate)
    : null;

  const initialDate =
    dateType === "departure"
      ? (departureDate || new Date())
      : (values.flights[flightIndex].returnDate
          ? new Date(values.flights[flightIndex].returnDate)
          : departureDate || new Date());

  const calendarRef = useRef(null);
  const [selectedDate, setSelectedDate] = useState(initialDate);
  const [currentDate, setCurrentDate] = useState(initialDate);
  const [flexibleDays, setFlexibleDays] = useState(values.flights[flightIndex].flexibleDays || 0);
  const [showYearDropdown, setShowYearDropdown] = useState(false);
  
  // Track whether it's the first click for return date selection
  const [isFirstReturnClick, setIsFirstReturnClick] = useState(true);

  const [dateRange, setDateRange] = useState(
    dateType === "return" && departureDate
      ? values.flights[flightIndex].returnDate
        ? [departureDate, new Date(values.flights[flightIndex].returnDate)]
        : [departureDate, departureDate]
      : null
  );

  const formatLocalDate = (date) => {
    const year = date.getFullYear();
    const month = (`0${date.getMonth() + 1}`).slice(-2);
    const day = (`0${date.getDate()}`).slice(-2);
    return `${year}-${month}-${day}`;
  };

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const minDate = dateType === "departure" ? today : (departureDate || today);

  useEffect(() => {
    // Reset first click state when switching between departure and return
    setIsFirstReturnClick(true);
    
    if (dateType === "return" && departureDate) {
      const returnDate = values.flights[flightIndex].returnDate
        ? new Date(values.flights[flightIndex].returnDate)
        : departureDate;
      setDateRange([departureDate, returnDate]);
      setSelectedDate(returnDate);
    }
  }, [dateType, departureDate, values.flights, flightIndex]);

  // After selecting a departure date, auto-shift to return calendar if needed.
  const triggerAutoShift = () => {
    if (dateType === "departure" && tripType === "Return" && onAfterConfirm) {
      const currentMonthYear = {
        month: currentDate.getMonth(),
        year: currentDate.getFullYear()
      };
      sessionStorage.setItem('calendarMonthYear', JSON.stringify(currentMonthYear));
      
      // Close the departure calendar
      calClose();
      
      // Wait a moment and then trigger the return date picker to open
      setTimeout(() => {
        onAfterConfirm();
      }, 50);
    }
  };

  const handleDateSelect = (value) => {
    if (dateType === "departure") {
      const newDate = new Date(value);
      setSelectedDate(newDate);
      setFieldValue(`flights[${flightIndex}].departureDate`, formatLocalDate(newDate));
      setCurrentDate(new Date(newDate.getFullYear(), newDate.getMonth()));
      
      if (values.flights[flightIndex].returnDate && new Date(values.flights[flightIndex].returnDate) < newDate) {
        setFieldValue(`flights[${flightIndex}].returnDate`, "");
      }
      
      if (tripType === "Return") {
        triggerAutoShift();
      } else {
        calClose(); 
      }
    } else if (dateType === "return" && departureDate) {
      const tentativeReturn = Array.isArray(value) ? new Date(value[1] || value[0]) : new Date(value);
      if (tentativeReturn >= departureDate) {
        const range = [departureDate, tentativeReturn];
        setDateRange(range);
        setSelectedDate(tentativeReturn);
        setFieldValue(`flights[${flightIndex}].returnDate`, formatLocalDate(tentativeReturn));
        calClose();
      }
    }
  };

  const adjustDate = (increment) => {
    const newDate = new Date(selectedDate);
    newDate.setDate(newDate.getDate() + (increment ? 1 : -1));

    if (dateType === "departure" && newDate >= today) {
      setSelectedDate(newDate);
      setFieldValue(`flights[${flightIndex}].departureDate`, formatLocalDate(newDate));
      triggerAutoShift();
    } else if (dateType === "return" && departureDate && newDate >= departureDate) {
      setSelectedDate(newDate);
      setDateRange([departureDate, newDate]);
      setFieldValue(`flights[${flightIndex}].returnDate`, formatLocalDate(newDate));
      calClose();
    }
  };

  const handleMonthChange = (date) => {
    setCurrentDate(date);
  };

  const navigationLabel = ({ date }) => {
    const month = date.toLocaleString('default', { month: 'long' });
    const year = date.getFullYear();
    return (
      <div className="flex justify-between w-full text-base">
        <div>{month}</div>
        <div className="flex items-center cursor-pointer" onClick={(e) => {
          e.stopPropagation();
          setShowYearDropdown(!showYearDropdown);
        }}>
          <span>{year}</span>
          <ChevronDown className="ml-1" size={16} />
        </div>
      </div>
    );
  };

  const handleYearSelect = (year) => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(year);
    setCurrentDate(newDate);
    setShowYearDropdown(false);
  };

  const customNavigation = () => {
    return (
      <div className="custom-navigation flex justify-between items-center mb-4">
        <div className="flex items-center justify-center space-x-4">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const newDate = new Date(currentDate);
              newDate.setMonth(newDate.getMonth() - 1);
              setCurrentDate(newDate);
              handleMonthChange(newDate);
            }}
            className="p-1 hover:bg-gray-100 rounded-full transition duration-200"
          >
            <ChevronLeft size={20} className="text-[#6b6868]" />
          </button>
          <span className="text-base font-medium text-[#6b6868] w-24 text-center capitalize">
            {currentDate.toLocaleString("default", { month: "long" })}
          </span>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const newDate = new Date(currentDate);
              newDate.setMonth(newDate.getMonth() + 1);
              setCurrentDate(newDate);
              handleMonthChange(newDate);
            }}
            className="p-1 hover:bg-gray-100 rounded-full transition duration-200"
          >
            <ChevronRight size={20} className="text-[#6b6868]" />
          </button>
        </div>
        <div className="relative">
          <div
            className="flex items-center cursor-pointer year-selector"
            onClick={() => setShowYearDropdown(!showYearDropdown)}
          >
            <span className="text-base font-normal mr-1 text-[#6b6868]">{currentDate.getFullYear()}</span>
            <ChevronDown size={18} className="text-[#6b6868]" />
          </div>
          {showYearDropdown && (
            <div className="year-dropdown absolute right-0 mt-1 bg-white border border-gray rounded-lg shadow-lg z-30 max-h-48 overflow-y-auto ">
              {Array.from({ length: 100 }, (_, i) => {
                const year = new Date().getFullYear() - 2 + i;
                return (
                  <div
                    key={year}
                    className="px-4 py-2 hover:bg-gray cursor-pointer"
                    onClick={() => handleYearSelect(year)}
                  >
                    {year}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };

  const tileClassName = ({ date, view }) => {
    if (view !== "month") return null;
  
    const dateObj = new Date(date);
    dateObj.setHours(0, 0, 0, 0);
  
    if (dateType === "departure") {
      if (departureDate && dateObj.getTime() === departureDate.getTime()) {
        return "selected-departure";
      }
    } else if (dateType === "return" && departureDate) {
      const startDate = new Date(departureDate);
      startDate.setHours(0, 0, 0, 0);
  
      // Get return date from state or the Calendar's value array
      const returnDateValue = values.flights[flightIndex].returnDate 
        ? new Date(values.flights[flightIndex].returnDate)
        : (Array.isArray(selectedDate) ? selectedDate[1] : selectedDate);
  
      const endDate = returnDateValue ? new Date(returnDateValue) : null;
      if (endDate) endDate.setHours(0, 0, 0, 0);
  
      // Highlight departure date as range start always
      if (dateObj.getTime() === startDate.getTime()) {
        return "react-calendar__tile--rangeStart";
      }
  
      // Handle valid end date
      if (endDate && !isNaN(endDate)) {
        if (dateObj.getTime() === endDate.getTime()) {
          return "react-calendar__tile--rangeEnd";
        }
        if (dateObj > startDate && dateObj < endDate) {
          return "react-calendar__tile--range";
        }
      }
    }
    return null;
  };
  
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        calClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [calClose]);

  useEffect(() => {
    const handleClickOutsideDropdown = (event) => {
      if (showYearDropdown &&
          !event.target.closest(".year-dropdown") &&
          !event.target.closest(".year-selector")) {
        setShowYearDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutsideDropdown);
    return () => document.removeEventListener("mousedown", handleClickOutsideDropdown);
  }, [showYearDropdown]);

  // Sync the calendar month from stored data when switching from departure to return
  useEffect(() => {
    if (dateType === "return") {
      try {
        const savedMonthYear = sessionStorage.getItem('calendarMonthYear');
        if (savedMonthYear) {
          const { month, year } = JSON.parse(savedMonthYear);
          setCurrentDate(new Date(year, month, 1));
          sessionStorage.removeItem('calendarMonthYear');
        }
      } catch (error) {
        console.error("Error syncing calendar month:", error);
      }
    }
  }, [dateType]);

  return (
    <div
      className="absolute w-full md:w-96 z-20 bg-white border border-border rounded-[2px] flex flex-col p-4 space-y-4 mt-14 md:-ml-14"
      ref={calendarRef}
    >
      {customNavigation()}
      
      <Calendar
        onChange={handleDateSelect}
        value={
          dateType === "return" && departureDate
            ? dateRange || [departureDate, departureDate] // Initial range
            : selectedDate
        }
        onActiveStartDateChange={({ activeStartDate }) => handleMonthChange(activeStartDate)}
        activeStartDate={currentDate}
        selectRange={dateType === "return"}
        className="custom-calendar"
        showNavigation={false}
        minDate={minDate}
        formatShortWeekday={(locale, date) =>
          date.toLocaleDateString(locale, { weekday: "short" }).slice(0, 2)
        }
        tileClassName={tileClassName}
      />

      <div className="flex flex-col justify-between space-y-4">
        <div className="flex flex-col  p-2 rounded-[2px]">
          <div className="flex space-x-2 w-full justify-between items-center">
            {[0, 1, 2, 3].map((days) => (
              <label key={days} className="flex items-center space-x-1 bg-white px-2 py-1 rounded-[2px] shadow-[0_0_0_1px_#024575]">
                <input
                  type="radio"
                  name={`flexibleDays-${flightIndex}`}
                  value={days}
                  onChange={() => {
                    setFlexibleDays(days);
                    setFieldValue(`flights[${flightIndex}].flexibleDays`, days);
                  }}
                  checked={flexibleDays === days}
                />
                <span className={`text-[10px] font-medium ${flexibleDays === days ? "text-darkBlue" : "text-smokyGray"}`}>
                  {days === 0 ? "0 day" : `± ${days} ${days === 1 ? "day" : "days"}`}
                </span>
              </label>
            ))}
          </div>
          <span className="text-sm font-semibold text-center mt-2 text-darkBlue">
            Flexible Dates
          </span>
        </div>
      </div>
    </div>
  );
};

export default CalendarComponent; 