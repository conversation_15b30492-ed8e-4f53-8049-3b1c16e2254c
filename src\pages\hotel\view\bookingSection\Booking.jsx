import React, { useRef, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import ClimateRegion from './ClimateRegion';
import Review from './Review';
import Description from './Description';
import AllOffer from './AllOffer';
import ErrorBoundary from '../../../../components/ui/errorBoundary/ErrorBoundary';

const tabs = [
  { id: 'description', label: 'Description' },
  { id: 'allOffer', label: 'All Offers' },
  { id: 'review', label: 'Review' },
  { id: 'climateRegion', label: 'Climate + Region' },
];

const Booking = ({ hotelDetails, searchCriteria }) => {
  const location = useLocation();
  const localSearchCriteria = location.state?.formData || searchCriteria || {};
  const [activeTab, setActiveTab] = useState('allOffer');

  const sectionRefs = useRef({
    description: null,
    allOffer: null,
    review: null,
    climateRegion: null,
  });

  const scrollToSection = (id) => {
    const section = sectionRefs.current[id];
    if (section) {
      section.scrollIntoView({ behavior: 'smooth', block: 'start' });
      setActiveTab(id);
    }
  };

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveTab(entry.target.getAttribute('data-id'));
          }
        });
      },
      { rootMargin: '0px 0px -60% 0px', threshold: 0 }
    );

    Object.keys(sectionRefs.current).forEach((key) => {
      const section = sectionRefs.current[key];
      if (section) observer.observe(section);
    });

    return () => observer.disconnect();
  }, []);

  return (
    <ErrorBoundary>
      <div className="flex flex-col space-y-6">
        {/* Tab Navigation */}
        <div className="px-6 py-3 md:px-0 md:py-0">
          <div className="border border-border rounded-[2px] px-1 overflow-x-auto">
            <div className="flex flex-row items-center justify-start md:justify-between gap-2 min-w-[480px] md:min-w-0">
              {tabs.map((tab, index) => (
                <React.Fragment key={tab.id}>
                  <button
                    onClick={() => scrollToSection(tab.id)}
                    className={`flex-1 min-w-[100px] py-3 md:py-4 px-2 text-sm sm:text-base transition-all duration-200 ${activeTab === tab.id ? 'text-darkBlue font-medium' : 'text-gray hover:text-darkBlue'
                      }`}
                    aria-label={`Go to ${tab.label} section`}
                    aria-selected={activeTab === tab.id}
                  >
                    {tab.label}
                    {activeTab === tab.id && (
                      <div className="absolute bottom-0  top-0 left-0 w-full h-[2px] bg-darkBlue rounded-[2px]" />
                    )}
                  </button>
                  {index < tabs.length - 1 && <div className="hidden sm:block h-6 w-[1px] bg-border" />}
                </React.Fragment>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-10 scroll-smooth">
          <section ref={(el) => (sectionRefs.current.description = el)} data-id="description">
            <Description hotelDetails={hotelDetails} searchCriteria={localSearchCriteria} />
          </section>
          <section ref={(el) => (sectionRefs.current.allOffer = el)} data-id="allOffer">
            <AllOffer hotelDetails={hotelDetails} searchCriteria={localSearchCriteria} />
          </section>
          <section ref={(el) => (sectionRefs.current.review = el)} data-id="review">
            <Review />
          </section>
          <section ref={(el) => (sectionRefs.current.climateRegion = el)} data-id="climateRegion">
            <ClimateRegion />
          </section>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default Booking;