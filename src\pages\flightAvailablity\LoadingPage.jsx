import React, { useEffect, useState } from "react";
import <PERSON>flyLogo from "../../assets/loading/logo.png";
import Image1 from "../../assets/loading/img7.jpeg";
import Image2 from "../../assets/loading/img10.jpg";
import Image3 from "../../assets/loading/img9.jpg";
import Icon1 from "../../assets/loading/icon1.svg";
import Icon2 from "../../assets/loading/icon2.svg";
import { Calendar, Plane } from "lucide-react";
import { MdOutlineAirplanemodeActive } from "react-icons/md";
import { FaPlane } from "react-icons/fa6";

function LoadingPage() {
  const Images = [
    { src: Image1, alt: "Resort" },
    { src: Image2, alt: "Bur<PERSON>fa" },
    { src: Image3, alt: "Statue of Liberty" },
  ];

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [progressArray, setProgressArray] = useState(
    new Array(Images.length).fill(0)
  );
  const intervalTime = 3000;

  useEffect(() => {
    const imageInterval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => {
        const nextIndex = (prevIndex + 1) % Images.length;

        if (nextIndex === 0) {                                 // looping back to first image, reset progress array
          setProgressArray(new Array(Images.length).fill(0));
        }

        return nextIndex;
      });
    }, intervalTime);

    return () => clearInterval(imageInterval);
  }, []);

  useEffect(() => {
    //  avoid duplication
    let progressInterval = setInterval(() => {
      setProgressArray((prevArray) => {
        const newArr = [...prevArray];
        for (let i = 0; i < currentImageIndex; i++) {
          newArr[i] = 100;
        }
        if (newArr[currentImageIndex] < 100) {
          newArr[currentImageIndex] += 1;
        }
        for (let i = currentImageIndex + 1; i < Images.length; i++) {
        }

        return newArr;
      });
    }, intervalTime / 100);

    return () => clearInterval(progressInterval);
  }, [currentImageIndex]);

    const [activeIndex, setActiveIndex] = useState(0);
    
    useEffect(() => {
      const interval = setInterval(() => {
        setActiveIndex((prevIndex) => (prevIndex + 1) % 3);
      }, 800); 
  
      return () => clearInterval(interval);
    }, []);
  
    const dotOpacities = [0.4, 0.6, 1];

  return (
    <div className="min-h-screen bg-white flex items-center justify-center p-4 w-full">
      <div className="max-w-[1440px] w-full grid md:grid-cols-2 gap-8 overflow-hidden">
        {/* Image Section */}
        <div className="relative w-[667px] h-[687px] overflow-hidden rounded-3xl">
          {Images.map((image, index) => (
            <img
              key={index}
              src={image.src}
              alt={image.alt}
              className={`w-full h-full object-cover absolute transition-opacity duration-1000 ease-in-out ${currentImageIndex === index
                ? "opacity-100 scale-100"
                : "opacity-0 scale-100"
                }`}
            />
          ))}

          {/* Progress Lines */}
          <div className="absolute top-6 left-0 right-0 flex space-x-2 px-4">
            {Images.map((_, index) => (
              <div
                key={index}
                className="flex-1 bg-white/50 rounded-full h-1 overflow-hidden"
              >
                <div
                  className="bg-white h-full transition-all duration-75 ease-linear"
                  style={{ width: `${progressArray[index]}%` }}
                />
              </div>
            ))}
          </div>

          {/* Calendar Overlay - Always Visible */}
          <div className="absolute bottom-6 left-6 flex items-center space-x-4 p-3">
          <img src={Icon1} alt="Image 1" className="w-24 h-24" style={{ opacity: 0.75 }} />
          <img src={Icon2} alt="Image 2" className="w-24 h-24" style={{ opacity: 0.75 }} />
          </div>
        </div>

        {/* Content Section */}
        <div className="p-6 flex flex-col justify-center">
          {/* Logo */}
          <div className="flex items-center mb-4 -ml-4">
            <img src={EflyLogo} alt="Efly Logo" className="w-40" />
          </div>

          {/* Tagline */}
          <h2 className="text-[38px] font-thin text-[#777777] font-poppins ml-2 flex items-center gap-2 ">
            Finding Routes For Your Destiny
            {/* <FaPlane className="w-8 h-8 animate-planeWiggle" /> */}
          </h2>
          {/* Navigation Dots */}
          <div className="flex absolute space-x-2 ml-72 bottom-20 items-center">
            {dotOpacities.map((baseOpacity, index) => {
              const opacity = dotOpacities[(index + activeIndex) % 3];
              return (
                <div
                  key={index}
                  className="w-3 h-3 rounded-full bg-gray"
                  style={{ opacity }}
                />
              );
            })}
          </div>
          {/* <div className="flex flex-col items-center absolute gap-4 justify-center">
            <MdOutlineAirplanemodeActive className="w-8 h-8 animate-planeWiggle" />
          </div> */}
        </div>
      </div>
    </div>
  );
}

export default LoadingPage;
