import React, { useState } from "react";
import Line2 from "../../assets/view/Line 2.svg";
import airports from "../../data/airports.json";
import { airlines } from "../flightAvailablity/AirlinesData";
import { FaPlane } from "react-icons/fa";
import { Briefcase, ChevronDown } from "lucide-react";

const FlightBookingCard = ({ bookingDetails }) => {
    const { outboundStops, returnStops } = bookingDetails;
    const [isOpen, setIsOpen] = useState(false);

    const getAirportByIata = (iataCode) => {
        return airports.find((airport) => airport.iata === iataCode) || { city: iataCode };
    };

    const getStopsText = (stops) => {
        const stopCount = stops.length - 1;
        return stopCount === 0 ? "Direct" : `${stopCount} Stop${stopCount > 1 ? 's' : ''}`;
    };

    const computeTotalDuration = (legs) => {
        if (!legs || legs.length === 0) return 'N/A';
        let totalMs = 0;
        for (const [index, leg] of legs.entries()) {
            if (!leg.departure_date || !leg.departure_time || !leg.arrival_date || !leg.arrival_time) {
                return 'N/A'; // Fallback if any required field is missing
            }
            const depDateTime = new Date(`${leg.departure_date}T${leg.departure_time}`);
            const arrDateTime = new Date(`${leg.arrival_date}T${leg.arrival_time}`);
            if (isNaN(depDateTime) || isNaN(arrDateTime)) return 'N/A';
            totalMs += arrDateTime - depDateTime;
            if (index < legs.length - 1) {
                const nextLeg = legs[index + 1];
                const nextDepDateTime = new Date(`${nextLeg.departure_date}T${nextLeg.departure_time}`);
                const waitingMs = nextDepDateTime - arrDateTime;
                totalMs += Math.max(waitingMs, 0);
            }
        }
        const hours = Math.floor(totalMs / (1000 * 60 * 60));
        const minutes = Math.floor((totalMs % (1000 * 60 * 60)) / (1000 * 60));
        return `${hours}h ${minutes}m`;
    };

    const getDaysDifference = (startDate, endDate) => {
        const start = new Date(startDate);
        const end = new Date(endDate);
        if (isNaN(start) || isNaN(end)) return "N/A";
        const differenceMs = end - start;
        return Math.floor(differenceMs / (1000 * 60 * 60 * 24)); 
    };
    

    const outboundDuration = computeTotalDuration(outboundStops);
    const returnDuration = computeTotalDuration(returnStops);

    const {
        outboundAirlineCode = "default",
        returnAirlineCode = "default",
        departureTime = "N/A",
        arrivalTime = "N/A",
        returnDepartureTime = "N/A",
        returnArrivalTime = "N/A",
        departureDate = "N/A",
        arrivalDate = "N/A",
        returnDepartureDate = "N/A",
        returnArrivalDate = "N/A",
        outboundBaggage = { FBA_1: "N/A" },
        returnBaggage = { FBA_1: "N/A" },
        totalPrice = "N/A",
        adults = 0,
        children = 0,
        babies = 0,
        priceDetails = {},
    } = bookingDetails;
    console.log("Booking Details:", bookingDetails);

    const outboundDepApt = outboundStops[0]?.departure_airport_code || "N/A";
    const outboundDesApt = outboundStops[outboundStops.length - 1]?.arrival_airport_code || "N/A";
    const returnDepApt = returnStops[0]?.departure_airport_code || "N/A";
    const returnDesApt = returnStops[returnStops.length - 1]?.arrival_airport_code || "N/A";

    return (
        <div className="max-w-md mx-auto bg-white rounded-xl overflow-hidden  border border-darkBlue">
            {/* Outbound Flight */}
            <div className="p-5">
                <div className="flex items-center justify-between mb-2 -mt-1">
                    <div className="flex items-center space-x-4 ">
                        <div className="w-32 bg-white h-14 border border-border rounded-2xl flex items-center justify-center ">
                            <img
                                src={airlines[outboundAirlineCode]?.logo || airlines.default.logo}
                                alt="Airline Logo"
                                className="w-16 h-14 object-contain  items-center"
                            />
                        </div>
                        {/* <div className="text-xs flex items-center space-x-2 ">
                            <Plane size={14} className=" text-black/50" />
                            <span className="text-black/60">
                                {airlines[outboundAirlineCode]?.name || "Airline"}
                            </span>
                        </div> */}
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                        <span className="bg-blue-50 text-darkBlue text-xs font-normal px-3 py-1 mb-0 rounded-full">
                            Economy
                        </span>
                        <div className="bg-orange/5 text-orange text-xs font-normal px-4 py-1 rounded-full flex items-center">
                            <Briefcase size={14} className="mr-1 " />
                            <span>Baggage included 40kg</span>
                        </div>
                    </div>
                </div>
                <div className="flex justify-between items-center my-3">
                    <div className="text-center">
                        <span className="text-base text-smokyGray font-semibold">
                            {getAirportByIata(outboundDepApt).city.toUpperCase()}
                        </span>
                        <span className="block font-bold text-4xl text-darkBlue">{departureTime}</span>
                        <span className="text-xs text-smokyGray tracking-widest">{departureDate}</span>
                    </div>
                    <div className="flex flex-col items-center mx-4 space-y-2">
                        <div className="text-xs text-smokyGray ">{outboundDuration}</div>
                        <img src={Line2} alt="Line" className="w-32 h-8 font-bold " />
                        <div className="absolute top-[128px] left-[198px] w-8 h-8 bg-darkBlue text-white rounded-full flex items-center justify-center -translate-x-1/2 -translate-y-1/2">
                            <FaPlane size={10} className="text-white" />
                        </div>
                        <div className="text-xs text-smokyGray mt-1">{getStopsText(outboundStops)}</div>
                    </div>
                    <div className="text-center">
                        <span className="text-base text-smokyGray font-semibold">
                            {getAirportByIata(outboundDesApt).city.toUpperCase()}
                        </span>
                        <span className="block font-bold text-4xl text-darkBlue">{arrivalTime}</span>
                        <span className="text-xs text-smokyGray tracking-widest">{arrivalDate}</span>
                    </div>
                </div>
            </div>

            <div className="flex items-center justify-center my-2">
                <hr className="flex-grow border-border border-t-1" />
                <span className="mx-4 px-4 py-1 border border-border rounded-full text-xs text-smokyGray">{getDaysDifference(departureDate, returnDepartureDate)} Days in {getAirportByIata(outboundDesApt).city.toUpperCase()}</span>
                <hr className="flex-grow border-border border-t-1" />
            </div>

            {/* Return Flight */}
            <div className="px-5 py-3">
                <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-4">
                        <div className="w-32 bg-white h-14 border border-border rounded-2xl flex items-center justify-center ">
                            <img
                                src={airlines[returnAirlineCode]?.logo || airlines.default.logo}
                                alt="Airline Logo"
                                className="w-16 h-14 object-contain  items-center"
                            />
                        </div>
                        {/* <div className="text-xs flex items-center space-x-2 ">
                            <Plane size={14} className=" text-black/50" />
                            <span className="text-black/60">
                                {airlines[returnAirlineCode]?.name || "Airline"}
                            </span>
                        </div> */}
                    </div>
                    <div className="flex flex-col items-end space-y-2">
                        <span className="bg-blue-50 text-darkBlue text-xs font-normal px-3 py-1 mb-0 rounded-full">
                            Economy
                        </span>
                        <div className="bg-orange/5 text-orange text-xs font-normal px-4 py-1 rounded-full flex items-center">
                            <Briefcase size={14} className="mr-1 " />
                            <span>Baggage included 40kg</span>
                        </div>
                    </div>
                </div>
                <div className="flex justify-center items-center my-3">
                    <div className="text-center">
                        <span className="text-base text-smokyGray font-semibold">
                            {getAirportByIata(returnDepApt).city.toUpperCase()}
                        </span>
                        <span className="block font-bold text-4xl text-darkBlue">{returnDepartureTime}</span>
                        <span className="text-xs text-smokyGray tracking-widest">{returnDepartureDate}</span>
                    </div>
                    <div className="flex flex-col items-center mx-4 space-y-2">
                        <div className="text-xs text-smokyGray ">{returnDuration}</div>
                        <img src={Line2} alt="Line" className="w-32 h-8 font-bold " />
                        <div className="absolute top-[380px] left-[200px] w-8 h-8 bg-darkBlue text-white rounded-full flex items-center justify-center -translate-x-1/2 -translate-y-1/2">
                            <FaPlane size={10} className="text-white" />
                        </div>
                        <div className="text-xs text-smokyGray mt-1">{getStopsText(returnStops)}</div>
                    </div>
                    <div className="text-center">
                        <span className="text-base font-semibold text-smokyGray">
                            {getAirportByIata(returnDesApt).city.toUpperCase()}
                        </span>
                        <span className="block font-bold text-4xl text-darkBlue">{returnArrivalTime}</span>
                        <span className="text-xs text-smokyGray tracking-widest">{returnArrivalDate}</span>
                    </div>
                </div>
            </div>

            <hr className="border-border" />

            {/* Price Details */}
            <div className="p-5">
                {/* Dropdown Header */}
                <div
                    className="flex justify-between items-center cursor-pointer"
                    onClick={() => setIsOpen(!isOpen)}
                >
                    <h3 className="text-2xl font-medium text-darkBlue mb-3">Price Details</h3>
                    <ChevronDown
                        size={24}
                        className={`transition-transform text-darkBlue duration-300 `}
                    />
                </div>
                <div className="flex justify-between mb-2">
                    <span className="text-smokyGray">Economy class</span>
                </div>

                {/* Collapsible Content */}

                <div className={`overflow-hidden transition-all duration-300 ${isOpen ? "max-h-[500px] opacity-100" : "max-h-0 opacity-0"}`}>
                
                    <div className="text-base">
                        <div className="flex justify-between mb-2 font-medium text-lg">
                            <span className="text-darkBlue "> Fare Price</span>
                            <span className="text-darkBlue ">
                                CHF {totalPrice.toFixed(2)}
                            </span>
                        </div>


                        {adults > 0 && (
                            <div className="flex justify-between mb-2">
                                <span className="text-darkBlue">{adults} x Adult Sell</span>
                                <span className="text-darkBlue">
                                    CHF {priceDetails.price_per_adtSell ? (priceDetails.price_per_adtSell * adults).toFixed(2) : "N/A"}
                                </span>
                            </div>
                        )}
                        {adults > 0 && (
                            <div className="flex justify-between mb-2">
                                <span className="text-darkBlue">{adults} x Adult Tax</span>
                                <span className="text-darkBlue">
                                    CHF {priceDetails.tax_per_adt ? (priceDetails.tax_per_adt * adults).toFixed(2) : "N/A"}
                                </span>
                            </div>
                        )}

                        {children > 0 && (
                            <div className="flex justify-between mb-2">
                                <span className="text-darkBlue">{children} x Child Sell</span>
                                <span className="text-darkBlue">
                                    CHF {priceDetails.price_per_chdSell ? (priceDetails.price_per_chdSell * children).toFixed(2) : "N/A"}
                                </span>
                            </div>
                        )}
                        {children > 0 && (
                            <div className="flex justify-between mb-2">
                                <span className="text-darkBlue">{children} x Child Tax</span>
                                <span className="text-darkBlue">
                                    CHF {priceDetails.tax_per_chd ? (priceDetails.tax_per_chd * children).toFixed(2) : "N/A"}
                                </span>
                            </div>
                        )}

                        {babies > 0 && (
                            <div className="flex justify-between mb-2">
                                <span className="text-darkBlue">{babies} x Baby Sell</span>
                                <span className="text-darkBlue">
                                    CHF {priceDetails.price_per_infSell ? (priceDetails.price_per_infSell * babies).toFixed(2) : "N/A"}
                                </span>
                            </div>
                        )}
                        {babies > 0 && (
                            <div className="flex justify-between mb-2">
                                <span className="text-darkBlue">{babies} x Baby Tax</span>
                                <span className="text-darkBlue">
                                    CHF {priceDetails.tax_per_inf ? (priceDetails.tax_per_inf * babies).toFixed(2) : "N/A"}
                                </span>
                            </div>
                        )}
                        

                        <div className="flex justify-between font-medium text-lg">
                            <span className="text-darkBlue">Travel Insurance</span>
                            <span className="text-darkBlue">
                                CHF {priceDetails.totalTax
                                    ? ((priceDetails.tax_per_adt * adults) +
                                        (priceDetails.tax_per_chd * children) +
                                        (priceDetails.tax_per_inf * babies)
                                    ).toFixed(2)
                                    : "N/A"}
                            </span>
                        </div>
                        </div>
                    </div>
            </div>

            <hr className="border-border" />

            {/* Total */}
            <div className="px-5">
                <div className="flex justify-between items-center">
                    <h3 className="text-2xl font-semibold text-darkBlue">Total</h3>
                    <div className="text-right mt-4">
                        <div className="text-2xl font-semibold text-darkBlue">CHF {totalPrice.toFixed(2)}</div>
                        <div className="text-xs text-smokyGray">Tax included</div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FlightBookingCard;