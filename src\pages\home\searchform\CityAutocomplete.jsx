import React, { useEffect, useState, useRef } from "react";
import Autosuggest from "react-autosuggest";
import { X } from "lucide-react";
import airports from "../../../data/airports.json";
import { useLocation } from "react-router-dom";

// Filter suggestions based on input value (at least 3 letters)
const getSuggestions = (value) => {
  const inputValue = value.trim().toLowerCase();
  if (inputValue.length < 1) return [];
  return airports.filter(
    (airport) =>
      (airport.city && airport.city.toLowerCase().includes(inputValue)) ||
      (airport.iata && airport.iata.toLowerCase().includes(inputValue)) ||
      (airport.name && airport.name.toLowerCase().includes(inputValue))
  );
};

// // Formats suggestion as "City (IATA)"
// const getSuggestionValue = (suggestion) =>
//   `${suggestion.city}, ${suggestion.country} (${suggestion.iata} ${suggestion.name})`;

const getSuggestionValue = (suggestion) => `${suggestion.city} (${suggestion.iata})`;

// Renders a suggestion item in the dropdown with modern styling
const renderSuggestion = (suggestion) => (
<div className="w-full px-4 py-2 hover:bg-darkBlue hover:text-white text-black cursor-pointer transition-colors duration-150 space-y-2 relative">
  <div className="absolute -bottom-[1px] left-10 w-[204px] h-[0.5px] bg-border"></div>
   <div className="flex flex-row justify-between">
    <span className="font-normal">{suggestion.city}</span>{" "}
    <span className="">{suggestion.iata}</span>
    </div>
    <div className="flex flex-row justify-between ">
    <span className=" text-xs">{suggestion.country}</span>
    <span className=" text-xs font-extralight">{suggestion.name}</span>
    </div>
  </div>
);

const renderSuggestionsContainer = ({ containerProps, children }) => {
  const { key, ...restProps } = containerProps; 

  return (
    <div
      key={key}
      {...restProps}
      className="absolute z-50 w-[262px] -ml-[57px] bg-white max-h-60 mt-4
        overflow-y-auto
        shadow-lg
        scrollbar-thin
        [scrollbar-width:thin]
        [scrollbar-color:theme(colors.smokyGray)_transparent]"
    >
      {children}
    </div>
  );
};

export const DepartureCityAutocomplete = ({ field, form, placeholder, onSelectFlyTo,isActive }) => {
  const inputRef = useRef(null);
  const location = useLocation();
  const state = location.state || {};
  const dep_apt = state.dep_apt || "";
  const dep_apt_full = state.dep_apt_full || dep_apt;

  const [displayValue, setDisplayValue] = useState(dep_apt_full);
  const [suggestions, setSuggestions] = useState([]);
  const [isFocused, setIsFocused] = useState(false);

  // Option 1: Run only once on mount
  useEffect(() => {
    if (!dep_apt) return;
    const airport = airports.find((airport) => airport.iata === dep_apt);
    if (airport) {
      const formatted = `${airport.city} (${airport.iata})`;
      setDisplayValue(formatted);
      form.setFieldValue(field.name, airport.iata);
    }
  }, []); // empty dependency array

  // Option 2: If you need to update when dep_apt changes, use the code below instead:
  // useEffect(() => {
  //   if (!dep_apt) return;
  //   const airport = airports.find((airport) => airport.iata === dep_apt);
  //   if (airport) {
  //     const formatted = `${airport.city}, ${airport.country} (${airport.iata}, ${airport.name})`;
  //     if (displayValue !== formatted) {
  //       setDisplayValue(formatted);
  //     }
  //     if (form.values[field.name] !== airport.iata) {
  //       form.setFieldValue(field.name, airport.iata);
  //     }
  //   }
  // }, [dep_apt]);

  const onChange = (event, { newValue }) => {
    setDisplayValue(newValue);
    // Reset the Formik field value until a valid suggestion is selected
    form.setFieldValue(field.name, "");
  };

  const onSuggestionsFetchRequested = ({ value }) => {
    setSuggestions(getSuggestions(value));
  };

  const onSuggestionsClearRequested = () => {
    setSuggestions([]);
  };

  const onSuggestionSelected = (event, { suggestion }) => {
    const suggestionText = getSuggestionValue(suggestion);
    setDisplayValue(suggestionText);
    form.setFieldValue(field.name, suggestion.iata);
    if (onSelectFlyTo) {
      setTimeout(() => {
        onSelectFlyTo();
      }, 100);
    }
  };

  const inputProps = {
    placeholder: placeholder || "Type a city...",
    value: displayValue,
    onChange: onChange,
    onFocus: (event) => {
      event.target.select();
      setIsFocused(true);
    },
    onBlur: () => setIsFocused(false),
    className: `w-full bg-white text-sm font-light text-black focus:outline-none`,
    ref: inputRef,
  };

  return (
    <div className={`relative  `}>
      <Autosuggest
        suggestions={suggestions}
        onSuggestionsFetchRequested={onSuggestionsFetchRequested}
        onSuggestionsClearRequested={onSuggestionsClearRequested}
        getSuggestionValue={getSuggestionValue}
        renderSuggestion={renderSuggestion}
        onSuggestionSelected={onSuggestionSelected}
        inputProps={inputProps}
        renderSuggestionsContainer={renderSuggestionsContainer}
      />
      {displayValue && (
        <button
          type="button"
          onClick={() => {
            setDisplayValue("");
            form.setFieldValue(field.name, "");
            if (inputRef.current) {
              inputRef.current.focus();
            }
          }}
          className="absolute -right-4 top-1 transform -translate-y-1/2 text-[#EFEFEF] w-7 h-7 flex items-center justify-center"
          title="Clear departure city"
          aria-label="Clear departure city"
        >
          <X className=" w-4 h-4"/>
        </button>
      )}
      {/* Hidden input to ensure Formik submits the IATA code */}
      <input type="hidden" name={field.name} value={field.value} onChange={form.handleChange} />
    </div>
  );
};

export const DestinationCityAutocomplete = ({ field, form, placeholder, isActive}) => {
  const inputRef = useRef(null);
  const location = useLocation();
  const state = location.state || {};
  const des_apt = state.des_apt || "";
  const des_apt_full = state.des_apt_full || des_apt;

  const [displayValue, setDisplayValue] = useState(des_apt_full);
  const [suggestions, setSuggestions] = useState([]);
  const [isFocused, setIsFocused] = useState(false);

  // Option 1: Run only once on mount
  useEffect(() => {
    if (!des_apt) return;
    const airport = airports.find((airport) => airport.iata === des_apt);
    if (airport) {
      const formatted = `${airport.city} (${airport.iata})`;
      setDisplayValue(formatted);
      form.setFieldValue(field.name, airport.iata);
    }
  }, []); // empty dependency array

  // Option 2: Use the following if you need to update on changes:
  // useEffect(() => {
  //   if (!des_apt) return;
  //   const airport = airports.find((airport) => airport.iata === des_apt);
  //   if (airport) {
  //     const formatted = `${airport.city}, ${airport.country} (${airport.iata}, ${airport.name})`;
  //     if (displayValue !== formatted) {
  //       setDisplayValue(formatted);
  //     }
  //     if (form.values[field.name] !== airport.iata) {
  //       form.setFieldValue(field.name, airport.iata);
  //     }
  //   }
  // }, [des_apt]);

  const onChange = (event, { newValue }) => {
    setDisplayValue(newValue);
    form.setFieldValue(field.name, "");
  };

  const onSuggestionsFetchRequested = ({ value }) => {
    setSuggestions(getSuggestions(value));
  };

  const onSuggestionsClearRequested = () => {
    setSuggestions([]);
  };


  const onSuggestionSelected = (event, { suggestion }) => {
    const suggestionText = getSuggestionValue(suggestion);
    setDisplayValue(suggestionText);
    form.setFieldValue(field.name, suggestion.iata);
  };

  const inputProps = {
    placeholder: placeholder || "Type a city...",
    value: displayValue,
    onChange: onChange,
    onFocus: (event) => {
      event.target.select();
      setIsFocused(true);
    },
    onBlur: () => setIsFocused(false),
    className: `w-full bg-white text-sm font-light text-black focus:outline-none `,
    ref: inputRef,
  };

  return (
    <div className={`relative `}>
      <Autosuggest
        suggestions={suggestions}
        onSuggestionsFetchRequested={onSuggestionsFetchRequested}
        onSuggestionsClearRequested={onSuggestionsClearRequested}
        getSuggestionValue={getSuggestionValue}
        renderSuggestion={renderSuggestion}
        onSuggestionSelected={onSuggestionSelected}
        inputProps={inputProps}
        renderSuggestionsContainer={renderSuggestionsContainer}
      />
      {displayValue && (
        <button
          type="button"
          onClick={() => {
            setDisplayValue("");
            form.setFieldValue(field.name, "");
            if (inputRef.current) {
              inputRef.current.focus();
            }
          }}
          className="absolute -right-4 top-1 transform -translate-y-1/2 text-[#EFEFEF] w-7 h-7 flex items-center justify-center"
          title="Clear destination city"
          aria-label="Clear destination city"
        >
         <X className=" w-4 h-4"/>
        </button>
      )}
      <input type="hidden" name={field.name} value={field.value} onChange={form.handleChange} />
    </div>
  );
};