import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { toast } from 'react-toastify';
import * as api from '../services/api/Api';

// Fetch hotel IDs by city
export const fetchHotelIdsByCity = createAsyncThunk(
  'hotels/fetchHotelIdsByCity',
  async (requestBody, { rejectWithValue }) => {
    try {
      const response = await api.fetchHotelIdsByCity(requestBody);
      console.log('Hotel IDs by City Response:', JSON.stringify(response, null, 2));
      toast.success('Hotel IDs fetched successfully!');
      return response;
    } catch (error) {
      console.error('fetchHotelIdsByCity Error:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Fetch filter counts
export const fetchFilterCounts = createAsyncThunk(
  'hotels/fetchFilterCounts',
  async (hotelIds, { rejectWithValue }) => {
    try {
      const response = await api.fetchFilterCounts(hotelIds);
      console.log('Filter Counts Response:', JSON.stringify(response, null, 2));
      toast.success('Filter counts fetched successfully!');
      return response;
    } catch (error) {
      console.error('fetchFilterCounts Error:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Fetch filtered hotels
export const fetchFilteredHotels = createAsyncThunk(
  'hotels/fetchFilteredHotels',
  async (filterRequest, { rejectWithValue, getState }) => {
    try {
      const data = await api.fetchFilteredHotels(filterRequest);
      console.log('Filtered Hotels Response:', JSON.stringify(data, null, 2));
      const state = getState();
      const originalHotels = state.hotels.originalHotels;
      const priceRange = state.hotels.priceRange;

      const enrichedHotels = data.filteredHotels.map((hotel) => {
        const originalHotel = originalHotels.find((h) => h.hotelId === hotel.hotelId);
        return {
          ...hotel,
          cheapestRoomTypeCode: originalHotel?.cheapestRoomTypeCode || null,
          cheapestRoomTypeName: originalHotel?.cheapestRoomTypeName || 'Standard Room',
          cheapestRoomCharge: originalHotel?.cheapestRoomCharge ?? null,
        };
      });

      const priceFilteredHotels = enrichedHotels.filter(
        (hotel) =>
          typeof hotel.cheapestRoomCharge === 'number' &&
          hotel.cheapestRoomCharge >= priceRange[0] &&
          hotel.cheapestRoomCharge <= priceRange[1]
      );

      toast.success('Filtered hotels fetched successfully!');
      return {
        filteredHotels: priceFilteredHotels,
        totalFilteredHotels: priceFilteredHotels.length,
      };
    } catch (error) {
      console.error('fetchFilteredHotels Error:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Fetch hotel details by hotel ID
export const fetchHotelDetails = createAsyncThunk(
  'hotels/fetchHotelDetails',
  async (hotelId, { rejectWithValue }) => {
    try {
      const response = await api.fetchHotelDetailsByHotelId(hotelId);
      console.log('Hotel Details Response:', JSON.stringify(response, null, 2));
      toast.success('Hotel details fetched successfully!');
      return response;
    } catch (error) {
      console.error('fetchHotelDetails Error:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Fetch room availability
export const fetchRoomAvailability = createAsyncThunk(
  'hotels/fetchRoomAvailability',
  async (requestBody, { rejectWithValue }) => {
    try {
      const response = await api.fetchRooms(requestBody);
      console.log('Room Availability Response:', JSON.stringify(response, null, 2));
      toast.success('Room availability fetched successfully!');
      return response;
    } catch (error) {
      console.error('fetchRoomAvailability Error:', error);
      return rejectWithValue(error.message);
    }
  }
);

// Fetch getRoomsWithBlocking
export const getRoomsWithBlocking = createAsyncThunk(
  'hotels/getRoomsWithBlocking',
  async (requestBody, { rejectWithValue }) => {
    try {
      const response = await api.getRoomsWithBlocking(requestBody);
      console.log('getRoomsWithBlocking Response:', JSON.stringify(response, null, 2));
      toast.success('Room blocked successfully!');
      return response;
    } catch (error) {
      console.error('getRoomsWithBlocking Error:', error);
      const errorMessage = error.message.includes('No bookable rates')
        ? 'Selected room is no longer available. Please choose another room.'
        : `Failed to block room: ${error.message}`;
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Save booking
export const saveBooking = createAsyncThunk(
  'hotels/saveBooking',
  async (requestBody, { rejectWithValue }) => {
    try {
      const response = await api.saveBooking(requestBody);
      console.log('Save Booking Response:', JSON.stringify(response, null, 2));
      toast.success('Booking saved successfully!');
      return response;
    } catch (error) {
      console.error('saveBooking Error:', error);
      const errorMessage = error.message.includes('Booking save failed')
        ? 'Unable to save booking. Please verify your details and try again.'
        : `Failed to save booking: ${error.message}`;
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Book itinerary without confirmation
export const bookItineraryWithConfirmNo = createAsyncThunk(
  'hotels/bookItineraryWithConfirmNo',
  async (requestBody, { rejectWithValue }) => {
    try {
      const response = await api.bookItineraryWithConfirmNo(requestBody);
      console.log('Book Itinerary (No Confirm) Response:', JSON.stringify(response, null, 2));
      toast.success('Booking prepared successfully!');
      return response;
    } catch (error) {
      console.error('bookItineraryWithConfirmNo Error:', error);
      const errorMessage = 'Failed to prepare booking. Please ensure all details are correct.';
      toast.error(errorMessage);
      return rejectWithValue(errorMessage);
    }
  }
);

// Book itinerary with confirmation
export const bookItineraryWithConfirmYes = createAsyncThunk(
  'hotels/bookItineraryWithConfirmYes',
  async (requestBody, { rejectWithValue }) => {
    try {
      const response = await api.bookItineraryWithConfirmYes(requestBody);
      console.log('Book Itinerary (Confirm) Response:', JSON.stringify(response, null, 2));
      toast.success('Booking confirmed successfully!');
      return response;
    } catch (error) {
      console.error('bookItineraryWithConfirmYes Error:', error);
      return rejectWithValue(error.message);
    }
  }
);

const hotelSlice = createSlice({
  name: 'hotels',
  initialState: {
    hotels: [],
    originalHotels: [],
    totalHotels: 0,
    filteredHotels: [],
    totalFilteredHotels: 0,
    filterCounts: [],
    hotelDetails: null,
    roomData: null,
    loading: false,
    filterLoading: false,
    filteredHotelsLoading: false,
    hotelDetailsLoading: false,
    roomLoading: false,
    error: null,
    filterError: null,
    filteredHotelsError: null,
    hotelDetailsError: null,
    roomError: null,
    priceRange: [1, 10000],
    blockedRoomData: null,
    blockedRoomLoading: false,
    blockedRoomError: null,
    bookingData: null,
    bookingLoading: false,
    bookingError: null,
    itineraryNoConfirmData: null,
    itineraryNoConfirmLoading: false,
    itineraryNoConfirmError: null,
    itineraryConfirmData: null,
    itineraryConfirmLoading: false,
    itineraryConfirmError: null,
  },
  reducers: {
    setPriceRange(state, action) {
      state.priceRange = action.payload;
    },
    clearHotels(state) {
      state.hotels = [];
      state.originalHotels = [];
      state.totalHotels = 0;
      state.filteredHotels = [];
      state.totalFilteredHotels = 0;
      state.filterCounts = [];
      state.hotelDetails = null;
      state.roomData = null;
      state.error = null;
      state.filterError = null;
      state.filteredHotelsError = null;
      state.hotelDetailsError = null;
      state.roomError = null;
      state.bookingData = null;
      state.itineraryNoConfirmData = null;
      state.itineraryConfirmData = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // fetchHotelIdsByCity
      .addCase(fetchHotelIdsByCity.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchHotelIdsByCity.fulfilled, (state, action) => {
        state.loading = false;
        state.hotels = action.payload.hotels;
        state.originalHotels = action.payload.hotels;
        state.totalHotels = action.payload.totalHotels;
        state.filteredHotels = action.payload.hotels;
        state.totalFilteredHotels = action.payload.totalHotels;
      })
      .addCase(fetchHotelIdsByCity.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload;
        state.hotels = [];
        state.originalHotels = [];
        state.filteredHotels = [];
      })
      // fetchFilterCounts
      .addCase(fetchFilterCounts.pending, (state) => {
        state.filterLoading = true;
        state.filterError = null;
      })
      .addCase(fetchFilterCounts.fulfilled, (state, action) => {
        state.filterLoading = false;
        state.filterCounts = action.payload;
      })
      .addCase(fetchFilterCounts.rejected, (state, action) => {
        state.filterLoading = false;
        state.filterError = action.payload;
      })
      // fetchFilteredHotels
      .addCase(fetchFilteredHotels.pending, (state) => {
        state.filteredHotelsLoading = true;
        state.filteredHotelsError = null;
      })
      .addCase(fetchFilteredHotels.fulfilled, (state, action) => {
        state.filteredHotelsLoading = false;
        state.filteredHotels = action.payload.filteredHotels;
        state.totalFilteredHotels = action.payload.totalFilteredHotels;
      })
      .addCase(fetchFilteredHotels.rejected, (state, action) => {
        state.filteredHotelsLoading = false;
        state.filteredHotelsError = action.payload;
        state.filteredHotels = [];
      })
      // fetchHotelDetails
      .addCase(fetchHotelDetails.pending, (state) => {
        state.hotelDetailsLoading = true;
        state.hotelDetailsError = null;
      })
      .addCase(fetchHotelDetails.fulfilled, (state, action) => {
        state.hotelDetailsLoading = false;
        state.hotelDetails = action.payload;
      })
      .addCase(fetchHotelDetails.rejected, (state, action) => {
        state.hotelDetailsLoading = false;
        state.hotelDetailsError = action.payload;
      })
      // fetchRoomAvailability
      .addCase(fetchRoomAvailability.pending, (state) => {
        state.roomLoading = true;
        state.roomError = null;
      })
      .addCase(fetchRoomAvailability.fulfilled, (state, action) => {
        state.roomLoading = false;
        state.roomData = action.payload;
      })
      .addCase(fetchRoomAvailability.rejected, (state, action) => {
        state.roomLoading = false;
        state.roomError = action.payload;
      })
      // Fetch getRoomsWithBlocking
      .addCase(getRoomsWithBlocking.pending, (state) => {
        state.blockedRoomLoading = true;
        state.blockedRoomError = null;
      })
      .addCase(getRoomsWithBlocking.fulfilled, (state, action) => {
        state.blockedRoomLoading = false;
        state.blockedRoomData = action.payload;
      })
      .addCase(getRoomsWithBlocking.rejected, (state, action) => {
        state.blockedRoomLoading = false;
        state.blockedRoomError = action.payload;
      })
      // saveBooking
      .addCase(saveBooking.pending, (state) => {
        state.bookingLoading = true;
        state.bookingError = null;
      })
      .addCase(saveBooking.fulfilled, (state, action) => {
        state.bookingLoading = false;
        state.bookingData = action.payload;
      })
      .addCase(saveBooking.rejected, (state, action) => {
        state.bookingLoading = false;
        state.bookingError = action.payload;
      })
      // bookItineraryWithConfirmNo
      .addCase(bookItineraryWithConfirmNo.pending, (state) => {
        state.itineraryNoConfirmLoading = true;
        state.itineraryNoConfirmError = null;
      })
      .addCase(bookItineraryWithConfirmNo.fulfilled, (state, action) => {
        state.itineraryNoConfirmLoading = false;
        state.itineraryNoConfirmData = action.payload;
      })
      .addCase(bookItineraryWithConfirmNo.rejected, (state, action) => {
        state.itineraryNoConfirmLoading = false;
        state.itineraryNoConfirmError = action.payload;
      })
      // bookItineraryWithConfirmYes
      .addCase(bookItineraryWithConfirmYes.pending, (state) => {
        state.itineraryConfirmLoading = true;
        state.itineraryConfirmError = null;
      })
      .addCase(bookItineraryWithConfirmYes.fulfilled, (state, action) => {
        state.itineraryConfirmLoading = false;
        state.itineraryConfirmData = action.payload;
      })
      .addCase(bookItineraryWithConfirmYes.rejected, (state, action) => {
        state.itineraryConfirmLoading = false;
        state.itineraryConfirmError = action.payload;
      })
      
  },
});

export const { setPriceRange, clearHotels } = hotelSlice.actions;
export default hotelSlice.reducer;