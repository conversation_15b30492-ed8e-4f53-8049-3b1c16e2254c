import React from "react";
import SliderSection from "../SliderSection";
import DropDownSection from "./DropDownSection";
import ClimateDropdown from "./ClimateDropdown";
import TemperatureChart from "./TemperatureChart";
import LocationPage from "./Location";
import { ChevronRight } from "lucide-react";


const temperatureData = [
  { month: "Jan", value: 28 },
  { month: "Feb", value: 26 },
  { month: "Mar", value: 26 },
  { month: "Apr", value: 27 },
  { month: "May", value: 26 },
  { month: "Jun", value: 28 },
  { month: "Jul", value: 27 },
  { month: "Aug", value: 26 },
  { month: "Sep", value: 25 },
  { month: "Oct", value: 24 },
  { month: "Nov", value: 23 },
  { month: "Dec", value: 22 },
];

const sunshineData = [
  { month: "Jan", value: 28 },
  { month: "Feb", value: 26 },
  { month: "Mar", value: 26 },
  { month: "Apr", value: 27 },
  { month: "May", value: 26 },
  { month: "Jun", value: 28 },
  { month: "Jul", value: 27 },
  { month: "Aug", value: 26 },
  { month: "Sep", value: 25 },
  { month: "Oct", value: 24 },
  { month: "Nov", value: 23 },
  { month: "Dec", value: 22 },
];

const waterTempData = [
  { month: "Jan", value: 22 },
  { month: "Feb", value: 23 },
  { month: "Mar", value: 24 },
  { month: "Apr", value: 25 },
  { month: "May", value: 26 },
  { month: "Jun", value: 27 },
  { month: "Jul", value: 28 },
  { month: "Aug", value: 28 },
  { month: "Sep", value: 27 },
  { month: "Oct", value: 26 },
  { month: "Nov", value: 24 },
  { month: "Dec", value: 23 },
];

const rainyDaysData = [
  { month: "Jan", value: 10 },
  { month: "Feb", value: 8 },
  { month: "Mar", value: 9 },
  { month: "Apr", value: 11 },
  { month: "May", value: 12 },
  { month: "Jun", value: 15 },
  { month: "Jul", value: 17 },
  { month: "Aug", value: 16 },
  { month: "Sep", value: 14 },
  { month: "Oct", value: 12 },
  { month: "Nov", value: 10 },
  { month: "Dec", value: 9 },
];

const questions = [
  "Do they serve breakfast?",
  "Can I park there?",
  "Is the swimming pool open?",
  "Is the spa open?",
  "Is the restaurant open?",
  "What is the WiFi policy?",
  "Are there rooms with private bathroom?",
  "Are the provided bathroom accelerates and bath soap?",
  "What time is check-in and check-out?",
  "Is there a gym?",
  "Is there a bar?",
];

const ClimateRegion = () => {
  return (
    <div className="w-full  flex flex-col space-y-10 md:space-y-8 px-6 py-3 md:px-0 md:py-0">
      <h2 className="text-2xl text-darkBlue font-semibold">Climate And Region  </h2>
      {/* Slider Section */}
      {/* <div className="w-full h-64 md:h-96">
        <SliderSection />
      </div> */}
      <div className="flex flex-col lg:flex-row lg:space-x-10 text-smokyGray text-sm">
        {/* pharagraph section */}
        <div className="flex flex-col space-y-6  lg:w-4/6 text-left">
          {/* <div className="flex flex-col space-y-4">
            <p className="font-medium">
              Kuredu Island is one of the largest islands and offers a generous
              infrastructure with a great range of sports. Both those seeking
              peace and quiet and active guests feel at home here.
            </p>
          </div> */}
          {/* <div className="flex flex-col space-y-4">
            <h6 className="font-medium">Lhaviyani Atoll</h6>
            <p className="">
              The Lhaviyani Atoll is a paradise for diving fans, underwater
              photographers and water sports enthusiasts as well as for those
              seeking active relaxation. The islands in the atoll all have
              wonderful white sandy beaches, which are picturesquely framed by
              palm trees. And the resorts offer their guests enough variety and
              entertainment, so that apart from doing nothing, boredom is
              guaranteed. What could be nicer than losing yourself on a deserted
              beach during the day? The Lhaviyani Atoll is about 130 kilometers
              north of Male and, with a length of 18 kilometers and a width of
              35 kilometers, is one of the smallest atolls in the Maldives.
              Around 2,500 people live on the main island of Hinnavaru, around
              3,000 people on the island of Naifaru, the seat of the atoll
              chief, and the remaining 2,500 inhabitants are spread across the
              other larger islands. There is a large tuna canning factory on
              Felivaru, which makes a significant contribution to the Maldives'
              economy. Around 100,000 tons of tuna are processed here every
              year. The fish is still caught in the traditional way with fishing
              rods and large-scale net fishing is deliberately avoided in order
              not to endanger dolphins and turtles. Five of the 44 islands of
              the Lhaviyani Atoll were opened to tourism a few years ago and 3
              of them are in operation as holiday resorts. There are numerous
              impressive dive sites in the Lhaviyani Atoll, ranging from
              pristine coral reefs to exciting channels. Here you can dive,
              snorkel and explore the magnificent underwater world in all its
              facets to your heart's content. At the Shipyard, you can dive on
              two shipwrecks covered in coral during one dive. In the southeast
              of the atoll, the water flows less freely, which is why there is
              hardly any destructive wave activity there and the coral banks
              shine untouched in a unique splendor of colors. The channels in
              Lhaviyani are deeper than in other atolls and are therefore ideal
              for drift diving.
            </p>
          </div> */}
          <div className="flex flex-col space-y-4">
            <h6 className="font-medium">Maldives</h6>
            <p className=" font-light">
            The Maldives are small, paradisiacal islands with snow-white sandy beaches and deep blue lagoons. The atolls are rightly considered the ultimate dream destination for ambitious divers, as the reefs are among the most beautiful in the world. The picturesque islands are widely popular because wishes come true here:
            </p>
          </div>
          {/* <div className="flex flex-col space-y-4">
            <h6 className="font-medium">Accommodation</h6>
            <p className="">
              The Maldives are small, paradisiacal islands with snow-white sandy
              beaches and deep blue lagoons. The atolls are rightly considered
              the ultimate dream destination for ambitious divers, as the reefs
              are among the most beautiful in the world. The picturesque islands
              are widely popular because wishes come true here: whether relaxing
              in the shade of a palm tree on the beach or being active in and
              under the water - the Maldives will make your dream of an island
              come true! The Maldives are unique in every respect and cannot be
              compared to any other destination. This applies to both the
              holiday resorts and the underwater world with its indescribable
              abundance of fish. The archipelago looks like a necklace of pearls
              on the blue velvet of the sea when approaching Male. There are
              countless islands and islets scattered in the sea, some of them
              inhabited, but most of them still completely untouched by
              civilisation. The incomparably fascinating Indian Ocean offers
              everything a diver could wish for, from large fish to exciting
              drift dives. Many return again and again to this underwater
              paradise. The countless channels and passages between the atolls
              attract large fish in abundance, which feast on the feast swimming
              past in the current. In the lagoons of the atolls there are many
              rocky peaks that reach below the water surface and where
              snorkelers can also admire wonderful coral formations. And in the
              open sea, eagle rays, sharks and occasionally even a mighty whale
              shark can be found. The Maldives offer the best conditions for
              learning the fascinating sport of diving, because nowhere else is
              the sea clearer, the water temperature more pleasant and the fish
              world more colorful. The Maldives are also known worldwide for
              their wonderful beaches, which are typical of this tropical idyll.
              Windsurfers, surfers, sailors, fishermen and other water sports
              fans are also among the enthusiastic guests and increasingly
              people seeking relaxation are coming here who want to spend quiet
              holidays away from the hustle and bustle of everyday life.
            </p>
          </div> */}
          <div className="flex flex-col lg:flex-row space-x-10">
            <h6 className="font-medium">
              destinationInfo.travelPeriods.heading
            </h6>
            <p className="">November - April</p>
          </div>
          <div className="flex flex-col space-y-2">
            <div className="flex flex-col lg:flex-row space-x-14">
              <h6 className="font-medium">
                destinationInfo.flightTime.heading
              </h6>
              <p className="">9.5 Hours</p>
            </div>
            <p className="">2430 hours of sunshine per year</p>
          </div>
        </div>
        {/* dropdown section */}
        {/* <div className="flex flex-col w-full lg:w-2/6 ">
          <ClimateDropdown />
        </div> */}
      </div>
      <div className="flex flex-col space-y-8 text-smokyGray">
        <h6 className='font-medium'>Climate</h6>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-10 ">
          <TemperatureChart title="Average daily maximum temperature" data={temperatureData} />
          <TemperatureChart title="Average hour of sunshine per day" data={sunshineData} color="#FFF1AC" yDomain={[0, 32]} yTicks={[0, 4, 8, 12, 16]} />
          <TemperatureChart title="Average water temperature" data={waterTempData} color="#ACDCFF" yTicks={[0, 8, 16, 24, 32]} />
          <TemperatureChart title="Average rainy days per month" data={rainyDaysData} color="#B3ACFF" yDomain={[0, 16]} yTicks={[0, 4, 8, 12, 16]} />
        </div>
      </div>
      <div className="w-full h-full flex flex-col space-y-4">
        <h2 className="text-2xl text-darkBlue font-semibold">Travelers are asking</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-y-4 md:gap-x-4">
          <div className="grid grid-cols-1 md:grid-cols-2  gap-3 col-span-2">
            {questions.map((q, idx) => (
              <div
                key={idx}
                className="flex justify-between items-center px-4 py-3 border border-border rounded-[2px] text-sm hover:bg-gray-50 transition"
              >
                <span>{q}</span>
                <ChevronRight className="w-4 h-4 text-smokyGray" />
              </div>
            ))}
          </div>
          <div className="w-full h-full border border-border rounded-[2px] p-6 text-center flex flex-col items-center justify-center space-y-3">
            <h4 className="text-md font-semibold text-darkBlue">Still need more info?</h4>
            <button className="px-4 py-2 border border-darkBlue text-darkBlue text-sm rounded hover:bg-blue-50 transition">
              Ask Question
            </button>
            <p className="text-sm">We have an instant answer to most questions</p>
          </div>
          </div>
          <div className="">
          <button className="mt-4 w-fit px-4 py-2 border border-darkBlue text-darkBlue text-sm rounded hover:bg-blue-50 transition">
              see other question (33)
            </button>
            </div>
      </div>
    </div>
  );
};

export default ClimateRegion;
