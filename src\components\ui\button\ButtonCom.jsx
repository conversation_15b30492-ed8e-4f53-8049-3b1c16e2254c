import React, { forwardRef } from 'react';
import PropTypes from 'prop-types';
import { ChevronRight, Search } from 'lucide-react';

const ButtonCom = forwardRef(({
  children,
  variant = 'primary',
  size = 'md',
  width = 'auto',
  rounded = 'md',
  icon = null,
  iconPosition = 'right',
  disabled = false,
  className = '',
  ariaLabel,
  onClick,
  type = 'button',
  ...props
}, ref) => {
  const baseStyles =
    'flex items-center justify-center gap-2 transition duration-200 focus:outline-none font-medium';

  const variantStyles = {
    primary: `bg-darkBlue text-white hover:bg-blue-900`,
    secondary: `bg-orange text-white hover:bg-[#FF9000]`,
    outline: `border border-darkBlue text-darkBlue hover:bg-blue-50`,
    ghost: `bg-transparent text-darkBlue `,
    muted: `bg-buttoncolor text-white hover:bg-orange`,
  };

  const sizeStyles = {
    sm: 'text-xs sm:text-sm px-3 py-2',
    md: 'text-sm md:text-base px-5 py-3',
    lg: 'text-lg md:text-xl px-6 py-3',
    xl: 'text-lg md:text-2xl px-6 py-4',
  };

  const widthStyles = {
    full: 'w-full',
    auto: 'w-max',
    fixed: 'w-[90%] sm:w-[312px]',
  };

  const roundedStyles = {
    sm: 'rounded-md',
    md: 'rounded-[8px]',
    lg: 'rounded-2xl',
    full: 'rounded-full',
  };

  const IconComponent =
    icon === 'search' ? Search : icon === 'chevronRight' ? ChevronRight : null;

  return (
    <button
      ref={ref}  
      type={type}
      className={`
        ${baseStyles}
        ${variantStyles[variant] || ''}
        ${sizeStyles[size] || ''}
        ${widthStyles[width] || ''}
        ${roundedStyles[rounded] || ''}
        ${disabled ? 'opacity-60 cursor-not-allowed' : ''}
        ${className}
      `}
      disabled={disabled}
      onClick={onClick}
      aria-label={ariaLabel || children}
      {...props}
    >
      {icon && iconPosition === 'left' && IconComponent && (
        <IconComponent className="h-5 w-5" strokeWidth={1.7} />
      )}
      <span>{children}</span>
      {icon && iconPosition === 'right' && IconComponent && (
        <IconComponent className="h-5 w-5" strokeWidth={1.7} />
      )}
    </button>
  );
});

ButtonCom.displayName = 'ButtonCom';

ButtonCom.propTypes = {
  children: PropTypes.node.isRequired,
  variant: PropTypes.oneOf(['primary', 'secondary', 'outline', 'ghost', 'muted']),
  size: PropTypes.oneOf(['sm', 'md', 'lg', 'xl']),
  width: PropTypes.oneOf(['full', 'auto', 'fixed']),
  rounded: PropTypes.oneOf(['sm', 'md', 'lg', 'full']),
  icon: PropTypes.oneOf(['search', 'chevronRight', null]),
  iconPosition: PropTypes.oneOf(['left', 'right']),
  disabled: PropTypes.bool,
  className: PropTypes.string,
  ariaLabel: PropTypes.string,
  onClick: PropTypes.func,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
};

export default ButtonCom;
