import React, { useRef } from "react";
import { airlines } from "./AirlinesData";
import { ChevronLeft, ChevronRight } from "lucide-react";

const FlightList = ({ data, selectedAirline, setSelectedAirline }) => {
  const scrollContainerRef = useRef(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: -150, behavior: "smooth" });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({ left: 150, behavior: "smooth" });
    }
  };

  const calculateLowestPricesAndDates = () => {
    const airlineData = {};

    data?.forEach((tarif) => {
      const price = tarif.tarifPriceDetails?.total_sell_Price;
      // Get the outbound departure date from the first flight option
      const departureDate = tarif.fareDTOList[0].flightDTOS[0]?.first_departure_date;

      // Check both outbound and return airlines
      tarif.fareDTOList.forEach((fare) => {
        const airlineCode = fare.airline;
        if (airlineCode && price && departureDate) {
          if (!airlineData[airlineCode] || price < airlineData[airlineCode].price) {
            // If this is the lowest price so far, update both price and date
            airlineData[airlineCode] = { price, departureDate };
          } else if (price === airlineData[airlineCode].price) {
            // If the price is the same, choose the earlier departure date
            const currentDate = new Date(airlineData[airlineCode].departureDate);
            const newDate = new Date(departureDate);
            if (newDate < currentDate) {
              airlineData[airlineCode].departureDate = departureDate;
            }
          }
        }
      });
    });

    return airlineData;
  };

  const lowestPricesAndDates = calculateLowestPricesAndDates();
  const airlineCodes = Object.keys(lowestPricesAndDates);

  if (airlineCodes.length === 0) return null;

  const airlineList = airlineCodes.map((code) => ({
    ...(airlines[code] || airlines.default),
    price: lowestPricesAndDates[code].price,
    departureDate: lowestPricesAndDates[code].departureDate,
  }));

  // Prepend "All Airlines" item (no price or date needed)
  const displayList = [{ code: "all", name: "All Airlines", textOnly: true }, ...airlineList];

  return (
    <div className="relative flex items-center justify-center">
      <div className="relative w-full md:max-w-[1100px] rounded-lg md:rounded-2xl border border-border">
        <div ref={scrollContainerRef} className="overflow-x-auto scrollbar-hide">
          <table className="w-full">
            <thead>
              <tr>
                {displayList.map((airline, index) => (
                  <th
                    key={airline.code}
                    className={`p-2 cursor-pointer min-w-[200px] relative 
                      ${selectedAirline === airline.code ? "bg-blue-100" : ""} 
                      ${index !== displayList.length - 1 ? "after:absolute after:right-0 after:top-[30%] after:h-[100%] after:w-[1px] after:bg-border" : ""}`}
                    onClick={() =>
                      setSelectedAirline(airline.code === "all" ? null : airline.code)
                    }
                  >
                    <div className="flex flex-col items-center">
                      {airline.textOnly ? (
                        <span className="text-base text-darkBlue font-semibold mt-9 -mx-4">
                          {airline.name}
                        </span>
                      ) : (
                        <div className="w-16 h-16 border border-border rounded-2xl p-1 mt-3 flex items-center justify-center">
                          <img
                            src={airline.logo}
                            alt={airline.name}
                            className="w-full h-full object-contain p-2"
                          />
                        </div>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody>
              {/* Row for Departure Dates */}
              <tr>
                {displayList.map((airline, index) => (
                  <td
                    key={`${airline.code}-date`}
                    className={`text-center min-w-[200px] ${index !== displayList.length - 1 ? "" : ""}`}
                  >
                    {airline.code !== "all" && airline.departureDate && (
                      <span className="text-xs text-smokyGray">
                        {new Date(airline.departureDate).toLocaleDateString("en-GB", {
                          weekday: "short",
                          day: "2-digit",
                          month: "short",
                          year: "numeric",
                        }).replace(",", " |")}
                      </span>
                    )}
                  </td>
                ))}
              </tr>
              {/* Row for Prices */}
              <tr>
                {displayList.map((airline, index) => (
                  <td
                    key={`${airline.code}-price`}
                    className={`text-center pb-2 min-w-[200px] ${index !== displayList.length - 1 ? "" : ""}`}
                  >
                    {airline.code !== "all" && (
                      <span className="font-medium text-xs text-smokyGray">
                        CHF {airline.price.toFixed(2)}
                      </span>
                    )}
                  </td>
                ))}
              </tr>
              
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default FlightList;