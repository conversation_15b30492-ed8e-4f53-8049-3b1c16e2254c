import React, { useEffect, useRef, useState } from "react";
import { Formik } from "formik";
import { useNavigate, useLocation } from "react-router-dom";
import * as Yup from "yup";
import FlightHotelForm from "./FlightHotelForm";
import FlightForm from "./FlightForm";
import HotelForm from "./HotelForm";
import TourForm from "./TourForm";
import { MdAirplanemodeActive } from "react-icons/md";
import { BedDouble, Navigation } from "lucide-react";

const ValidationSchema = Yup.object().shape({
  destination: Yup.string().required("Destination is required"),
  cityCode: Yup.string().required("Select valid destination "),
  rooms: Yup.number().min(1, "At least 1 room required").required("Required"),
  persons: Yup.number().min(1, "At least 1 person required").required("Required"),
});

const SearchForm = ({ initialData = {} }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const calendarRef = useRef(null);
  const formContainerRef = useRef(null);

  const isHomePage = location.pathname === "/";
  const initialTab = location.state?.selectedTab || 0;
  const initialFormData = location.state?.formData || {
    ...initialData,
    departure: initialData?.dep_apt,
    destination: initialData?.des_apt,
  };

  const [searchStatusVal, setSearchStatusVal] = useState(initialTab);
  const [dateRange, setDateRange] = useState({
    startDate: initialData.dateRange?.startDate
      ? new Date(initialData.dateRange.startDate)
      : new Date(),
    endDate: initialData.dateRange?.endDate
      ? new Date(initialData.dateRange.endDate)
      : new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    key: "selection",
  });

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target)
      ) {
        console.log("Clicked outside calendar");
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  const handleTabClick = (index) => {
    setSearchStatusVal(index);
    console.log("Tab changed to:", index);
    // if (formContainerRef.current) {
    //   const targetOffset = formContainerRef.current.offsetTop - 120;
    //   window.scrollTo({ top: targetOffset, behavior: "smooth" });
    // }
    const routes = ["/", "/", "/", "/tour"];
    navigate(routes[index] || "/", {
      state: { selectedTab: index, formData: initialFormData },
    });
  };

  const searchTabs = [
    {
      component: (
        <div className="flex items-center text-base space-x-1 lg:space-x-4">
          <MdAirplanemodeActive className="h-5 w-5 rotate-90" />
          <span>Flight</span>
          <span>+</span>
          <BedDouble className="h-5 w-5" />
          <span>Hotel</span>
        </div>
      ),
    },
    {
      component: (
        <div className="flex items-center text-base space-x-1 lg:space-x-4">
          <MdAirplanemodeActive className="h-5 w-5 rotate-90" />
          <span>Flight</span>
        </div>
      ),
    },
    {
      component: (
        <div className="flex items-center text-base space-x-1 lg:space-x-4">
          <BedDouble className="h-5 w-5" />
          <span>Hotel</span>
        </div>
      ),
    },
    {
      component: (
        <div className="flex items-center text-base space-x-1 lg:space-x-4">
          <Navigation className="h-5 w-5" />
          <span>Tour</span>
        </div>
      ),
    },
  ];

  const formComponents = [FlightHotelForm, FlightForm, HotelForm, TourForm];
  const FormComponent = formComponents[searchStatusVal] || FlightForm;

  return (
    <div
      ref={formContainerRef}
      className="absolute top-[90px] left-0 w-full z-[60] flex justify-center "
    >
      <div
        className={`w-full max-w-[80%] bg-white rounded-[8px] border border-border drop-shadow-md px-6 pb-6 space-y-8 ml-4 ${isHomePage ? "bg-opacity-50" : "bg-opacity-100"
          }`}
      >

        <div className="flex flex-col items-center w-full p-0 h-[67px]">
          <div className="flex w-full lg:mx-0 md:w-[104.4%] md:-mt-6 rounded-t-[8px] lg:border border-border bg-white h-16 items-center relative overflow-x-auto md:overflow-hidden">
            {searchTabs.map((item, index) => (
              <React.Fragment key={index}>
                <div
                  onClick={() => handleTabClick(index)}
                  className={`flex-1 min-w-[100px] h-full flex items-center border-r border-border  justify-center cursor-pointer transition-all ${searchStatusVal === index
                    ? "text-white px-1 py-4 bg-orange "
                    : "hover:text-primaryColor"
                    }`}
                >
                  {item.component}
                </div>

              </React.Fragment>
            ))}
          </div>
        </div>
        <Formik
          initialValues={{
            destination: initialFormData.destination || initialFormData.des_apt || "",
            cityCode: initialFormData.cityCode || null,
            departure: initialFormData.departure || initialFormData.dep_apt || "",
            rooms: initialFormData.rooms || 1,
            adults: initialFormData.adults || 2,
            children: initialFormData.children || 0,
            persons: (initialFormData.adults || 2) + (initialFormData.children || 0),
          }}
          validationSchema={ValidationSchema}
          onSubmit={(values) => {
            if (!values.cityCode) {
              alert("Please select a valid destination from the suggestions.");
              return;
            }
            const formData = {
              ...values,
              dateRange: {
                startDate: dateRange.startDate,
                endDate: dateRange.endDate,
              },
            };
            navigate("/TrendingPackage", {
              state: { selectedTab: searchStatusVal, formData },
            });
          }}
        >
          {({ values, setFieldValue, errors, touched }) => (
            <FormComponent
              dateRange={dateRange}
              setDateRange={setDateRange}
              values={values}
              errors={errors}
              touched={touched}
              setFieldValue={setFieldValue}
            />
          )}
        </Formik>
      </div>
    </div>
  );
};

export default SearchForm;