import React from 'react';
import { FaCircleCheck, FaHotel, FaBath, FaPersonRunning, FaSpa, FaBaby, FaCarSide, FaCar, FaUserDoctor, FaChargingStation, FaTruckRampBox, FaCampground } from 'react-icons/fa6';
import { GiClubs, GiWaterPolo, GiCoffeeCup, GiLift, GiLockedChest, GiVideoConference, GiVacuumCleaner } from 'react-icons/gi';
import { TbBeach, TbBuildingPavilion, TbAirConditioning, TbCarGarage, TbDevicesCheck, TbMoon } from 'react-icons/tb';
import { LiaSwimmingPoolSolid, LiaTableTennisSolid, LiaCcDinersClub, LiaShuttleVanSolid, LiaPeopleCarrySolid } from 'react-icons/lia';
import { <PERSON>r<PERSON><PERSON>, <PERSON>r<PERSON><PERSON><PERSON> } from 'react-icons/gr';
import { RiDrinks2Line, RiScissorsLine, Ri24HoursFill } from 'react-icons/ri';
import { LuFileVideo2, LuRefrigerator, LuCircleParking, LuShip } from 'react-icons/lu';
import { CiWifiOn, CiParking1 } from 'react-icons/ci';
import { IoGolfOutline, IoRestaurantOutline, IoLibraryOutline } from 'react-icons/io5';
import { BsCurrencyExchange, BsPersonHeart, BsReception1 } from 'react-icons/bs';
import { TiFlowChildren } from 'react-icons/ti';
import { FaStarOfDavid } from 'react-icons/fa6';
import { MdOutlineInterpreterMode, MdOutlineGolfCourse, MdSportsGymnastics, MdOutlineScubaDiving, MdOutlineSnowboarding, MdPool, MdBusiness, MdLocalAtm, MdOutlineCarRental, MdOutlineCasino, MdOutlineLocalPharmacy, MdLocalLaundryService, MdOutlineRoomService, MdPets, MdWheelchairPickup, MdMeetingRoom, MdLocalPostOffice, MdEventAvailable, MdOutlineSportsTennis, MdHotTub, MdOutlineBlock, MdOutlineMuseum, MdAirportShuttle } from 'react-icons/md';
import { SiCcleaner } from 'react-icons/si';
import { PiParkDuotone, PiAirplaneTaxiing, PiBathtubDuotone, PiBicycleLight, PiVideoConferenceLight, PiHairDryerLight } from 'react-icons/pi';
import { Gamepad2, Dumbbell, Waves, Tv, ConciergeBell, WineOff, FileVideo2 as LucideFileVideo2, CircleCheckBig, BadgeDollarSign, EarOff, HandPlatter, PawPrint } from 'lucide-react';

const amenityIconMap = {
  "arcade / game room": { icon: Gamepad2, source: "lucide" },
  "bbq/picnic area": { icon: PiParkDuotone, source: "react-icons" },
  "fitness classes": { icon: PiAirplaneTaxiing, source: "react-icons" },
  "golf course": { icon: MdOutlineGolfCourse, source: "react-icons" },
  "gymnasium": { icon: Dumbbell, source: "lucide" },
  "jacuzzi": { icon: FaBath, source: "react-icons" },
  "jogging track": { icon: FaPersonRunning, source: "react-icons" },
  "kids club": { icon: GiClubs, source: "react-icons" },
  "kids play ground": { icon: FaCampground, source: "react-icons" },
  "kids pool": { icon: Waves, source: "lucide" },
  "live entertainment": { icon: Tv, source: "lucide" },
  "on-site beach": { icon: TbBeach, source: "react-icons" },
  "personal trainer available": { icon: MdEventAvailable, source: "react-icons" },
  "poolside snack bar": { icon: RiDrinks2Line, source: "react-icons" },
  "sauna": { icon: PiBathtubDuotone, source: "react-icons" },
  "scuba diving": { icon: MdOutlineScubaDiving, source: "react-icons" },
  "snow sports": { icon: MdOutlineSnowboarding, source: "react-icons" },
  "spa": { icon: FaSpa, source: "react-icons" },
  "squash court": { icon: MdOutlineSportsTennis, source: "react-icons" },
  "steam room": { icon: MdHotTub, source: "react-icons" },
  "swimming pool - indoor": { icon: LiaSwimmingPoolSolid, source: "react-icons" },
  "swimming pool - outdoor": { icon: MdPool, source: "react-icons" },
  "teen club": { icon: LiaCcDinersClub, source: "react-icons" },
  "tennis courts": { icon: LiaTableTennisSolid, source: "react-icons" },
  "water sports": { icon: GiWaterPolo, source: "react-icons" },
  "av equipment available": { icon: TbDevicesCheck, source: "react-icons" },
  "conference rooms": { icon: PiVideoConferenceLight, source: "react-icons" },
  "lounge access for club/executive rooms": { icon: GrLounge, source: "react-icons" },
  "meeting rooms": { icon: MdMeetingRoom, source: "react-icons" },
  "post/courier services": { icon: MdLocalPostOffice, source: "react-icons" },
  "video conference": { icon: GiVideoConference, source: "react-icons" },
  "24-hour food and beverage outlet": { icon: Ri24HoursFill, source: "react-icons" },
  "adjoining rooms": { icon: FaHotel, source: "react-icons" },
  "air conditioning": { icon: TbAirConditioning, source: "react-icons" },
  "airport shuttle - free": { icon: MdAirportShuttle, source: "react-icons" },
  "airport shuttle available (surcharge applies)": { icon: LiaShuttleVanSolid, source: "react-icons" },
  "atm machine": { icon: MdLocalAtm, source: "react-icons" },
  "baby sitting": { icon: FaBaby, source: "react-icons" },
  "banquet hall": { icon: TbBuildingPavilion, source: "react-icons" },
  "bar": { icon: GrBar, source: "react-icons" },
  "barber shop": { icon: RiScissorsLine, source: "react-icons" },
  "beauty salon": { icon: PiHairDryerLight, source: "react-icons" },
  "bicycle rentals": { icon: PiBicycleLight, source: "react-icons" },
  "business centre": { icon: MdBusiness, source: "react-icons" },
  "car parking - onsite free": { icon: CiParking1, source: "react-icons" },
  "car parking - onsite paid": { icon: TbCarGarage, source: "react-icons" },
  "car rental desk": { icon: MdOutlineCarRental, source: "react-icons" },
  "casino": { icon: MdOutlineCasino, source: "react-icons" },
  "children not allowed": { icon: MdOutlineBlock, source: "react-icons" },
  "children's nursery": { icon: TiFlowChildren, source: "react-icons" },
  "complimentary in-room coffee or tea": { icon: GiCoffeeCup, source: "react-icons" },
  "complimentary wifi access": { icon: CiWifiOn, source: "react-icons" },
  "concierge": { icon: ConciergeBell, source: "lucide" },
  "doctor on call": { icon: FaUserDoctor, source: "react-icons" },
  "driving range": { icon: IoGolfOutline, source: "react-icons" },
  "drugstore/pharmacy": { icon: MdOutlineLocalPharmacy, source: "react-icons" },
  "dry hotel – no alcohol served": { icon: WineOff, source: "lucide" },
  "dvd/video rental": { icon: LucideFileVideo2, source: "lucide" },
  "elevators": { icon: GiLift, source: "react-icons" },
  "ev charging station": { icon: FaChargingStation, source: "react-icons" },
  "exhibition/convention facilities": { icon: MdOutlineMuseum, source: "react-icons" },
  "express check-in": { icon: CircleCheckBig, source: "lucide" },
  "express check-out": { icon: CircleCheckBig, source: "lucide" },
  "foreign currency exchange": { icon: BadgeDollarSign, source: "lucide" },
  "free ice available": { icon: LuRefrigerator, source: "react-icons" },
  "halal food available": { icon: TbMoon, source: "react-icons" },
  "hearing impaired services": { icon: EarOff, source: "lucide" },
  "housekeeping-daily": { icon: GiVacuumCleaner, source: "react-icons" },
  "housekeeping-weekly": { icon: SiCcleaner, source: "react-icons" },
  "kosher food available": { icon: FaStarOfDavid, source: "react-icons" },
  "laundry service": { icon: MdLocalLaundryService, source: "react-icons" },
  "library": { icon: IoLibraryOutline, source: "react-icons" },
  "limited time reception": { icon: BsReception1, source: "react-icons" },
  "limousine service - paid": { icon: FaCarSide, source: "react-icons" },
  "local shuttle - free": { icon: MdAirportShuttle, source: "react-icons" },
  "long term parking": { icon: LuCircleParking, source: "react-icons" },
  "multilingual staff": { icon: MdOutlineInterpreterMode, source: "react-icons" },
  "pets allowed": { icon: PawPrint, source: "lucide" },
  "porterage": { icon: LiaPeopleCarrySolid, source: "react-icons" },
  "ramp access": { icon: FaTruckRampBox, source: "react-icons" },
  "restaurant": { icon: IoRestaurantOutline, source: "react-icons" },
  "room service - 24 hours": { icon: Ri24HoursFill, source: "react-icons" },
  "room service - limited hours": { icon: HandPlatter, source: "lucide" },
  "safety deposit box": { icon: GiLockedChest, source: "react-icons" },
  "tour desk": { icon: BsPersonHeart, source: "react-icons" },
  "valet parking": { icon: FaCar, source: "react-icons" },
  "water park": { icon: LuShip, source: "react-icons" },
  "wheelchair access": { icon: MdWheelchairPickup, source: "react-icons" },
  // Custom mappings
  "room": { icon: FaHotel, source: "react-icons" },
  "free wifi": { icon: CiWifiOn, source: "react-icons" },
  "pool with view": { icon: MdPool, source: "react-icons" },
  "balcony": { icon: FaHotel, source: "react-icons" },
  "mini bar": { icon: LuRefrigerator, source: "react-icons" },
  "soundproofing": { icon: EarOff, source: "lucide" },
  "roof top pool": { icon: MdPool, source: "react-icons" },
};

const FacilitiesIcon = ({ label, className = "w-4 h-4 text-darkBlue" }) => {
  const normalizedLabel = label.toLowerCase().trim();
  const iconInfo = amenityIconMap[normalizedLabel] || { icon: FaCircleCheck, source: "react-icons" };
  const IconComponent = iconInfo.icon;
  return <IconComponent className={className} />;
};

export default FacilitiesIcon;