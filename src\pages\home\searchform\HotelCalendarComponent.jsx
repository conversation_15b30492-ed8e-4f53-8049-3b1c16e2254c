import React, { useEffect, useRef, useState } from "react";
import Calendar from "react-calendar";
import "react-calendar/dist/Calendar.css";
import { ChevronLeft, ChevronRight, ChevronDown } from "lucide-react";
import "./CustomCalendar.css";

const HotelCalendarComponent = ({ dateRange, setDateRange, onClose, setFieldValue }) => {
  const calendarRef = useRef(null);
  const [flexibleDays, setFlexibleDays] = useState(0); 
  const [currentDate, setCurrentDate] = useState(dateRange.startDate || new Date());
  const [showYearDropdown, setShowYearDropdown] = useState(false);

  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const handleDateSelect = (value) => {
    if (Array.isArray(value)) {
      // Range selection
      const [start, end] = value;
      setDateRange({
        startDate: start,
        endDate: end,
        key: "selection",
      });
      onClose(); // Close calendar after range selection
    }
  };

  const handleMonthChange = (date) => {
    setCurrentDate(date);
  };

  const handleYearSelect = (year) => {
    const newDate = new Date(currentDate);
    newDate.setFullYear(year);
    setCurrentDate(newDate);
    setShowYearDropdown(false);
  };

  const customNavigation = () => {
    return (
      <div className="custom-navigation flex justify-between items-center mb-4">
        <div className="flex items-center justify-center space-x-4">
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const newDate = new Date(currentDate);
              newDate.setMonth(newDate.getMonth() - 1);
              setCurrentDate(newDate);
              handleMonthChange(newDate);
            }}
            className="p-1 hover:bg-gray-100 rounded-full transition duration-200"
          >
            <ChevronLeft size={20} className="text-[#6b6868]" />
          </button>
          <span className="text-base font-medium text-[#6b6868] w-24 text-center capitalize">
            {currentDate.toLocaleString("default", { month: "long" })}
          </span>
          <button
            type="button"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              const newDate = new Date(currentDate);
              newDate.setMonth(newDate.getMonth() + 1);
              setCurrentDate(newDate);
              handleMonthChange(newDate);
            }}
            className="p-1 hover:bg-gray-100 rounded-full transition duration-200"
          >
            <ChevronRight size={20} className="text-[#6b6868]" />
          </button>
        </div>
        <div className="relative">
          <div
            className="flex items-center cursor-pointer year-selector"
            onClick={() => setShowYearDropdown(!showYearDropdown)}
          >
            <span className="text-base font-normal mr-1 text-[#6b6868]">
              {currentDate.getFullYear()}
            </span>
            <ChevronDown size={18} className="text-[#6b6868]" />
          </div>
          {showYearDropdown && (
            <div className="year-dropdown absolute right-0 mt-1 bg-white border border-gray rounded-lg shadow-lg z-30 max-h-48 overflow-y-auto">
              {Array.from({ length: 100 }, (_, i) => {
                const year = new Date().getFullYear() - 2 + i;
                return (
                  <div
                    key={year}
                    className="px-4 py-2 hover:bg-gray cursor-pointer"
                    onClick={() => handleYearSelect(year)}
                  >
                    {year}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };

  const tileClassName = ({ date, view }) => {
    if (view !== "month") return null;

    const dateObj = new Date(date);
    dateObj.setHours(0, 0, 0, 0);

    const startDate = dateRange.startDate
      ? new Date(dateRange.startDate).setHours(0, 0, 0, 0)
      : null;
    const endDate = dateRange.endDate
      ? new Date(dateRange.endDate).setHours(0, 0, 0, 0)
      : null;

    if (startDate && dateObj.getTime() === startDate) {
      return "react-calendar__tile--rangeStart";
    }
    if (endDate && dateObj.getTime() === endDate) {
      return "react-calendar__tile--rangeEnd";
    }
    if (startDate && endDate && dateObj > startDate && dateObj < endDate) {
      return "react-calendar__tile--range";
    }
    if (dateObj.getTime() === today.getTime()) {
      return "react-calendar__tile--now";
    }
    return null;
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (calendarRef.current && !calendarRef.current.contains(event.target)) {
        onClose();
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, [onClose]);

  useEffect(() => {
    const handleClickOutsideDropdown = (event) => {
      if (
        showYearDropdown &&
        !event.target.closest(".year-dropdown") &&
        !event.target.closest(".year-selector")
      ) {
        setShowYearDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutsideDropdown);
    return () => document.removeEventListener("mousedown", handleClickOutsideDropdown);
  }, [showYearDropdown]);

  return (
    <div
      className="absolute w-full md:w-96 z-20 bg-white border border-border rounded-lg flex flex-col p-4 space-y-4 mt-2 -ml-2"
      ref={calendarRef}
    >
      {customNavigation()}
      <Calendar
        onChange={handleDateSelect}
        onClickDay={(value) => {
          setDateRange({ startDate: value, endDate: null });
        }}
        value={[dateRange.startDate, dateRange.endDate]}
        onActiveStartDateChange={({ activeStartDate }) =>
          handleMonthChange(activeStartDate)
        }
        activeStartDate={currentDate}
        selectRange={true}
        className="custom-calendar"
        showNavigation={false}
        minDate={today}
        formatShortWeekday={(locale, date) =>
          date.toLocaleDateString(locale, { weekday: "short" }).slice(0, 2)
        }
        tileClassName={tileClassName}
      />

      <div className="flex flex-col justify-between ">
        <div className="flex flex-col p-2 rounded-[2px] space-y-6">
          <div className="flex space-x-2 w-full justify-evenly">
            {[ 1, 2, 3].map((days) => (
              <label
                key={days}
                onClick={() => {
                  const newValue = flexibleDays === days ? 0 : days;
                  setFlexibleDays(newValue);
                  setFieldValue("flexibleDays", newValue);
                }}
                className={`cursor-pointer flex items-center space-x-1 px-4 py-1 rounded-[2px] 
                  shadow-[0_0_0_0.5px_#024575] 
                  ${flexibleDays === days ? "bg-darkBlue " : "bg-white text-smokyGray"}`}
              >
                {/* <input
                  type="radio"
                  name="flexibleDays"
                  value={days}
                  onChange={() => {
                    setFlexibleDays(days);
                    setFieldValue("flexibleDays", days); // Store in form values
                  }}
                  checked={flexibleDays === days}
                /> */}
                <span
                  className={`text-[10px]  ${flexibleDays === days ? "text-white" : "text-smokyGray"
                    }`}
                >
                  {days === 0 ? "0 day" : `± ${days} ${days === 1 ? "day" : "days"}`}
                </span>
              </label>
            ))}
          </div>
          <span className="text-sm font-semibold text-center mt-2 text-darkBlue">
            Flexible Dates
          </span>
        </div>
      </div>
    </div>
  );
};

export default HotelCalendarComponent;