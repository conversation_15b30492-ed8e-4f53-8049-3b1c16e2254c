/* Date Picker Custom Styles */
.dob-picker {
    width: 100%;
    border: none !important;
    padding: 0 !important;
    color: #6B7280;
    font-size: 16px;
    font-family: 'Inter', sans-serif;
  }
  
  .dob-picker .react-date-picker__wrapper {
    border: none !important;
    padding: 0 !important;
  }
  
  .dob-picker .react-date-picker__inputGroup {
    padding: 0 !important;
    min-width: 90%;
  }
  
  .dob-picker .react-date-picker__inputGroup__input {
    color: #6B7280;
    font-size: 16px;
    padding: 2px 0;
    height: 26px;
  }
  
  .dob-picker .react-date-picker__inputGroup__divider {
    color: #6B7280;
    padding: 0 2px;
  }
  
  .dob-picker .react-date-picker__button {
    padding: 0 4px;
    margin: 0;
  }
  
  .dob-picker .react-date-picker__button svg {
    stroke: #6B7280;
    width: 18px;
    height: 18px;
    margin-right: 24px;
  }
  
  /* Calendar styles */
  .dob-calendar {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: 1px solid #E5E7EB !important;
    overflow: hidden;
    width: 300px;
    font-family: 'Inter', sans-serif;
  }
  
  .dob-calendar .react-calendar__navigation {
    background-color: #F9FAFB;
    height: 48px;
    margin-bottom: 0;
  }
  
  .dob-calendar .react-calendar__navigation button {
    color: #374151;
    font-weight: 600;
    font-size: 16px;
  }
  
  .dob-calendar .react-calendar__navigation button:enabled:hover,
  .dob-calendar .react-calendar__navigation button:enabled:focus {
    background-color: #F3F4F6;
  }
  
  .dob-calendar .react-calendar__month-view__weekdays {
    background-color: #F9FAFB;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 12px;
    padding: 8px 0;
  }
  
  .dob-calendar .react-calendar__month-view__weekdays__weekday {
    color: #6B7280;
  }
  
  .dob-calendar .react-calendar__month-view__days__day {
    padding: 8px;
    color: #374151;
  }
  
  .dob-calendar .react-calendar__month-view__days__day--weekend {
    color: #4B5563;
  }
  
  .dob-calendar .react-calendar__month-view__days__day--neighboringMonth {
    color: #D1D5DB;
  }
  
  .dob-calendar .react-calendar__tile {
    font-size: 14px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .dob-calendar .react-calendar__tile:enabled:hover,
  .dob-calendar .react-calendar__tile:enabled:focus {
    background-color: #F3F4F6;
    border-radius: 8px;
  }
  
  .dob-calendar .react-calendar__tile--active {
    background-color: #2563EB !important;
    color: white !important;
    border-radius: 8px;
  }
  
  .dob-calendar .react-calendar__tile--now {
    background-color: #EFF6FF;
    border-radius: 8px;
  }
  
  .dob-calendar .react-calendar__year-view__months__month,
  .dob-calendar .react-calendar__decade-view__years__year,
  .dob-calendar .react-calendar__century-view__decades__decade {
    padding: 16px 8px;
  }
  
  /* Year and decade view styles */
  .dob-calendar .react-calendar__year-view .react-calendar__tile,
  .dob-calendar .react-calendar__decade-view .react-calendar__tile,
  .dob-calendar .react-calendar__century-view .react-calendar__tile {
    padding: 1em 0.5em;
  }