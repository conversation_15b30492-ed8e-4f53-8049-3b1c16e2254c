import { useState } from "react";
import axios from "axios";
import useSWR from "swr";

const API_URL = "http://192.168.1.5:9100/eflyer-bookings/api/v1/packagePricing";
const fetcher = (url) => axios.post(url).then((res) => res.data);

function Test() {
  const { data, error } = useSWR(API_URL, fetcher);

  if (error) return <div className="text-red-500 text-center">Failed to load data.</div>;
  if (!data) return <div className="text-gray-500 text-center">Loading...</div>;

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4 text-center">API Response</h1>
      <pre className="bg-gray-100 p-4 rounded shadow-md">{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
}

export default Test;
