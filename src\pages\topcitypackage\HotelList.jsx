import React from 'react';
import PackageCard from '../../components/ui/commonCard/PackageCard';
import HotelImage1 from '../../assets/adventure/HotelImage1.svg';
import HotelImage2 from '../../assets/adventure/HotelImage2.svg';
import HotelImage3 from '../../assets/adventure/HotelImage3.svg';
import Icon1 from '../../assets/packages/icon_1.svg';
import Icon2 from '../../assets/packages/icon_2.svg';
import Icon3 from '../../assets/packages/icon_3.svg';
import Icon4 from '../../assets/packages/icon_4.svg';
import Icon5 from '../../assets/packages/icon_5.svg';
import Icon6 from '../../assets/packages/icon_6.svg';
import Icon7 from '../../assets/packages/icon_7.svg';
import Icon8 from '../../assets/packages/icon_8.svg';
import Icon9 from '../../assets/packages/icon_9.svg';
import Icon10 from '../../assets/packages/icon_10.svg';

const iconsList = [
  [Icon1, Icon2, Icon3, Icon4, Icon5],
  [Icon6, Icon7, Icon8, Icon9, Icon10],
];

const classificationMapping = {
  559: 1,
  560: 2,
  561: 3,
  562: 4,
  563: 5,
};

const HotelList = ({ hotels, totalHotels, searchCriteria }) => {
  const formatDate = (date) => {
    return new Intl.DateTimeFormat('en-US', {
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    }).format(new Date(date));
  };

  const calculateNights = () => {
    try {
      const startDate = new Date(searchCriteria.dateRange?.startDate);
      const endDate = new Date(searchCriteria.dateRange?.endDate);
      if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
        return Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
      }
    } catch (error) {
      console.error('Error calculating nights:', error);
    }
    return 1;
  };

  const mappedHotels = hotels.map((hotel, index) => {
    const nights = calculateNights();
    const adults = searchCriteria.adults || 2;
    const children = searchCriteria.children || 0;
    const imageSet = [HotelImage1, HotelImage2, HotelImage3];

    return {
      id: hotel.hotelId,
      title: hotel.hotelName,
      location: `${hotel.cityData?.cityName || 'Unknown'}, ${hotel.cityData?.countryData?.countryName || 'Unknown'}`,
      dates: searchCriteria.dateRange
        ? `${formatDate(searchCriteria.dateRange.startDate)} - ${formatDate(searchCriteria.dateRange.endDate)}`
        : 'N/A',
      duration: `${nights} ${nights === 1 ? 'night' : 'nights'} - ${adults} ${adults === 1 ? 'Adult' : 'Adults'}${children > 0 ? `, ${children} ${children === 1 ? 'Child' : 'Children'}` : ''}`,
      package: `${hotel.cheapestRoomTypeName || 'Standard Room'} | Including Free Cancellation`,
      originalPrice: '', 
      discountedPrice: hotel.cheapestRoomCharge ? `CHF ${hotel.cheapestRoomCharge.toFixed(2)}` : 'N/A',
      discount: null, 
      image: hotel.thumbUrl || imageSet[index % 3],
      rating: classificationMapping[hotel.classificationCode] || 3,
      isTop: index % 2 === 0, 
      icon: iconsList[index % 2],
    };
  });

  return (
    <div className="w-full space-y-6">
      {/* <Header totalHotels={totalHotels} startingPrice={mappedHotels[0]?.discountedPrice || 'N/A'} /> */}
      <div className="grid grid-cols-1 sm:grid-cols-1 lg:grid-cols-1 gap-6">
        {mappedHotels.map((hotel) => (
          <PackageCard adventure={hotel} key={hotel.id} />
        ))}
      </div>
    </div>
  );
};

// const Header = ({ totalHotels, startingPrice }) => (
//   <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4">
//     <p className="text-gray-700 font-medium text-lg">
//       {totalHotels} Hotels From <span className="font-bold text-gray-900">{startingPrice}</span>
//     </p>
//     <SortDropdown />
//   </div>
// );

// const SortDropdown = () => (
//   <div className="flex flex-col items-start space-x-2 space-y-2">
//     <label htmlFor="sort" className="text-darkBlue text-sm">
//       Sort By
//     </label>
//     <select
//       id="sort"
//       className="border border-darkBlue rounded-full px-4 py-1 text-sm text-gray-700 focus:outline-none focus:ring"
//     >
//       <option>Most Popular</option>
//       <option>Price: Low to High</option>
//       <option>Price: High to Low</option>
//       <option>Top Rated</option>
//     </select>
//   </div>
// );

export default HotelList;