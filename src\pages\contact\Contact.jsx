import React from 'react';
import { Facebook, Twitter, Youtube, Instagram, Globe } from 'lucide-react';
import ContactImg from "../../assets/contact/contact.png";

const ContactPage = () => {
    const openingHours = [
        { day: 'Mon', hours: '9 : 00 AM - 11 : 00 PM' },
        { day: 'Tue', hours: '9 : 00 AM - 11 : 00 PM' },
        { day: 'Wed', hours: '9 : 00 AM - 11 : 00 PM' },
        { day: 'Thu', hours: '9 : 00 AM - 11 : 00 PM' },
        { day: 'Fri', hours: '9 : 00 AM - 11 : 00 PM' },
        { day: 'Sat', hours: '9 : 00 AM - 11 : 00 PM' },
        { day: 'Sun', hours: 'Closed', isClosed: true }
    ];

    return (
        <div>
            {/* Hero Image Section - Made responsive */}
            <div className="absolute inset-0 z-0 bg-opacity-5 h-72 md:h-[600px] w-full">
                <img
                    src={ContactImg}
                    alt="Luxury Pool"
                    className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-30"></div>
            </div>

            {/* Contact Information Section - Adjusted for mobile */}
            <div className="flex justify-center pt-72 md:pt-[600px] bg-white font-inter md:ml-16">
                <div className="max-w-6xl w-full px-4 md:px-6">
                    <h1 className="text-2xl md:text-3xl font-medium text-darkBlue mt-6 md:mt-8">Contact</h1>
                    <p className="text-sm md:text-base text-smokyGray mt-2 md:mt-4 mb-6 md:mb-8">
                        This is how you get in touch with us
                    </p>

                    <div>
                        <h2 className="text-lg md:text-xl font-medium text-smokyGray mb-4 md:mb-6">
                            Internet - Travel shop EFly
                        </h2>
                        <div className="text-sm md:text-base text-lightGray space-y-2 mb-6">
                            <p className='hover:text-red break-words'>Langstrasse 214, 8005 Zurich</p>
                            <p className='hover:text-red'><EMAIL></p>
                            <p className='hover:text-red'>+41 44 666 0606</p>
                        </div>

                        <div className="flex flex-col md:flex-row space-y-3 md:space-y-0 md:space-x-8 mb-6 md:mb-8">
                            <h3 className="text-base text-smokyGray mb-2 md:mb-4">Opening Hours:</h3>
                            <div className="space-y-3 md:space-y-4">
                                {openingHours.map((item, index) => (
                                    <div key={index} className="flex items-start md:items-center text-smokyGray">
                                        <span className="text-sm md:text-base w-12 md:w-20">{item.day}</span>
                                        <span className={`text-sm md:text-base hover:text-red ml-2`}>{item.hours}</span>
                                    </div>
                                ))}
                            </div>
                        </div>

                        <div>
                            <h2 className="text-lg md:text-xl font-medium text-smokyGray mb-4 md:mb-6">
                                Sri Lanka Telephone Service
                            </h2>
                            <p className="text-sm md:text-base text-lightGray  mb-6 md:mb-8 break-words">
                                Langstrasse 214, 8005 Zurich
                            </p>

                            <div className="mb-8">
                                <h3 className="text-lg md:text-xl font-medium text-smokyGray mb-3 md:mb-4">
                                    Social Media
                                </h3>
                                <div className="flex flex-wrap gap-4 md:gap-7 text-socialmedia">
                                    <Facebook className="w-5 h-5 md:w-6 md:h-6" />
                                    <Twitter className="w-5 h-5 md:w-6 md:h-6" />
                                    <Youtube className="w-5 h-5 md:w-6 md:h-6" />
                                    <Instagram className="w-5 h-5 md:w-6 md:h-6" />
                                    <Globe className="w-5 h-5 md:w-6 md:h-6" strokeWidth={1.5} />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default ContactPage;