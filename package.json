{"name": "basic-folder-stucture", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@mui/icons-material": "^7.1.2", "@mui/material": "^7.2.0", "@mui/styled-engine-sc": "^6.4.9", "@mui/x-date-pickers": "^8.6.0", "@mui/x-date-pickers-pro": "^8.6.0", "@reduxjs/toolkit": "^2.8.1", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "cities": "^2.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "country-json": "^2.3.0", "country-state-city": "^3.2.1", "date-fns": "^4.1.0", "date-of-birth-js": "^2.0.5", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "formik": "^2.4.6", "framer-motion": "^11.18.2", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "leaflet": "^1.9.4", "libphonenumber-js": "^1.12.10", "lodash": "^4.17.21", "lucide": "^0.503.0", "lucide-react": "^0.469.0", "rc-slider": "^11.1.8", "react": "^18.3.1", "react-autosuggest": "^10.1.0", "react-calendar": "^5.1.0", "react-country-state-city": "^1.1.12", "react-d-calendar": "^1.1.0", "react-date-picker": "^11.0.0", "react-date-range": "^2.0.1", "react-datepicker": "^8.2.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "react-leaflet": "^4.2.1", "react-phone-number-input": "^3.4.12", "react-redux": "^9.2.0", "react-router-dom": "^7.1.1", "react-slick": "^0.30.3", "react-toastify": "^11.0.5", "react-tooltip": "^5.28.0", "recharts": "^2.15.0", "redux-persist": "^6.0.0", "slick-carousel": "^1.8.1", "styled-components": "^6.1.19", "swr": "^2.3.3", "tailwind-merge": "^2.6.0", "xml-js": "^1.6.11", "xmlbuilder": "^15.1.1", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.4.49", "tailwindcss": "^3.4.17", "vite": "^6.3.2"}}