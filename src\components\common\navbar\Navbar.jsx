import React, { useState, useEffect, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import {
  BookOpen,
  ChevronRight,
  Globe,
  MapPin,
  Menu,
  Package,
  Search,
  X,
  Heart,
  UserRound,
} from "lucide-react";
import { CiUser } from "react-icons/ci";
import switzerlandFlag from "../../../assets/navbar/Switzerland.png";
import EnglandFlag from "../../../assets/navbar/England.png";
import FranceFlag from "../../../assets/navbar/France.png";
import EflyLogo from "../../../assets/navbar/Efly Logo.png";
import ButtonCom from "../../ui/button/ButtonCom";
import { IconButton, Tooltip } from "@mui/material";

const headerText = [
  { text: "Explore", icon: <Globe className="h-5 w-5" />, url: "/explore" },
  { text: "Offers", icon: <BookOpen className="h-5 w-5" />, url: "/offers" },
  { text: "Destinations", icon: <MapPin className="h-5 w-5" />, url: "/destinations" },
  { text: "Contact", icon: <Package className="h-5 w-5" />, url: "/contact" },
];

const languages = [
  { code: "Select Language", label: "XX", isDefault: true },
  { code: "German", label: "DE", flag: switzerlandFlag },
  { code: "English", label: "EN", flag: EnglandFlag },
  { code: "French", label: "FR", flag: FranceFlag },
];

const Navbar = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const menuRef = useRef(null);
  const buttonRef = useRef(null);

  const [isOpen, setIsOpen] = useState(false);
  const [navState, setNavState] = useState(false);
  const [languageDropdown, setLanguageDropdown] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(languages[0]);

  const isHomePage = location.pathname === "/";
  const isContactPage = location.pathname === "/contact";
  const isTransparentPage = isHomePage || isContactPage;

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };
  const toggleLanguageDropdown = () => setLanguageDropdown(!languageDropdown);

  const handleNavigation = (url) => {
    navigate(url);
    setIsOpen(false);
  };

  const handleLanguageSelect = (lang) => {
    setSelectedLanguage(lang);
    setLanguageDropdown(false);
    console.log("Selected language:", lang.code); 
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        menuRef.current &&
        !menuRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
      if (languageDropdown && !event.target.closest(".language-dropdown")) {
        setLanguageDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [languageDropdown]);

  useEffect(() => {
    const onNavScroll = () => {
      const scrolled = window.scrollY > 180;
      setNavState(scrolled);
    };

    window.addEventListener("scroll", onNavScroll);
    return () => {
      window.removeEventListener("scroll", onNavScroll);
    };
  }, []);

  const headerTextColor = navState
    ? "text-white"
    : isTransparentPage
      ? "text-white"
      : "text-darkBlue";

  const iconHoverBg = navState || isTransparentPage
    ? "rgba(255, 255, 255, 0.1)"
    : "rgba(0, 0, 0, 0.1)";

  return (
    <div
      className={`w-full z-50 top-0 left-0 transition-all duration-300 overflow-visible ${
        navState
          ? "fixed bg-darkBlue"
          : isTransparentPage
            ? "relative bg-transparent"
            : "relative bg-white"
      }`}
    >
      <div className="h-[50px] flex justify-center items-center bg-opacity-80">
        <div className="max-w-[100%] md:max-w-[80%] w-full flex justify-between items-center h-full font-light text-base text-darkBlue px-2 md:px-0">
          <div className="flex flex-row items-center space-x-6">
            <img
              src={EflyLogo}
              alt="Efly Logo"
              className="w-20 h-20 cursor-pointer bg-opacity-100"
              onClick={() => handleNavigation("/")}
            />
            <div className="hidden lg:flex space-x-8 mt-1">
              {headerText.map((item, index) => (
                <span
                  key={index}
                  className={`cursor-pointer hover:text-orange ${headerTextColor}`}
                  onClick={() => handleNavigation(item?.url)}
                >
                  {item.text}
                </span>
              ))}
            </div>
          </div>

          <div className="hidden lg:flex space-x-6 items-center ml-8 hover:cursor-pointer">
            <Tooltip
              title="Watchlist"
              placement="bottom"
              componentsProps={{
                tooltip: {
                  sx: {
                    bgcolor: navState || isTransparentPage ? "rgba(0, 0, 0, 0.4)" : "rgba(255, 255, 255, 0.9)",
                    color: navState || isTransparentPage ? "white" : "darkBlue",
                    fontSize: "0.875rem",
                    padding: "8px 12px",
                    borderRadius: "4px",
                  },
                },
              }}
            >
              <IconButton
                onClick={() => handleNavigation("/watchlist")}
                sx={{
                  color: headerTextColor,
                  "&:hover": { backgroundColor: iconHoverBg },
                }}
              >
                <Heart className={`h-5 w-5 ${headerTextColor}`} strokeWidth={1.3} />
              </IconButton>
            </Tooltip>
            <div className="relative language-dropdown">
              <Tooltip
                title={selectedLanguage.isDefault ? "Select Language" : selectedLanguage.code}
                placement="bottom"
                componentsProps={{
                  tooltip: {
                    sx: {
                      bgcolor: navState || isTransparentPage ? "rgba(0, 0, 0, 0.4)" : "rgba(255, 255, 255, 0.9)",
                      color: navState || isTransparentPage ? "white" : "darkBlue",
                      fontSize: "0.875rem",
                      padding: "8px 12px",
                      borderRadius: "4px",
                    },
                  },
                }}
              >
                <IconButton
                  onClick={toggleLanguageDropdown}
                  sx={{
                    color: headerTextColor,
                    "&:hover": { backgroundColor: iconHoverBg },
                  }}
                >
                  {selectedLanguage.isDefault ? (
                    <Globe className={`h-5 w-5 ${headerTextColor}`} strokeWidth={1.3} />
                  ) : (
                    <img
                      src={selectedLanguage.flag}
                      alt="Selected Language Flag"
                      className="w-5 h-5 rounded-full object-cover"
                    />
                  )}
                </IconButton>
              </Tooltip>
              {languageDropdown && (
                <ul
                  className="absolute bg-white/40 shadow-lg rounded-md w-40 mt-2 z-[100]"
                  style={{ backdropFilter: "blur(4px)" }}
                >
                  {languages
                    .filter((lang) => !lang.isDefault)
                    .map((lang) => (
                      <li
                        key={lang.code}
                        className="flex flex-row space-x-2 items-center p-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleLanguageSelect(lang)}
                      >
                        <img
                          src={lang.flag}
                          alt={`${lang.label} Flag`}
                          className="w-6 h-6 rounded-full object-cover"
                        />
                        <span className="text-base font-light tracking-widest text-darkBlue">
                          {lang.code}
                        </span>
                      </li>
                    ))}
                </ul>
              )}
            </div>
            <Tooltip
              title="Login"
              placement="bottom"
              componentsProps={{
                tooltip: {
                  sx: {
                    bgcolor: navState || isTransparentPage ? "rgba(0, 0, 0, 0.4)" : "rgba(255, 255, 255, 0.9)",
                    color: navState || isTransparentPage ? "white" : "darkBlue",
                    fontSize: "0.875rem",
                    padding: "8px 12px",
                    borderRadius: "4px",
                  },
                },
              }}
            >
              <IconButton
                onClick={() => handleNavigation("/login")}
                sx={{
                  color: headerTextColor,
                  "&:hover": { backgroundColor: iconHoverBg },
                }}
              >
                <UserRound className={`h-5 w-5 ${headerTextColor}`} strokeWidth={1.3} />
              </IconButton>
            </Tooltip>
          </div>

          <div className="lg:hidden">
            <div className="flex items-center space-x-4">
              <Tooltip
                title="Watchlist"
                placement="bottom"
                componentsProps={{
                  tooltip: {
                    sx: {
                      bgcolor: navState || isTransparentPage ? "rgba(0, 0, 0, 0.8)" : "rgba(255, 255, 255, 0.9)",
                      color: navState || isTransparentPage ? "white" : "darkBlue",
                      fontSize: "0.875rem",
                      padding: "8px 12px",
                      borderRadius: "4px",
                    },
                  },
                }}
              >
                <IconButton
                  onClick={() => handleNavigation("/watchlist")}
                  sx={{
                    color: headerTextColor,
                    "&:hover": { backgroundColor: iconHoverBg },
                  }}
                >
                  <Heart className={`h-5 w-5 hover:cursor-pointer ${headerTextColor}`} strokeWidth={1.3} />
                </IconButton>
              </Tooltip>
              <div className="relative language-dropdown">
                <Tooltip
                  title={selectedLanguage.isDefault ? "Select Language" : selectedLanguage.code}
                  placement="bottom"
                  componentsProps={{
                    tooltip: {
                      sx: {
                        bgcolor: navState || isTransparentPage ? "rgba(0, 0, 0, 0.8)" : "rgba(255, 255, 255, 0.9)",
                        color: navState || isTransparentPage ? "white" : "darkBlue",
                        fontSize: "0.875rem",
                        padding: "8px 12px",
                        borderRadius: "4px",
                      },
                    },
                  }}
                >
                  <IconButton
                    onClick={toggleLanguageDropdown}
                    sx={{
                      color: headerTextColor,
                      "&:hover": { backgroundColor: iconHoverBg },
                    }}
                  >
                    {selectedLanguage.isDefault ? (
                      <Globe className={`h-5 w-5 hover:cursor-pointer ${headerTextColor}`} strokeWidth={1.3} />
                    ) : (
                      <img
                        src={selectedLanguage.flag}
                        alt="Selected Language Flag"
                        className="w-5 h-5 rounded-full object-cover"
                      />
                    )}
                  </IconButton>
                </Tooltip>
                {languageDropdown && (
                  <ul
                    className="absolute bg-white/40 shadow-lg rounded-md w-32 mt-2 z-[100]"
                    style={{ backdropFilter: "blur(4px)" }}
                  >
                    {languages
                      .filter((lang) => !lang.isDefault)
                      .map((lang) => (
                        <li
                          key={lang.code}
                          className="flex flex-row space-x-2 items-center p-2 hover:bg-gray-100 cursor-pointer"
                          onClick={() => {
                            handleLanguageSelect(lang);
                            setIsOpen(false);
                          }}
                        >
                          <img
                            src={lang.flag}
                            alt={`${lang.label} Flag`}
                            className="w-6 h-6 rounded-full object-cover"
                          />
                          <span className="text-base font-light tracking-widest text-darkBlue">
                            {lang.code}
                          </span>
                        </li>
                      ))}
                  </ul>
                )}
              </div>
              <Tooltip
                title="Login"
                placement="bottom"
                componentsProps={{
                  tooltip: {
                    sx: {
                      bgcolor: navState || isTransparentPage ? "rgba(0, 0, 0, 0.8)" : "rgba(255, 255, 255, 0.9)",
                      color: navState || isTransparentPage ? "white" : "darkBlue",
                      fontSize: "0.875rem",
                      padding: "8px 12px",
                      borderRadius: "4px",
                    },
                  },
                }}
              >
                <IconButton
                  onClick={() => handleNavigation("/login")}
                  sx={{
                    color: headerTextColor,
                    "&:hover": { backgroundColor: iconHoverBg },
                  }}
                >
                  <CiUser className={`h-5 w-5 hover:cursor-pointer ${headerTextColor}`} />
                </IconButton>
              </Tooltip>
              <ButtonCom
                ref={buttonRef}
                variant="ghost"
                size="sm"
                onClick={toggleMenu}
                ariaLabel="Toggle Menu"
              >
                {isOpen ? (
                  <X className="h-5 w-5 hover:cursor-pointer" />
                ) : (
                  <Menu className={`h-5 w-5 hover:cursor-pointer ${headerTextColor}`} />
                )}
              </ButtonCom>
            </div>
          </div>
        </div>
      </div>

      {isOpen && (
        <div
          ref={menuRef}
          className="lg:hidden bg-white bg-opacity-95 shadow-lg p-1 md:px-4"
        >
          <div className="flex flex-row justify-between py-2">
            <div className="font-light">Search</div>
            <div className="opacity-50 font-light">
              <Search />
            </div>
          </div>
          <div className="border-t border-gray opacity-10"></div>
          <div className="py-2 space-y-2">
            {headerText.map((item, index) => (
              <div
                key={index}
                className="block text-darkBlue hover:text-primaryColor cursor-pointer py-2 text-lg font-semibold"
                onClick={() => handleNavigation(item?.url)}
              >
                <div className="flex flex-row justify-between">
                  <div className="flex flex-row items-center space-x-2">
                    <div className="opacity-50">{item.icon}</div>
                    <div>{item.text}</div>
                  </div>
                  <div className="opacity-50">
                    <ChevronRight />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default Navbar;