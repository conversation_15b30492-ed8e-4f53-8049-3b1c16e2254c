import { lightBlue } from "@mui/material/colors";
import { button } from "framer-motion/client";

/** @type {import('tailwindcss').Config} */
export default {
  darkMode: false,
  content: ["./index.html", "./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    extend: {
      height: {
        defaultHeight: "5vh",
      },
      colors: {
        primaryColor: "#0761AE",
        secondaryColor: "#F89D1E",
        accentColor: "#F09A1C",
        textColor: "#222222",
        backgroundColor: "#F2F2F2",
        darkBlue: "#024575",
        white: "#FFFFFF",
        orange: "#FFA333",
        gray: "#3C3C3C",
        smokeGray: "#C2C2C2",
        borderGray: "#DDDDDD",
        smokyGray: "#5A5A5A",
        red: "#E30606",
        lightGray: "#9C9C9C",
        darkcolor: "#484747",
        offWhite: "#F5F5F5",
        buttoncolor: "#8B8B8B",
        border: "#D5D5D5",
        socialmedia: "#666666",
        green: "#1CAC03",
        day: "#EAEAEA",
        disable: "#D1D5DB",
        bgcolor: "#F6F5F5",
        darkGray: "#222222",
        darkGreen: "#088828",
        lightBlue: "#EDF7FE",
        adjustcolor: "#E6EDF2",
      },

      maxWidth: {
        xs: "475px",
        sm: "640px",
        md: "768px",
        lg: "1024px",
        xl: "1280px",
        "2xl": "1536px",
      },

      fontFamily: {
        roboto: ["Roboto", "sans-serif"],
        inter: ["Inter", "sans-serif"],
        italiana: ["Italiana", "serif"],
        nunito: ["Nunito", "sans-serif"],
        poppins: ["Poppins", "sans-serif"],
      },

      fontWeight: {},

      fontSize: {
        xs: ["0.75rem", { lineHeight: "1.5" }],
        sm: ["0.875rem", { lineHeight: "1.5715" }],
        base: ["1rem", { lineHeight: "1.5", letterSpacing: "-0.01em" }],
        lg: ["1.125rem", { lineHeight: "1.5", letterSpacing: "-0.01em" }],
        xl: ["1.25rem", { lineHeight: "1.5", letterSpacing: "-0.01em" }],
        "2xl": ["1.5rem", { lineHeight: "1.33", letterSpacing: "-0.01em" }],
        "3xl": ["1.88rem", { lineHeight: "1.33", letterSpacing: "-0.01em" }],
        "4xl": ["2.25rem", { lineHeight: "1.25", letterSpacing: "-0.02em" }],
        "5xl": ["3rem", { lineHeight: "1.25", letterSpacing: "-0.02em" }],
        "6xl": ["3.75rem", { lineHeight: "1.2", letterSpacing: "-0.02em" }],
      },

      fontStyle: {},
      keyframes: {
        planeWiggle: {
          '0%, 100%': {
            transform: 'rotate(10deg)',
            color: '#FFA500',
          },
          '50%': {
            transform: 'rotate(-10deg)',
            color: '#024575',
          },
        },
        blinkTwice: {
          '0%, 100%': { opacity: 1 },
          '25%': { opacity: 0 },
          '50%': { opacity: 1 },
          '75%': { opacity: 0 },
        },
      },
      animation: {
        planeWiggle: 'planeWiggle 3s ease-in-out infinite',
        blinkTwice: 'blinkTwice 1s ease-in-out 1',
      },
    },
    screens: {
      xs: "475px",
      sm: "640px",
      md: "768px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1536px",
    },
    container: {
      center: true,
      padding: {
        DEFAULT: "1rem",
        sm: "2rem",
        lg: "4rem",
        xl: "6rem",
      },
    },
  },
  plugins: [],
};
