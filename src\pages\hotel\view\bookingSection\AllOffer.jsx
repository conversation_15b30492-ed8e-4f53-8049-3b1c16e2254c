import React, { useState } from 'react';
import { Bed, Calendar, Check, Minus, Plus, UserRound, Gift, Tag } from 'lucide-react';
import { motion } from 'framer-motion';
import FacilitiesIcon from "../FacilitiesIcon";
import HotelCalendarComponent from "../../../home/<USER>/HotelCalendarComponent";
import ButtonCom from "../../../../components/ui/button/ButtonCom";
import { useHotelContext } from '../context/HotelContext';
import { useRoomSelection } from '../hooks/useRoomSelection';
import { useSmoothScroll } from '../hooks/useSmoothScroll';

// Sub-components (SpecialOffers, CancellationRules, RoomTabs, RoomListing, TotalPanel) remain similar but use Context and hooks
const SpecialOffers = ({ specials }) => {
  if (!specials || specials.length === 0) return null;
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      className="bg-gradient-to-r from-green to-emerald-50 border border-green rounded-lg p-3 mb-3"
    >
      <div className="flex items-center gap-2 mb-2">
        <Gift className="w-4 h-4 text-green" />
        <span className="text-sm font-semibold text-green">Special Offers</span>
      </div>
      <div className="space-y-1">
        {specials.map((special, index) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <Tag className="w-3 h-3 text-green" />
            <span className="text-green font-medium">{special.specialName}</span>
            {special.discount && (
              <span className="bg-green text-green px-2 py-1 rounded-full text-xs font-bold">
                {special.discount}% OFF
              </span>
            )}
          </div>
        ))}
      </div>
    </motion.div>
  );
};

const AllOffer = () => {
  const { hotelDetails, searchCriteria } = useHotelContext();
  const [selectedHeader, setSelectedHeader] = useState(null);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [showTravelersModal, setShowTravelersModal] = useState(false);
  const [activeSection, setActiveSection] = useState(null);
  const {
    selectedRooms,
    travelerConfig,
    dateRange,
    setDateRange,
    flexibleDays,
    setFlexibleDays,
    nights,
    displayText,
    dateRangeText,
    handleRoomSelect,
    calculateTotalPrice,
    handleReserve,
    handleTravelerChange,
    handleChangeSearch,
  } = useRoomSelection(hotelDetails, searchCriteria, roomTypes);
  const { roomRefs, scrollToRoom } = useSmoothScroll(roomTypes, setSelectedHeader);

  const roomTypes = useMemo(() => {
    if (!hotelDetails?.rooms || hotelDetails.rooms.length === 0) return [];
    return hotelDetails.rooms.map((roomType, index) => ({
      id: roomType.roomTypeCode || index,
      type: roomType.roomTypeName || 'Unknown Room Type',
      singleRoom: roomType.twin === 'yes' ? 2 : 1,
      description: roomType.roomInfo ? `Max ${roomType.roomInfo.maxOccupancy} guests` : 'No description available.',
      roomTypeCode: roomType.roomTypeCode || `ROOM_${index}`,
      configurations: roomType.rateBases?.map((rate, configIndex) => ({
        id: `${index}-${configIndex}`,
        guestConfig: { adults: travelerConfig.adults, children: travelerConfig.children, childAge: 6 },
        price: rate.totalCharge ? `CHF ${rate.totalCharge.toFixed(2)}` : 'N/A',
        totalTax: rate.totalTaxes ? `+ CHF ${parseFloat(rate.totalTaxes).toFixed(2)} Taxes and Charges` : '+ CHF 0.00 Taxes and Charges',
        benefits: [{ text: rate.rateBaseName || 'Room Only', included: !!rate.rateBaseName }],
        nights: `${nights} ${nights === 1 ? 'night' : 'nights'}`,
        totalPrice: rate.totalCharge ? `CHF ${rate.totalCharge.toFixed(2)}` : 'N/A',
        leftToSell: rate.leftToSell || 3,
        rateBaseId: rate.rateBaseId || 0,
        tariffNotes: rate.tariffNotes || '',
        totalTaxes: rate.totalTaxes || '0.00',
        cancellationRules: rate.cancellationRules || [],
        availableDates: rate.availableDates || [],
        specials: rate.specials || [],
      })) || [],
    }));
  }, [hotelDetails.rooms, nights, travelerConfig]);

  if (!hotelDetails || !hotelDetails.rooms) {
    return <div className="text-center text-gray-600">Loading hotel details or no rooms available...</div>;
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="w-full space-y-0 md:space-y-8"
    >
      <div className="mb-8 px-6 py-3 md:px-0 md:py-0">
        <h2 className="text-2xl font-semibold text-darkBlue mb-6">Availability</h2>
        {/* Date picker and traveler modal UI remains the same */}
      </div>
      <div className="space-y-6 scroll-smooth">
        {roomTypes.length === 0 ? (
          <div className="text-center text-gray-600 py-12">
            <Bed className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">No rooms available</h3>
            <p>No rooms available for the selected dates. Please try different dates.</p>
          </div>
        ) : (
          <>
            <RoomTabs roomTypes={roomTypes} selectedHeader={selectedHeader} setSelectedHeader={setSelectedHeader} scrollToRoom={scrollToRoom} />
            <div className="flex flex-col md:flex-row gap-4">
              <div className="hidden md:block relative chart-container pb-16 space-y-6 w-full md:w-3/4">
                {roomTypes.map((roomType, roomTypeIndex) => (
                  <div key={roomType.id} id={`room-${roomType.id}`} ref={(el) => (roomRefs.current[roomType.id] = el)} className="space-y-4">
                    {roomType.configurations.map((config, configIndex) => (
                      <RoomListing
                        key={config.id}
                        roomType={roomType}
                        config={config}
                        configIndex={configIndex}
                        roomTypeIndex={roomTypeIndex}
                        selectedRooms={selectedRooms}
                        handleRoomSelect={handleRoomSelect}
                        travelerConfig={travelerConfig}
                        hotelDetails={hotelDetails}
                      />
                    ))}
                  </div>
                ))}
              </div>
              <TotalPanel
                travelerConfig={travelerConfig}
                displayText={displayText}
                calculateTotalPrice={calculateTotalPrice}
                roomTypes={roomTypes}
                selectedRooms={selectedRooms}
                handleReserve={handleReserve}
                bookingDetails={{ hotelDetails, searchCriteria, nights, dateRangeText }}
              />
            </div>
          </>
        )}
      </div>
       {/* Mobile view */}
      <div className="flex flex-col h-screen md:hidden">
        <div className="chart-container overflow-y-auto flex-1 px-6 py-3 md:px-0 md:py-0 space-y-4">
          {roomTypes.map((roomType, roomTypeIndex) => (
            <div
              key={roomType.id}
              id={`room-${roomType.id}`}
              ref={(el) => (roomRefs.current[roomType.id] = el)}
              className="border border-border rounded-[8px] p-4 bg-white shadow-md"
            >
              <h3 className="text-lg font-semibold text-darkBlue">{roomType.type}</h3>
              <p className="text-sm text-gray-600 mt-1">{roomType.description}</p>
              <div className="flex mt-1">
                {Array(travelerConfig.adults)
                  .fill(0)
                  .map((_, i) => (
                    <UserRound key={`adult-${i}`} className="w-5 h-5 mr-1" />
                  ))}
                {travelerConfig.children > 0 &&
                  Array(travelerConfig.children)
                    .fill(0)
                    .map((_, i) => (
                      <UserRound key={`child-${i}`} className="w-4 h-4 mr-1" />
                    ))}
              </div>
              <div className="mt-4 space-y-2">
                {roomType.configurations.map((config, configIndex) => (
                  <div
                    key={config.id}
                    className="border border-border p-3 rounded-[8px] space-y-3"
                  >
                    <div className="flex flex-col justify-between items-start mb-2">
                      <p className="text-sm font-medium">{config.price}</p>
                      <p className="text-xs font-light">{config.priceDetails}</p>
                    </div>
                    <div className="text-xs">{config.nights}</div>
                    <ul className="mt-2 space-y-2">
                      {config.benefits.map((benefit, i) => (
                        <li
                          key={i}
                          className="text-xs text-darkGreen flex items-center gap-1"
                        >
                          <Check size={14} strokeWidth={1} /> {benefit.text}
                        </li>
                      ))}
                    </ul>
                    <CancellationRules
                      rules={config.cancellationRules}
                    />
                    {selectedRooms[config.id] > 0 ? (
                      <>
                        <button
                          className="mt-3 w-full bg-darkBlue text-white text-sm py-2 rounded-[8px]"
                          onClick={() =>
                            setShowRoomSelectorId(
                              showRoomSelectorId === config.id ? null : config.id
                            )
                          }
                          aria-label={`Selected ${selectedRooms[config.id]} rooms`}
                        >
                          Selected ({selectedRooms[config.id]}{" "}
                          {selectedRooms[config.id] === 1 ? "Room" : "Rooms"})
                        </button>
                        {showRoomSelectorId === config.id && (
                          <div className="mt-2 border border-border p-3 rounded-[8px] bg-[#f9f9f9] space-y-3">
                            <div className="flex items-center justify-between">
                              <button
                                type="button"
                                onClick={() =>
                                  handleRoomSelect(
                                    roomTypeIndex,
                                    configIndex,
                                    (selectedRooms[config.id] || 0) - 1
                                  )
                                }
                                disabled={(selectedRooms[config.id] || 0) <= 0}
                                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${(selectedRooms[config.id] || 0) <= 0
                                  ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                                  : ""
                                  }`}
                                aria-label="Decrease room quantity"
                              >
                                <Minus className="w-3 h-3" />
                              </button>
                              <span className="text-sm font-light text-darkGray">
                                {selectedRooms[config.id] || 0} Room
                              </span>
                              <button
                                type="button"
                                onClick={() =>
                                  handleRoomSelect(
                                    roomTypeIndex,
                                    configIndex,
                                    (selectedRooms[config.id] || 0) + 1
                                  )
                                }
                                disabled={
                                  (selectedRooms[config.id] || 0) >= travelerConfig.rooms
                                }
                                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${(selectedRooms[config.id] || 0) >= travelerConfig.rooms
                                  ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                                  : ""
                                  }`}
                                aria-label="Increase room quantity"
                              >
                                <Plus className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <button
                        className="mt-3 w-full bg-darkBlue text-white text-sm py-2 rounded-[8px]"
                        onClick={() => {
                          handleRoomSelect(roomTypeIndex, configIndex, 1);
                          setShowRoomSelectorId(config.id);
                        }}
                        aria-label="Select room"
                      >
                        Select
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        <div className="sticky bottom-0 top-10 bg-offWhite z-30 border-t border-border px-4 py-3">
          <div className="max-h-[200vh] space-y-3">
            <div className="space-y-2 shadow-sm">
              {selectedRoomDetails.length === 0 ? (
                <div className="text-xs">No rooms selected</div>
              ) : (
                <>
                  <h3 className="text-xs font-normal">Selected Room</h3>
                  {selectedRoomDetails.map(({ roomTypeName, config, count, roomTypeId }) => (
                    <div key={roomTypeId}>
                      <div
                        className="text-xs font-medium text-darkBlue cursor-pointer hover:underline"
                        onClick={() => scrollToRoom(roomTypeId)}
                      >
                        {roomTypeName} ({count} {count > 1 ? "Rooms" : "Room"})
                      </div>
                    </div>
                  ))}
                </>
              )}
              <div className="text-xs">
                {Object.values(selectedRooms).reduce((sum, count) => sum + count, 0)} Room
                {Object.values(selectedRooms).reduce((sum, count) => sum + count, 0) !== 1 ? "s" : ""} for
              </div>
              <div className="text-xs">{displayText}</div>
              <hr className="border-border" />
              <div className="text-sm font-medium w-full text-end">
                CHF {calculateTotalPrice()}
                {selectedRoomDetails.length > 0 && (
                  <p className="text-xs font-normal">
                    {selectedRoomDetails[0].config.totalTax || "+ Taxes and Charges"}
                  </p>
                )}
              </div>
              <div className="flex justify-between items-center pt-4">
                <ButtonCom
                  variant="primary"
                  size="md"
                  width="full"
                  disabled={selectedRoomDetails.length === 0}
                  onClick={() => {
                    if (selectedRoomDetails.length > 0) {
                      handleReserve(
                        selectedRoomDetails[0].roomTypeName,
                        selectedRoomDetails[0].config,
                        selectedRoomDetails[0].roomTypeCode
                      );
                    }
                  }}
                  ariaLabel="Reserve selected room"
                >
                  Reserve
                </ButtonCom>
              </div>
            </div>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default AllOffer;