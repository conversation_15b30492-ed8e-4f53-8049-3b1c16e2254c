import React, { useState, useEffect, useRef, useMemo, useCallback } from "react";
import {
  BedDouble, Calendar, Check, Minus, Plus, UserRound, Gift, Info, Bed, Tag} from "lucide-react";
import { useNavigate } from "react-router-dom";
import FacilitiesIcon from "../FacilitiesIcon";
import HotelCalendarComponent from "../../../home/<USER>/HotelCalendarComponent";
import ButtonCom from "../../../../components/ui/button/ButtonCom";

// Special Offers Component
const SpecialOffers = ({ specials }) => {
  if (!specials || specials.length === 0) return null;

  return (
    <div className="bg-gradient-to-r from-green to-emerald-50 border border-green rounded-lg p-3 mb-3">
      <div className="flex items-center gap-2 mb-2">
        <Gift className="w-4 h-4 text-green" />
        <span className="text-sm font-semibold text-green">Special Offers</span>
      </div>
      <div className="space-y-1">
        {specials.map((special, index) => (
          <div key={index} className="flex items-center gap-2 text-sm">
            <Tag className="w-3 h-3 text-green" />
            <span className="text-green font-medium">
              {special.specialName}
            </span>
            {special.discount && (
              <span className="bg-green text-green px-2 py-1 rounded-full text-xs font-bold">
                {special.discount}% OFF
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

// Cancellation Rules Component
const CancellationRules = ({ rules,}) => {
  if (!rules || rules.length === 0) return null;

  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleDateString('en-US', {
      weekday: 'short',
      day: '2-digit',
      month: 'short',
      year: 'numeric',
    });
  };

  const getRuleIcon = (type) => {
    switch (type) {
      case 'free_cancellation':
        return <Check className="w-4 h-4 text-darkGreen" />;
      case 'penalty_period':
        return <Check className="w-4 h-4 text-darkGreen" />;
      case 'no_show':
        return <Check className="w-4 h-4 text-darkGreen" />;
      default:
        return <Info className="w-4 h-4 text-darkGreen" />;
    }
  };

  const getRuleColor = (type) => {
    switch (type) {
      case 'free_cancellation':
        return 'text-darkGreen';
      case 'penalty_period':
        return 'text-darkGreen ';
      case 'no_show':
        return 'text-darkGreen ';
      default:
        return 'text-darkGreen ';
    }
  };

  return (
    <div className="">
      <button
        className="flex items-center justify-between w-full text-left  p-1 rounded"
      >
        {/* <span className="text-sm  text-gray flex items-center gap-2">
          <Info className="w-4 h-4" />
          Cancellation Policy
        </span> */}
      </button>
        <div className="space-y-2">
          {rules.map((rule, index) => (
            <div key={index} className={`${getRuleColor(rule.type)}`}>
              <div className="flex items-start ">
                {getRuleIcon(rule.type)}
                <div className="flex">
                  <div className="text-xs font-semibold">
                    {rule.type === 'free_cancellation' && 'Free Cancellation'}
                    {rule.type === 'penalty_period' && 'Cancellation Fee'}
                    {rule.type === 'no_show' && 'No Show Policy'}
                  </div>
                  <div className="text-[10px] mt-1 ml-1">
                    {rule.type === 'free_cancellation' && (
                      <span>Until {formatDate(rule.toDate)}</span>
                    )}
                    {rule.type === 'penalty_period' && (
                      <span>
                        From {formatDate(rule.fromDate)} - Fee: {rule.formattedCancelCharge || rule.formattedCharge}
                      </span>
                    )}
                    {rule.type === 'no_show' && (
                      <span>
                        From {formatDate(rule.fromDate)} - Charge: {rule.formattedCharge}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
    </div>
  );
};

// Daily Rates Component
// const DailyRates = ({ availableDates, isExpanded, onToggle }) => {
//   if (!availableDates || availableDates.length === 0) return null;

//   return (
//     <div className="border border-gray-200 rounded-lg p-3 mb-3">
//       <button
//         onClick={onToggle}
//         className="flex items-center justify-between w-full text-left hover:bg-gray-50 p-1 rounded"
//       >
//         <span className="text-sm font-semibold text-gray-800 flex items-center gap-2">
//           <Calendar className="w-4 h-4" />
//           Daily Rates ({availableDates.length} nights)
//         </span>
//         {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
//       </button>

//       {isExpanded && (
//         <div className="mt-3 space-y-2">
//           {availableDates.map((date, index) => (
//             <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
//               <span className="text-sm text-gray-700">{date.day}</span>
//               <div className="flex items-center gap-2">
//                 <span className="text-sm font-medium text-darkBlue">
//                   CHF {date.formatedPrice}
//                 </span>
//                 {date.freeStay && (
//                   <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
//                     FREE
//                   </span>
//                 )}
//                 {date.discount && (
//                   <span className="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs">
//                     -{date.discount}%
//                   </span>
//                 )}
//               </div>
//             </div>
//           ))}
//         </div>
//       )}
//     </div>
//   );
// };

// Enhanced Room Tabs with perfect scrolling
const RoomTabs = ({ roomTypes, selectedHeader, setSelectedHeader, scrollToRoom }) => {
  const tabsRef = useRef(null);
  const [isScrolling, setIsScrolling] = useState(false);

  const handleTabClick = useCallback((roomType) => {
    if (isScrolling) return; // Prevent multiple clicks during scroll

    setIsScrolling(true);
    setSelectedHeader(roomType.id);

    // Add visual feedback immediately
    const clickedTab = document.querySelector(`[data-room-id="${roomType.id}"]`);
    if (clickedTab) {
      clickedTab.style.transform = 'scale(0.95)';
      setTimeout(() => {
        clickedTab.style.transform = 'scale(1.05)';
      }, 100);
    }

    // Scroll to room
    scrollToRoom(roomType.id);

    // Reset scrolling state
    setTimeout(() => {
      setIsScrolling(false);
      if (clickedTab) {
        clickedTab.style.transform = '';
      }
    }, 1000);
  }, [isScrolling, setSelectedHeader, scrollToRoom]);

  // Auto-scroll tabs container to show active tab
  useEffect(() => {
    if (selectedHeader && tabsRef.current) {
      const activeTab = tabsRef.current.querySelector(`[data-room-id="${selectedHeader}"]`);
      if (activeTab) {
        const container = tabsRef.current;
        const containerRect = container.getBoundingClientRect();
        const tabRect = activeTab.getBoundingClientRect();

        if (tabRect.left < containerRect.left || tabRect.right > containerRect.right) {
          const scrollLeft = activeTab.offsetLeft - (container.offsetWidth / 2) + (activeTab.offsetWidth / 2);
          container.scrollTo({
            left: scrollLeft,
            behavior: 'smooth'
          });
        }
      }
    }
  }, [selectedHeader]);

  return (
    <div className="sticky top-0 z-40 bg-white/95 backdrop-blur-sm border-b border-gray-200 pb-2 mb-4">
      <div
        ref={tabsRef}
        className="flex text-xs font-light space-x-3 overflow-x-auto px-6 md:px-0 md:py-2 scrollbar-hide scroll-smooth"
        style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
      >
        {roomTypes.map((roomType) => (
          <button
            key={roomType.id}
            data-room-id={roomType.id}
            className={`px-4 py-3 rounded-[12px] cursor-pointer transition-all duration-300 whitespace-nowrap flex items-center gap-2 min-w-fit relative ${
              selectedHeader === roomType.id
                ? "bg-darkBlue text-white shadow-lg ring-2 ring-darkBlue/20"
                : "bg-darkBlue/5 text-darkBlue hover:bg-darkBlue/10 hover:shadow-md hover:scale-105"
            } ${isScrolling ? 'pointer-events-none' : ''}`}
            onClick={() => handleTabClick(roomType)}
            disabled={isScrolling}
            role="tab"
            aria-selected={selectedHeader === roomType.id}
            aria-label={`Scroll to ${roomType.type} rooms`}
          >
            <Bed className="w-4 h-4 flex-shrink-0" />
            <span className="font-medium">{roomType.type}</span>
            <span className={`px-2 py-1 rounded-full text-xs font-bold ${
              selectedHeader === roomType.id
                ? 'bg-white/20 text-white'
                : 'bg-darkBlue/10 text-darkBlue'
            }`}>
              {roomType.configurations.length}
            </span>

            {/* Active indicator */}
            {selectedHeader === roomType.id && (
              <div className="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full"></div>
            )}
          </button>
        ))}
      </div>

      {/* Enhanced scroll indicators for mobile */}
      <div className="md:hidden flex justify-center mt-3 space-x-2">
        {roomTypes.map((roomType) => (
          <button
            key={roomType.id}
            onClick={() => handleTabClick(roomType)}
            className={`w-2 h-2 rounded-full transition-all duration-300 ${
              selectedHeader === roomType.id
                ? 'bg-darkBlue scale-125'
                : 'bg-gray-300 hover:bg-gray-400'
            }`}
            aria-label={`Go to ${roomType.type}`}
          />
        ))}
      </div>

      {/* Scroll progress bar */}
      <div className="hidden md:block mt-2 h-0.5 bg-gray-200 rounded-full overflow-hidden">
        <div
          className="h-full bg-darkBlue transition-all duration-300 ease-out"
          style={{
            width: `${((roomTypes.findIndex(r => r.id === selectedHeader) + 1) / roomTypes.length) * 100}%`
          }}
        />
      </div>
    </div>
  );
};

// Room Listing
const RoomListing = ({
  roomType,
  config,
  configIndex,
  roomTypeIndex,
  selectedRooms,
  handleRoomSelect,
  travelerConfig,
  hotelDetails,
}) => {
  const [showFullNotes, setShowFullNotes] = useState(false);
  const [showAllFacilities, setShowAllFacilities] = useState(false);
  // const [showDailyRates, setShowDailyRates] = useState(false);

  const toggleNotes = useCallback(() => {
    setShowFullNotes((prev) => !prev);
  }, []);

  const toggleFacilities = useCallback(() => {
    setShowAllFacilities((prev) => !prev);
  }, []);

  const displayedFacilities = useMemo(() =>
    showAllFacilities
      ? hotelDetails?.facilities || []
      : (hotelDetails?.facilities || []).slice(0, 5),
    [showAllFacilities, hotelDetails?.facilities]
  );

  return (
    <div
      key={config.id}
      className="border border-borderGray rounded-[8px] bg-white shadow-sm mb-4"
      id={`room-${roomType.id}`}
    >
      <div className="flex flex-col md:flex-row w-full">
        <div className="p-4 md:w-2/5 w-full space-y-2">
          <h3 className="text-lg text-darkBlue font-semibold underline">
            {roomType.type}
          </h3>
          <p className="text-sm flex items-center gap-2">
            {roomType.singleRoom} {roomType.singleRoom === 2 ? "Single" : "Double"}{" "}
            room
            {roomType.singleRoom === 2 && (
              <>
                <BedDouble className="w-4 h-4 ml-2 text-black" strokeWidth={1} />
                <BedDouble className="w-4 h-4 ml-2 text-black" strokeWidth={1} />
              </>
            )}{" "}
            /
          </p>
          {roomType.description && (
            <p className="text-sm">{roomType.description}</p>
          )}

          {/* Room Info Display */}
          {/* {roomType.roomInfo && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 space-y-1">
              <div className="flex items-center gap-2 text-sm">
                <Users className="w-4 h-4 text-blue-600" />
                <span className="text-blue-800 font-medium">
                  Max {roomType.roomInfo.maxOccupancy} guests
                  {roomType.roomInfo.maxChildren > 0 && ` (${roomType.roomInfo.maxChildren} children max)`}
                </span>
              </div>
              {roomType.roomInfo.maxExtraBed > 0 && (
                <div className="text-xs text-blue-700">
                  Extra beds available: {roomType.roomInfo.maxExtraBed}
                </div>
              )}
              {roomType.roomInfo.minChildAge !== undefined && roomType.roomInfo.maxChildAge !== undefined && (
                <div className="text-xs text-blue-700">
                  Child age: {roomType.roomInfo.minChildAge}-{roomType.roomInfo.maxChildAge} years
                </div>
              )}
            </div>
          )} */}

          {/* Special Offers */}
          <SpecialOffers specials={roomType.specials} />

          <div className="flex mt-1">
            {Array(travelerConfig.adults)
              .fill(0)
              .map((_, i) => (
                <UserRound key={`adult-${i}`} className="w-5 h-5 mr-1" />
              ))}
            {travelerConfig.children > 0 &&
              Array(travelerConfig.children)
                .fill(0)
                .map((_, i) => (
                  <UserRound key={`child-${i}`} className="w-4 h-4 mr-1" />
                ))}
          </div>
          {config.benefits.map((benefit, i) => (
            <div key={i} className="flex items-center mb-2">
              <Check className="text-darkGreen mr-1" size={16} />
              <span className="text-darkGreen font-semibold text-xs">
                {benefit.text}
              </span>
            </div>
          ))}
          <CancellationRules
            rules={config.cancellationRules}
          />
        </div>
        <div className="p-4 md:w-3/5 w-full space-y-3">
          {hotelDetails?.facilities?.length > 0 && (
            <div className="flex gap-x-5 gap-y-1 flex-wrap">
              {displayedFacilities.map((facility, i) => (
                <div key={`facility-${i}`} className="flex items-center font-light text-xs">
                  <FacilitiesIcon label={facility.label} className="w-3 h-4 mr-1" />
                  <span className="text-xs">{facility.label}</span>
                </div>
              ))}
              {hotelDetails.facilities.length > 5 && (
                <button
                  onClick={toggleFacilities}
                  className="text-darkBlue text-xs mt-1 hover:underline"
                >
                  {showAllFacilities ? "See Less" : "See More"}
                </button>
              )}
            </div>
          )}
          {/* Rate Details */}
          {/* <div className="bg-gray-50 border border-gray-200 rounded-lg p-3 space-y-2">
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-darkBlue text-sm">{config.rateBaseName}</h4>
                <div className="text-xs text-gray-600 space-y-1 mt-1">
                  {config.allowsExtraMeals && (
                    <div className="flex items-center gap-1">
                      <Check className="w-3 h-3 text-green" />
                      <span>Extra meals available</span>
                    </div>
                  )}
                  {config.allowsSpecialRequests && (
                    <div className="flex items-center gap-1">
                      <Check className="w-3 h-3 text-green-600" />
                      <span>Special requests accepted</span>
                    </div>
                  )}
                  {config.allowsBeddingPreference && (
                    <div className="flex items-center gap-1">
                      <Check className="w-3 h-3 text-green-600" />
                      <span>Bedding preference available</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div> */}
          {/* Daily Rates */}
          {/* <DailyRates
            availableDates={config.availableDates}
            isExpanded={showDailyRates}
            onToggle={() => setShowDailyRates(!showDailyRates)}
          /> */}

          {config.tariffNotes && (
            
              <div className="text-xs font-light whitespace-break-spaces">
                <div className={`${showFullNotes ? '' : 'line-clamp-3'}`}>
                  {config.tariffNotes}
                </div>
                {config.tariffNotes.length > 300 && (
                  <button
                    onClick={toggleNotes}
                    className="text-darkBlue hover:text-blue-800 underline mt-2"
                  >
                    {showFullNotes ? 'Show Less' : 'Read More'}
                  </button>
                )}
            </div>
          )}
        </div>
      </div>
      <div className="px-4 py-3 bg-smokeGray/20 flex items-center space-x-6">
        <div className="w-full justify-end text-end">
          <div className="text-xs">{config.nights}</div>
          <div className="text-base font-semibold">{config.price}</div>
          <div className="text-xs">{config.totalTax}</div>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={() =>
              handleRoomSelect(
                roomTypeIndex,
                configIndex,
                (selectedRooms[config.id] || 0) - 1
              )
            }
            disabled={(selectedRooms[config.id] || 0) <= 0}
            className="w-8 h-8 flex items-center justify-center bg-indigo-100 rounded-[8px] text-lg font-bold disabled:opacity-50"
            aria-label="Decrease room quantity"
          >
            -
          </button>
          <div className="w-8 h-8 flex items-center justify-center border border-border rounded-[8px] px-6">
            {selectedRooms[config.id] || 0}
          </div>
          <button
            onClick={() =>
              handleRoomSelect(
                roomTypeIndex,
                configIndex,
                (selectedRooms[config.id] || 0) + 1
              )
            }
            disabled={(selectedRooms[config.id] || 0) >= travelerConfig.rooms}
            className="w-8 h-8 flex items-center justify-center bg-indigo-100 rounded-[8px] text-lg font-bold disabled:opacity-50"
            aria-label="Increase room quantity"
          >
            +
          </button>
        </div>
      </div>
    </div>
  );
};

// Total Panel
const TotalPanel = ({
  travelerConfig,
  displayText,
  calculateTotalPrice,
  roomTypes,
  selectedRooms,
  handleReserve,
  bookingDetails,
}) => {
  const selectedRoomDetails = Object.entries(selectedRooms)
    .map(([roomId, count]) => {
      const [roomTypeIndex, configIndex] = roomId.split("-").map(Number);
      const roomType = roomTypes[roomTypeIndex];
      const config = roomType?.configurations[configIndex];
      if (!roomType || !config) return null;
      return {
        roomTypeName: roomType.type,
        config,
        count,
        roomId,
        roomTypeCode: roomType.roomTypeCode,
      };
    })
    .filter(Boolean);

  return (
    <div className="w-full md:w-1/4 md:block hidden">
      <div className="bg-smokeGray/10 border border-border rounded-[8px] p-4 space-y-4 shadow-sm h-auto">
        <h3 className="text-2xl font-bold">Total</h3>
        <div className="text-lg font-medium">
          CHF {calculateTotalPrice()}
          {selectedRoomDetails.length > 0 && (
            <p className="text-sm font-normal">
              {selectedRoomDetails[0].config.totalTax || "+ Taxes and Charges"}
            </p>
          )}
        </div>
        <div className="mb-2">
          <div className="text-sm">
            {travelerConfig.rooms} Room{travelerConfig.rooms > 1 ? "s" : ""} for
          </div>
          <div className="text-sm">{displayText}</div>
        </div>
        {selectedRoomDetails.length === 0 ? (
          <div className="text-sm ">No rooms selected</div>
        ) : (
          selectedRoomDetails.map(({ roomTypeName, config, count, roomId, roomTypeCode }) => (
            <div key={roomId} className="space-y-2">
              <div className="text-sm font-semibold">
                {roomTypeName} ({count} {count > 1 ? "Rooms" : "Room"})
              </div>
              {config.benefits.map((benefit, i) => (
                <div key={i} className="flex items-start">
                  <Check className="text-darkGreen mr-2 mt-1" size={16} />
                  <div>
                    <span className="text-darkGreen font-semibold text-xs">
                      {benefit.text}
                    </span>
                  </div>
                </div>
              ))}
              <CancellationRules
                rules={config.cancellationRules}
              />
              <div className="text-sm">{config.nights}</div>
              <div className="text-base font-semibold">{config.price}</div>
              <div className="text-xs">{config.totalTax}</div>
            </div>
          ))
        )}
        <ButtonCom
          variant="primary"
          size="md"
          width="full"
          disabled={selectedRoomDetails.length === 0}
          onClick={() => {
            if (selectedRoomDetails.length > 0) {
              handleReserve(
                selectedRoomDetails[0].roomTypeName,
                selectedRoomDetails[0].config,
                selectedRoomDetails[0].roomTypeCode
              );
            }
          }}
          ariaLabel="Reserve selected room"
        >
          I'll Reserve
        </ButtonCom>
      </div>
    </div>
  );
};

// Main Component
export default function AllOffer({ hotelDetails, searchCriteria }) {
  const navigate = useNavigate();
  const calendarRef = useRef(null);
  const buttonRef = useRef(null);
  const modalRef = useRef(null);
  const roomRefs = useRef({});

  // Star rating calculation (same as WellcomSection.jsx)
  const getStarCount = () => {
    if (hotelDetails.classificationBody) {
      const starsFromName = hotelDetails.classificationBody.classificationName?.match(/\*+/)?.[0]?.length;
      if (starsFromName) return starsFromName;

      const starsFromRating = hotelDetails.classificationBody.rating?.match(/(\d+)\s*Star/i)?.[1];
      if (starsFromRating) return parseInt(starsFromRating, 10);

      const classificationId = hotelDetails.classificationBody.classificationId;
      if (classificationId) {
        switch (classificationId) {
          case 559: return 1;
          case 560: return 2;
          case 561: return 3;
          case 562: return 4;
          case 563: return 5;
          default: return 0;
        }
      }
    }

    if (hotelDetails.classificationCode) {
      switch (hotelDetails.classificationCode) {
        case 559: return 1;
        case 560: return 2;
        case 561: return 3;
        case 562: return 4;
        case 563: return 5;
        default: return 0;
      }
    }

    return 0;
  };

  const getClassificationName = () => {
    if (hotelDetails.classificationBody?.classificationName) {
      return hotelDetails.classificationBody.classificationName.replace(/\*+$/, '').trim();
    }
    if (hotelDetails.classificationBody?.rating) {
      return hotelDetails.classificationBody.rating;
    }
    return hotelDetails.classificationName || 'Unrated';
  };

  const starCount = getStarCount();
  const classificationName = getClassificationName();

  const processedHotelDetails = {
    ...hotelDetails, // This spreads ALL original fields first
    starCount,
    classificationName,
    // Explicitly ensure these critical fields are preserved (don't override if they exist)
    hotelPhone: hotelDetails?.hotelPhone || hotelDetails?.phone || hotelDetails?.phoneNumber || 'N/A',
    regionName: hotelDetails?.regionName || hotelDetails?.region || hotelDetails?.city || 'N/A',
  };

  // Debug logging to check what fields are available
  console.log('AllOffer - Original hotelDetails:', hotelDetails);
  console.log('AllOffer - Original hotelPhone:', hotelDetails?.hotelPhone);
  console.log('AllOffer - Original regionName:', hotelDetails?.regionName);
  console.log('AllOffer - processedHotelDetails:', processedHotelDetails);
  console.log('AllOffer - Final hotelPhone:', processedHotelDetails.hotelPhone);
  console.log('AllOffer - Final regionName:', processedHotelDetails.regionName);

  const [selectedHeader, setSelectedHeader] = useState(null);
  const [selectedRooms, setSelectedRooms] = useState({});
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [activeSection, setActiveSection] = useState(null);
  const [showTravelersModal, setShowTravelersModal] = useState(false);
  const [showRoomSelectorId, setShowRoomSelectorId] = useState(null);
  const [travelerConfig, setTravelerConfig] = useState({
    rooms: searchCriteria?.rooms || 1,
    adults: searchCriteria?.adults || 2,
    children: searchCriteria?.children || 0,
  });
  const [dateRange, setDateRange] = useState({
    startDate: searchCriteria?.dateRange?.startDate || new Date("2025-05-25"),
    endDate: searchCriteria?.dateRange?.endDate || new Date("2025-05-28"),
    key: "selection",
  });
  const [flexibleDays, setFlexibleDays] = useState(0);

  const calculateNightsAndTravelers = () => {
    let nights = 1;
    let dateRangeText = "Select dates";
    try {
      const startDate = new Date(dateRange.startDate);
      const endDate = new Date(dateRange.endDate);
      if (startDate && endDate && !isNaN(startDate) && !isNaN(endDate)) {
        if (endDate >= startDate) {
          nights = Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24));
          const formatter = new Intl.DateTimeFormat("en-US", {
            month: "short",
            day: "2-digit",
            year: "numeric",
          });
          dateRangeText = `${formatter.format(startDate)} - ${formatter.format(endDate)}`;
        } else {
          console.warn("Invalid date range: endDate is before startDate");
          dateRangeText = "Invalid dates";
        }
      }
    } catch (error) {
      console.error("Error calculating nights:", error);
    }
    const adults = travelerConfig.adults;
    const children = travelerConfig.children;
    const travelersText = [
      adults > 0 ? `${adults} ${adults === 1 ? "Adult" : "Adults"}` : "",
      children > 0 ? `${children} ${children === 1 ? "Child" : "Children"}` : "",
    ]
      .filter(Boolean)
      .join(", ");
    return {
      nights: Math.max(1, nights),
      dateRangeText,
      displayText: `${nights} ${nights === 1 ? "night" : "nights"}, ${travelersText}`,
    };
  };

  // Memoized calculations for performance
  const { nights, dateRangeText, displayText } = useMemo(() => {
    return calculateNightsAndTravelers();
  }, [dateRange, travelerConfig]);

  const processRoomTypes = useCallback((nights) => {
    if (!hotelDetails?.rooms || hotelDetails.rooms.length === 0) {
      console.warn("No rooms available in hotelDetails:", hotelDetails);
      return [];
    }

    return hotelDetails.rooms.map((roomType, index) => ({
      id: roomType.roomTypeCode || index,
      type: roomType.roomTypeName || "Unknown Room Type",
      singleRoom: roomType.twin === "yes" ? 2 : 1,
      description: roomType.roomInfo
        ? `Max ${roomType.roomInfo.maxOccupancy} guests`
        : "No description available.",
      roomTypeCode: roomType.roomTypeCode || `ROOM_${index}`,
      configurations: roomType.rateBases?.map((rate, configIndex) => {
        const mealPlans = rate.availableDates?.flatMap((date) =>
          date.mealIncludes?.map((meal) => meal.mealName || meal.mealType) || []
        ) || [];
        const uniqueMealPlans = [...new Set(mealPlans)];

        return {
          id: `${index}-${configIndex}`,
          guestConfig: {
            adults: travelerConfig.adults,
            children: travelerConfig.children,
            childAge: 6,
          },
          price: rate.totalCharge ? `CHF ${rate.totalCharge.toFixed(2)}` : "N/A",
          priceDetails: rate.totalTaxes ? `+ CHF ${parseFloat(rate.totalTaxes).toFixed(2)} Taxes and Charges` : "+ CHF 0.00 Taxes and Charges",
          benefits: [
            {
              text: rate.rateBaseName || "Room Only",
              included: !!rate.rateBaseName,
            },
          ],
          nights: `${nights} ${nights === 1 ? "night" : "nights"}`,
          totalPrice: rate.totalCharge ? `CHF ${rate.totalCharge.toFixed(2)}` : "N/A",
          totalTax: rate.totalTaxes ? `+ CHF ${parseFloat(rate.totalTaxes).toFixed(2)} Taxes and Charges` : "+ CHF 0.00 Taxes and Charges",
          leftToSell: rate.leftToSell || 3,
          rateBaseId: rate.rateBaseId || 0,
          tariffNotes: rate.tariffNotes || "",
          totalTaxes: rate.totalTaxes || "0.00",
          allocationDetails: rate.allocationDetails || "",
          cancellationRules: rate.cancellationRules || [],
          availableDates: rate.availableDates || [],
          specials: rate.specials || [],
        };
      }) || [],
    }));
  }, [hotelDetails?.rooms]);

  const roomTypes = useMemo(() => processRoomTypes(nights), [processRoomTypes, nights]);

  const calculateTotalPrice = () => {
    if (!roomTypes || !Array.isArray(roomTypes)) {
      console.warn("roomTypes is not defined or not an array:", roomTypes);
      return "0.00";
    }
    let total = 0;
    Object.entries(selectedRooms).forEach(([roomId, count]) => {
      const [roomTypeIndex, configIndex] = roomId.split("-").map(Number);
      const config = roomTypes[roomTypeIndex]?.configurations[configIndex];
      if (config && config.totalPrice !== "N/A") {
        const price = parseFloat(config.totalPrice.replace("CHF ", ""));
        total += price * count;
      }
    });
    return total.toFixed(2);
  };

  const toggleCalendar = () => {
    setShowDatePicker((prev) => !prev);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        calendarRef.current &&
        !calendarRef.current.contains(event.target) &&
        buttonRef.current &&
        !buttonRef.current.contains(event.target)
      ) {
        setShowDatePicker(false);
      }
      if (modalRef.current && !modalRef.current.contains(event.target)) {
        setShowTravelersModal(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  useEffect(() => {
    setTravelerConfig({
      rooms: searchCriteria?.rooms || 1,
      adults: searchCriteria?.adults || 2,
      children: searchCriteria?.children || 0,
    });
    setDateRange({
      startDate: searchCriteria?.dateRange?.startDate || new Date("2025-05-25"),
      endDate: searchCriteria?.dateRange?.endDate || new Date("2025-05-28"),
      key: "selection",
    });
    setFlexibleDays(searchCriteria?.flexibleDays || 0);
  }, [searchCriteria]);

  useEffect(() => {
    setSelectedRooms({});
  }, [hotelDetails]);

  // Intersection Observer for automatic tab highlighting during manual scroll
  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '-20% 0px -70% 0px', // Trigger when room is in the middle portion of viewport
      threshold: 0.1
    };

    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const roomId = entry.target.id.replace('room-', '');
          if (roomId && roomId !== selectedHeader) {
            setSelectedHeader(roomId);
          }
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);

    // Observe all room elements
    Object.values(roomRefs.current).forEach((roomElement) => {
      if (roomElement) {
        observer.observe(roomElement);
      }
    });

    return () => {
      observer.disconnect();
    };
  }, [roomTypes, selectedHeader]);

  const getSectionBorderStyle = (sectionName) =>
    activeSection === sectionName
      ? "border border-gray/35"
      : "border border-transparent";

  const handleRoomSelect = (roomTypeIndex, configIndex, newCount) => {
    const roomId = `${roomTypeIndex}-${configIndex}`;
    setSelectedRooms((prev) => {
      const updated = { ...prev };
      if (newCount <= 0) {
        delete updated[roomId];
      } else if (newCount <= travelerConfig.rooms) {
        updated[roomId] = newCount;
      }
      return updated;
    });
  };

  const handleTravelerChange = (field, value) => {
    setTravelerConfig((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleConfirmTravelers = () => {
    console.log("Traveler Selection:", travelerConfig);
    setShowTravelersModal(false);
  };

  const handleReserve = (roomTypeName, config, roomTypeCode) => {
    const totalPrice = calculateTotalPrice();
    if (!totalPrice || totalPrice === "0.00") {
      alert("Please select a valid room with pricing.");
      return;
    }
    const selectedRoom = {
      roomTypeName,
      totalCharge: parseFloat(totalPrice) || 0,
      rateBaseId: config.rateBaseId || 0,
      roomTypeCode: roomTypeCode,
      allocationDetails: config.allocationDetails || "",
      isBookable: config.isBookable !== undefined ? config.isBookable : true,
      benefits: config.benefits || [],
      tariffNotes: config.tariffNotes || "",
      totalTaxes: config.totalTaxes || "0.00",
      selectedRoomCounts: selectedRooms,
      cancellationRules: config.cancellationRules || [],
      availableDates: config.availableDates || [],
      rateBaseName: config.rateBaseName || 'Room Only',
      currencyCode: config.currencyCode || 'USD',
    };
    const bookingDetails = {
      hotelDetails: processedHotelDetails,
      searchCriteria: {
        ...searchCriteria,
        dateRange,
        adults: travelerConfig.adults,
        children: travelerConfig.children,
        rooms: travelerConfig.rooms,
      },
      selectedRoom,
      nights,
      dateRangeText,
    };
    console.log('AllOffer - bookingDetails:', bookingDetails);
    navigate("/finaldetails", { state: { bookingDetails } });
  };

  const scrollToRoom = useCallback((roomId) => {

    if (window.scrollTimeout) {
      clearTimeout(window.scrollTimeout);
    }

    window.scrollTimeout = setTimeout(() => {
      const roomElement = roomRefs.current[roomId];
      if (roomElement) {
        const isMobile = window.innerWidth < 768;
        const headerHeight = isMobile ? 120 : 80; 
        const tabsHeight = isMobile ? 60 : 50;
        const totalOffset = headerHeight + tabsHeight + 20; 
        const elementRect = roomElement.getBoundingClientRect();
        const absoluteElementTop = elementRect.top + window.pageYOffset;
        const targetPosition = absoluteElementTop - totalOffset;
        const finalPosition = Math.max(0, targetPosition);
        const startPosition = window.pageYOffset;
        const distance = finalPosition - startPosition;
        const duration = 800; 
        let startTime = null;

        const animateScroll = (currentTime) => {
          if (startTime === null) startTime = currentTime;
          const timeElapsed = currentTime - startTime;
          const progress = Math.min(timeElapsed / duration, 1);
          const easeInOutCubic = progress =>
            progress < 0.5
              ? 4 * progress * progress * progress
              : (progress - 1) * (2 * progress - 2) * (2 * progress - 2) + 1;

          const currentPosition = startPosition + (distance * easeInOutCubic(progress));
          window.scrollTo(0, currentPosition);

          if (progress < 1) {
            requestAnimationFrame(animateScroll);
          } else {
            setSelectedHeader(roomId);
            roomElement.style.transform = 'scale(1.02)';
            roomElement.style.transition = 'transform 0.3s ease';
            setTimeout(() => {
              roomElement.style.transform = 'scale(1)';
              setTimeout(() => {
                roomElement.style.transition = '';
              }, 300);
            }, 200);
          }
        };

        requestAnimationFrame(animateScroll);
        if (!window.requestAnimationFrame) {
          window.scrollTo({
            top: finalPosition,
            behavior: 'smooth'
          });
          setSelectedHeader(roomId);
        }

      } else {
        console.warn(`Room element with ID room-${roomId} not found`);
      }
    }, 100);
  }, []);

  const handleChangeSearch = () => {
    const updatedSearchCriteria = {
      ...searchCriteria,
      dateRange: {
        startDate: dateRange.startDate,
        endDate: dateRange.endDate,
      },
      adults: travelerConfig.adults,
      children: travelerConfig.children,
      rooms: travelerConfig.rooms,
      flexibleDays,
    };
    navigate(`/hotel-and-flight/${hotelDetails.id}`, {
      state: {
        formData: updatedSearchCriteria,
        previousSearch: searchCriteria,
      },
    });
  };

  const selectedRoomDetails = Object.entries(selectedRooms)
    .map(([roomId, count]) => {
      const [roomTypeIndex, configIndex] = roomId.split("-").map(Number);
      const roomType = roomTypes[roomTypeIndex];
      const config = roomType?.configurations[configIndex];
      if (!roomType || !config) return null;
      return {
        roomTypeName: roomType.type,
        config,
        count,
        roomId,
        roomTypeId: roomType.id,
        roomTypeCode: roomType.roomTypeCode,
      };
    })
    .filter(Boolean);

  if (!hotelDetails || !hotelDetails.rooms) {
    console.warn("hotelDetails or hotelDetails.rooms is undefined:", hotelDetails);
    return (
      <div className="text-center text-gray-600">
        Loading hotel details or no rooms available...
      </div>
    );
  }

  return (
    <div className="w-full space-y-0 md:space-y-8">
      <div className="mb-8 px-6 py-3 md:px-0 md:py-0">
        <h2 className="text-2xl font-semibold text-darkBlue mb-6">Availability</h2>
        <div className="flex flex-col md:flex-row flex-wrap gap-2 justify-start md:justify-between p-2 rounded-[8px] border border-border max-w-[820px] w-full">
          <div className="flex flex-col md:flex-row md:w-[70%] border border-border bg-white items-center rounded-[8px] justify-between">
            <div
              className="flex flex-col relative w-full"
              onClick={() => setActiveSection("DateRange")}
            >
              <div
                className={`flex justify-between items-center w-full py-[15px] rounded-[8px] ${getSectionBorderStyle(
                  "DateRange"
                )}`}
              >
                <div className="hidden md:block absolute top-2 bottom-2 right-0 w-[0.5px] bg-border"></div>
                <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                  <Calendar
                    className="text-smokyGray w-6 h-6 flex-shrink-0"
                    strokeWidth={1}
                  />
                  <div className="flex flex-col w-full">
                    <button
                      type="button"
                      ref={buttonRef}
                      onClick={toggleCalendar}
                      className="text-sm font-light text-black focus:outline-none text-left w-full"
                      aria-label="Select dates"
                    >
                      {dateRange?.startDate && dateRange?.endDate
                        ? `${new Date(dateRange.startDate).toLocaleDateString(
                          "en-US",
                          { month: "short", day: "2-digit", year: "numeric" }
                        )} - ${new Date(dateRange.endDate).toLocaleDateString(
                          "en-US",
                          { month: "short", day: "2-digit", year: "numeric" }
                        )}`
                        : "Select dates"}
                    </button>
                  </div>
                </div>
                {showDatePicker && (
                  <div
                    ref={calendarRef}
                    className="absolute z-50 mt-12 left-0 md:-left-12 shadow-lg w-full md:w-auto transform -translate-x-0 md:-translate-x-1/4"
                  >
                    <HotelCalendarComponent
                      dateRange={dateRange}
                      setDateRange={setDateRange}
                      onClose={() => setShowDatePicker(false)}
                      flexibleDays={flexibleDays}
                      setFlexibleDays={setFlexibleDays}
                    />
                  </div>
                )}
              </div>
            </div>
            <div
              className="flex flex-col relative w-full"
              onClick={() => setActiveSection("Travelers")}
            >
              <div
                className={`flex justify-between items-center w-full py-[15px] rounded-[8px] ${getSectionBorderStyle(
                  "Travelers"
                )}`}
                onClick={() => setShowTravelersModal(true)}
              >
                <div className="flex flex-row items-center space-x-4 ml-3 w-full">
                  <UserRound
                    className="text-smokyGray w-6 h-6 flex-shrink-0"
                    strokeWidth={1}
                  />
                  <div className="flex flex-col w-full">
                    <span className="text-sm font-light text-black focus:outline-none">
                      {travelerConfig.adults} Adults {travelerConfig.children}{" "}
                      Children · {travelerConfig.rooms} Room
                    </span>
                  </div>
                </div>
                {showTravelersModal && (
                  <div
                    className="absolute z-50 top-full right-0 sm:left-auto bg-white border border-border shadow-lg p-4 rounded-[8px] w-[280px]"
                    ref={modalRef}
                    onClick={(e) => e.stopPropagation()}
                  >
                    <div className="flex flex-col space-y-4">
                      <span className="flex justify-center text-base text-black border-b border-border pb-4">
                        Rooms & Travelers
                      </span>
                      <div className="flex items-center justify-between">
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "rooms",
                              Math.max(1, travelerConfig.rooms - 1)
                            )
                          }
                          disabled={travelerConfig.rooms <= 1}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.rooms <= 1
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Decrease rooms"
                        >
                          <Minus className="w-3 h-3" />
                        </button>
                        <div className="text-center">
                          <span className="text-sm font-light text-darkGray">
                            {travelerConfig.rooms} Room
                          </span>
                        </div>
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "rooms",
                              Math.min(5, travelerConfig.rooms + 1)
                            )
                          }
                          disabled={travelerConfig.rooms >= 5}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.rooms >= 5
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Increase rooms"
                        >
                          <Plus className="w-3 h-3" />
                        </button>
                      </div>
                      <div className="flex items-center justify-between">
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "adults",
                              Math.max(1, travelerConfig.adults - 1)
                            )
                          }
                          disabled={travelerConfig.adults <= 1}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.adults <= 1
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Decrease adults"
                        >
                          <Minus className="w-3 h-3" />
                        </button>
                        <div className="text-center">
                          <span className="text-sm font-light text-darkGray">
                            {travelerConfig.adults} Adult
                          </span>
                          <div className="text-smokyGray text-[10px] font-light">
                            Ages 12+
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            const total = travelerConfig.adults + travelerConfig.children;
                            if (total < 9)
                              handleTravelerChange("adults", travelerConfig.adults + 1);
                          }}
                          disabled={travelerConfig.adults + travelerConfig.children >= 9}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.adults + travelerConfig.children >= 9
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Increase adults"
                        >
                          <Plus className="w-3 h-3" />
                        </button>
                      </div>
                      <div className="flex items-center justify-between">
                        <button
                          type="button"
                          onClick={() =>
                            handleTravelerChange(
                              "children",
                              Math.max(0, travelerConfig.children - 1)
                            )
                          }
                          disabled={travelerConfig.children <= 0}
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.children <= 0
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Decrease children"
                        >
                          <Minus className="w-3 h-3" />
                        </button>
                        <div className="text-center">
                          <span className="text-sm font-light text-darkGray">
                            {travelerConfig.children} Child
                          </span>
                          <div className="text-smokyGray text-[10px] font-light">
                            Ages 2-11
                          </div>
                        </div>
                        <button
                          type="button"
                          onClick={() => {
                            const total = travelerConfig.adults + travelerConfig.children;
                            if (total < 9)
                              handleTravelerChange(
                                "children",
                                travelerConfig.children + 1
                              );
                          }}
                          disabled={
                            travelerConfig.adults + travelerConfig.children >= 9 ||
                            travelerConfig.children >= 6
                          }
                          className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${travelerConfig.adults + travelerConfig.children >= 9 ||
                            travelerConfig.children >= 6
                            ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                            : ""
                            }`}
                          aria-label="Increase children"
                        >
                          <Plus className="w-3 h-3" />
                        </button>
                      </div>
                      <ButtonCom
                        onClick={handleConfirmTravelers}
                        width="full"
                        size="md"
                        variant="primary"
                        rounded="md"
                        ariaLabel="Confirm traveler selection"
                      >
                        Confirm
                      </ButtonCom>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
          <div className="flex w-full md:w-[25%]">
            <ButtonCom
              variant="primary"
              size="md"
              width="full"
              icon="search"
              iconPosition="left"
              onClick={handleChangeSearch}
              type="button"
            >
              Change Search
            </ButtonCom>
          </div>
        </div>
      </div>
      <div className="space-y-6 scroll-smooth">
        {roomTypes.length === 0 ? (
          <div className="text-center text-gray-600 py-12">
            <Bed className="w-16 h-16 mx-auto mb-4 text-gray-400" />
            <h3 className="text-lg font-semibold mb-2">No rooms available</h3>
            <p>No rooms available for the selected dates. Please try different dates.</p>
          </div>
        ) : (
          <>
            <RoomTabs
              roomTypes={roomTypes}
              selectedHeader={selectedHeader}
              setSelectedHeader={setSelectedHeader}
              scrollToRoom={scrollToRoom}
            />
            <div className="flex flex-col md:flex-row gap-4">
              <div className="hidden md:block relative chart-container pb-16 space-y-6 w-full md:w-3/4">
                {roomTypes.map((roomType, roomTypeIndex) => (
                  <div
                    key={roomType.id}
                    id={`room-${roomType.id}`}
                    ref={(el) => (roomRefs.current[roomType.id] = el)}
                    className="space-y-4"
                  >
                    {roomType.configurations.map((config, configIndex) => (
                      <RoomListing
                        key={config.id}
                        roomType={roomType}
                        config={config}
                        configIndex={configIndex}
                        roomTypeIndex={roomTypeIndex}
                        selectedRooms={selectedRooms}
                        handleRoomSelect={handleRoomSelect}
                        travelerConfig={travelerConfig}
                        hotelDetails={hotelDetails}
                      />
                    ))}
                  </div>
                ))}
              </div>
              <TotalPanel
                travelerConfig={travelerConfig}
                displayText={displayText}
                calculateTotalPrice={calculateTotalPrice}
                roomTypes={roomTypes}
                selectedRooms={selectedRooms}
                handleReserve={handleReserve}
                bookingDetails={{
                  hotelDetails: processedHotelDetails,
                  searchCriteria,
                  nights,
                  dateRangeText,
                }}
              />
            </div>
          </>
        )}
      </div>
      {/* Mobile view */}
      <div className="flex flex-col h-screen md:hidden">
        <div className="chart-container overflow-y-auto flex-1 px-6 py-3 md:px-0 md:py-0 space-y-4">
          {roomTypes.map((roomType, roomTypeIndex) => (
            <div
              key={roomType.id}
              id={`room-${roomType.id}`}
              ref={(el) => (roomRefs.current[roomType.id] = el)}
              className="border border-border rounded-[8px] p-4 bg-white shadow-md"
            >
              <h3 className="text-lg font-semibold text-darkBlue">{roomType.type}</h3>
              <p className="text-sm text-gray-600 mt-1">{roomType.description}</p>
              <div className="flex mt-1">
                {Array(travelerConfig.adults)
                  .fill(0)
                  .map((_, i) => (
                    <UserRound key={`adult-${i}`} className="w-5 h-5 mr-1" />
                  ))}
                {travelerConfig.children > 0 &&
                  Array(travelerConfig.children)
                    .fill(0)
                    .map((_, i) => (
                      <UserRound key={`child-${i}`} className="w-4 h-4 mr-1" />
                    ))}
              </div>
              <div className="mt-4 space-y-2">
                {roomType.configurations.map((config, configIndex) => (
                  <div
                    key={config.id}
                    className="border border-border p-3 rounded-[8px] space-y-3"
                  >
                    <div className="flex flex-col justify-between items-start mb-2">
                      <p className="text-sm font-medium">{config.price}</p>
                      <p className="text-xs font-light">{config.priceDetails}</p>
                    </div>
                    <div className="text-xs">{config.nights}</div>
                    <ul className="mt-2 space-y-2">
                      {config.benefits.map((benefit, i) => (
                        <li
                          key={i}
                          className="text-xs text-darkGreen flex items-center gap-1"
                        >
                          <Check size={14} strokeWidth={1} /> {benefit.text}
                        </li>
                      ))}
                    </ul>
                    <CancellationRules
                      rules={config.cancellationRules}
                    />
                    {selectedRooms[config.id] > 0 ? (
                      <>
                        <button
                          className="mt-3 w-full bg-darkBlue text-white text-sm py-2 rounded-[8px]"
                          onClick={() =>
                            setShowRoomSelectorId(
                              showRoomSelectorId === config.id ? null : config.id
                            )
                          }
                          aria-label={`Selected ${selectedRooms[config.id]} rooms`}
                        >
                          Selected ({selectedRooms[config.id]}{" "}
                          {selectedRooms[config.id] === 1 ? "Room" : "Rooms"})
                        </button>
                        {showRoomSelectorId === config.id && (
                          <div className="mt-2 border border-border p-3 rounded-[8px] bg-[#f9f9f9] space-y-3">
                            <div className="flex items-center justify-between">
                              <button
                                type="button"
                                onClick={() =>
                                  handleRoomSelect(
                                    roomTypeIndex,
                                    configIndex,
                                    (selectedRooms[config.id] || 0) - 1
                                  )
                                }
                                disabled={(selectedRooms[config.id] || 0) <= 0}
                                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${(selectedRooms[config.id] || 0) <= 0
                                  ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                                  : ""
                                  }`}
                                aria-label="Decrease room quantity"
                              >
                                <Minus className="w-3 h-3" />
                              </button>
                              <span className="text-sm font-light text-darkGray">
                                {selectedRooms[config.id] || 0} Room
                              </span>
                              <button
                                type="button"
                                onClick={() =>
                                  handleRoomSelect(
                                    roomTypeIndex,
                                    configIndex,
                                    (selectedRooms[config.id] || 0) + 1
                                  )
                                }
                                disabled={
                                  (selectedRooms[config.id] || 0) >= travelerConfig.rooms
                                }
                                className={`flex justify-center items-center w-7 h-7 bg-[#E6EDF2] text-smokyGray rounded-[8px] ${(selectedRooms[config.id] || 0) >= travelerConfig.rooms
                                  ? "cursor-not-allowed bg-opacity-60 text-smokyGray/60"
                                  : ""
                                  }`}
                                aria-label="Increase room quantity"
                              >
                                <Plus className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <button
                        className="mt-3 w-full bg-darkBlue text-white text-sm py-2 rounded-[8px]"
                        onClick={() => {
                          handleRoomSelect(roomTypeIndex, configIndex, 1);
                          setShowRoomSelectorId(config.id);
                        }}
                        aria-label="Select room"
                      >
                        Select
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
        <div className="sticky bottom-0 top-10 bg-offWhite z-30 border-t border-border px-4 py-3">
          <div className="max-h-[200vh] space-y-3">
            <div className="space-y-2 shadow-sm">
              {selectedRoomDetails.length === 0 ? (
                <div className="text-xs">No rooms selected</div>
              ) : (
                <>
                  <h3 className="text-xs font-normal">Selected Room</h3>
                  {selectedRoomDetails.map(({ roomTypeName, config, count, roomTypeId }) => (
                    <div key={roomTypeId}>
                      <div
                        className="text-xs font-medium text-darkBlue cursor-pointer hover:underline"
                        onClick={() => scrollToRoom(roomTypeId)}
                      >
                        {roomTypeName} ({count} {count > 1 ? "Rooms" : "Room"})
                      </div>
                    </div>
                  ))}
                </>
              )}
              <div className="text-xs">
                {Object.values(selectedRooms).reduce((sum, count) => sum + count, 0)} Room
                {Object.values(selectedRooms).reduce((sum, count) => sum + count, 0) !== 1 ? "s" : ""} for
              </div>
              <div className="text-xs">{displayText}</div>
              <hr className="border-border" />
              <div className="text-sm font-medium w-full text-end">
                CHF {calculateTotalPrice()}
                {selectedRoomDetails.length > 0 && (
                  <p className="text-xs font-normal">
                    {selectedRoomDetails[0].config.totalTax || "+ Taxes and Charges"}
                  </p>
                )}
              </div>
              <div className="flex justify-between items-center pt-4">
                <ButtonCom
                  variant="primary"
                  size="md"
                  width="full"
                  disabled={selectedRoomDetails.length === 0}
                  onClick={() => {
                    if (selectedRoomDetails.length > 0) {
                      handleReserve(
                        selectedRoomDetails[0].roomTypeName,
                        selectedRoomDetails[0].config,
                        selectedRoomDetails[0].roomTypeCode
                      );
                    }
                  }}
                  ariaLabel="Reserve selected room"
                >
                  Reserve
                </ButtonCom>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}