import React from "react";
import TrendingAdventures from "../../home/<USER>";
import CardAndCity from "../../home/<USER>";
import AdventureIntro from "../AdventureIntro";
import Expect from "../Expect";
import Feedback from "../../../components/common/feedback/Feedback";
import AdventureWorld from "./AdventureWorld";

import SoloImage from "../../../assets/adventure/Solo.svg";
import SeaniorImage1 from "../../../assets/adventure/SeaniorImage1.svg";
import SeaniorImage2 from "../../../assets/adventure/SeaniorImage2.svg";
import SeaniorImage3 from "../../../assets/adventure/SeaniorImage3.svg";
import SeaniorImage4 from "../../../assets/adventure/SeaniorImage4.svg";
import {isEqual} from "lodash";
const data = [SeaniorImage1, <PERSON><PERSON>Image2, <PERSON><PERSON><PERSON>mage3, <PERSON><PERSON>Image4];

const paragraph =
  "Who needs a playground when there’s a whole world to play in? Embark on a fun, family adventure in the Canadian Rockies, see colorful Colombia through the eyes of <PERSON><PERSON><PERSON>, or head on a family safari for a Lion King-inspired adventure. With activities that even the fussiest teenager will love, and child-friendly resorts (and spas!), family tours take the stress away so you spend less time planning and more time discovering.";

const SoloAdventure = () => {
  return (
    <>
      <div className="space-y-20 mx-2">
        <AdventureIntro
          image={SoloImage}
          title="Solo Adventures"
          paragraph={paragraph}
        />
        <TrendingAdventures title="Trending Solo Adventures" />
        <CardAndCity title="Solo Packages" />
        <AdventureWorld title="Solo tour around the world" />
        <Expect title="What to Expect on a Solo vacation " data={data} />
        <Feedback title="Feedback and Review About Solo Adventure" />
      </div>
    </>
  );
};

export default SoloAdventure;
