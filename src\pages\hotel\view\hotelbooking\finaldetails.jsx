import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Formik, Form } from 'formik';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { CarFront, Check, CircleCheck, Users, ChevronRight } from 'lucide-react';
import * as Yup from 'yup';
import { toast, ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { useDispatch, useSelector } from 'react-redux';
import TrackingStepper from './TrackingStepper';
import PriceSummary from './pricesummary';
import HotelDetails from './HotelDetails';
import SignInPrompt from '../../../booking/SignInPrompt';
import PaymentMethods from './PaymentMethods';
import Persondetails from './Persondetails';
import ButtonCom from '../../../../components/ui/button/ButtonCom';
import { getRoomsWithBlocking, saveBooking, bookItineraryWithConfirmNo, bookItineraryWithConfirmYes } from '../../../../store/hotelSlice';
import { parsePhoneNumberFromString } from 'libphonenumber-js';

// Constants
const INITIAL_BOOKING_STATUS = {
  saving: false,
  saved: false,
  confirmingNo: false,
  confirmedNo: false,
  confirmingYes: false,
  confirmedYes: false,
  error: null,
};

const titleOptions = [
  { id: "147", name: "Mr." },
  { id: "148", name: "Ms." },
  { id: "149", name: "Mrs." },
  { id: "15134", name: "Miss" },
  { id: "558", name: "Dr." },
  { id: "1671", name: "Madame" },
  { id: "1328", name: "Sir" },
  { id: "3801", name: "Sir/Madam" },
  { id: "14632", name: "Child" },
  { id: "9234", name: "Messrs." },
  { id: "74185", name: "Monsieur" },
  { id: "74195", name: "Mademoiselle" },
];

const VALIDATION_SCHEMA = Yup.object({
  passengers: Yup.array().of(
    Yup.object().shape({
      firstName: Yup.string().required('First name is required'),
      lastName: Yup.string().required('Last name is required'),
      title: Yup.string()
        .notOneOf(['Select here'], 'Please select a valid title (e.g., Mr., Mrs.)')
        .required('Title is required')
        .oneOf(
          titleOptions.map((option) => option.name),
          'Please select a valid title'
        ),
      email: Yup.string()
        .email('Enter a valid email (e.g., <EMAIL>)')
        .when('$isMainGuest', {
          is: true,
          then: Yup.string().required('Email is required for the main guest'),
        }),
      phoneNumber: Yup.string()
        .matches(/^\d{7,15}$/, 'Phone number must be 7-15 digits')
        .when('$isMainGuest', {
          is: true,
          then: Yup.string().required('Phone number is required for the main guest'),
        }),
      phoneCode: Yup.string().when('$isMainGuest', {
        is: true,
        then: Yup.string().required('Phone country code is required'),
      }),
      streetName: Yup.string().when('$isMainGuest', {
        is: true,
        then: Yup.string().required('Street name is required'),
      }),
      houseNumber: Yup.string().when('$isMainGuest', {
        is: true,
        then: Yup.string().required('House number is required'),
      }),
      postalCode: Yup.string().when('$isMainGuest', {
        is: true,
        then: Yup.string().required('Postal code is required'),
      }),
      city: Yup.string().when('$isMainGuest', {
        is: true,
        then: Yup.string().required('City is required'),
      }),
      country: Yup.string().when('$isMainGuest', {
        is: true,
        then: Yup.string().required('Country is required'),
      }),
      countryCode: Yup.string().when('$isMainGuest', {
        is: true,
        then: Yup.string().required('Country code is required'),
      }),
      nationality: Yup.string().when(['$isMainGuest', 'sameNationality'], {
        is: (isMainGuest, sameNationality) => isMainGuest && !sameNationality,
        then: Yup.string().required('Nationality is required'),
      }),
      nationalityCode: Yup.string().when(['$isMainGuest', 'sameNationality'], {
        is: (isMainGuest, sameNationality) => isMainGuest && !sameNationality,
        then: Yup.string().required('Nationality code is required'),
      }),
    })
  ).min(1, 'At least one guest is required'),
});

const DEFAULT_BOOKING_DETAILS = {
  hotelDetails: {},
  searchCriteria: { adults: 1 },
  selectedRoom: {},
  nights: 1,
  dateRangeText: 'Select dates',
};

// Utility Functions
const formatDate = (date) => {
  return date ? new Date(date).toISOString().split('T')[0] : '2025-09-01';
};

const getFormattedCancelDate = (startDate) => {
  const cancelDate = new Date(startDate || '2025-05-25');
  cancelDate.setDate(cancelDate.getDate() - 1);
  return new Intl.DateTimeFormat('en-US', { month: 'long', day: '2-digit', year: 'numeric' }).format(cancelDate);
};

// Components
const GoodToKnow = ({ bookingDetails }) => {
  const { searchCriteria } = bookingDetails || {};
  const cancelDateText = useMemo(() => getFormattedCancelDate(searchCriteria?.dateRange?.startDate), [searchCriteria?.dateRange?.startDate]);

  return (
    <div className="w-full p-6 mb-6 mx-auto border border-darkBlue rounded-2xl bg-white">
      <div className="flex flex-col gap-4 items-start">
        <h1 className="font-medium text-lg">Good to know:</h1>
        {[
          `Stay flexible: You can cancel for free before ${cancelDateText}, so lock in this great price today.`,
          "No payment needed today. You'll pay when you stay.",
        ].map((text, idx) => (
          <div key={idx} className="flex flex-row items-start space-x-3">
            <CircleCheck className="w-5 h-5 text-darkGreen" strokeWidth={1} />
            <p className="text-sm text-smokyGray font-light mb-2">{text}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

// Main Component
const FinalDetails = ({ onNext }) => {
  const { state } = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const {
    blockedRoomData,
    blockedRoomLoading,
    blockedRoomError,
    bookingData,
    bookingLoading,
    bookingError,
    itineraryNoConfirmData,
    itineraryNoConfirmLoading,
    itineraryNoConfirmError,
    itineraryConfirmData,
    itineraryConfirmLoading,
    itineraryConfirmError,
  } = useSelector((state) => state.hotels);

  const bookingDetails = state?.bookingDetails || DEFAULT_BOOKING_DETAILS;
  const passengerCount = bookingDetails.searchCriteria?.adults || 1;

  // Debug logging to check what's received in finaldetails
  console.log('FinalDetails - Received bookingDetails:', bookingDetails);
  console.log('FinalDetails - hotelDetails:', bookingDetails.hotelDetails);
  console.log('FinalDetails - hotelPhone:', bookingDetails.hotelDetails?.hotelPhone);
  console.log('FinalDetails - regionName:', bookingDetails.hotelDetails?.regionName);

  const [savedPassengerValues, setSavedPassengerValues] = useState(null);
  const [selectChecked, setSelectChecked] = useState({ terms: false, documents: false });
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [roomBlocked, setRoomBlocked] = useState(false);
  const [bookingCode, setBookingCode] = useState(null);
  const [serviceCode, setServiceCode] = useState(null);
  const [bookingStatus, setBookingStatus] = useState(INITIAL_BOOKING_STATUS);
  const [fieldErrors, setFieldErrors] = useState(null);

  const defaultPassengerValues = useMemo(() =>
    Array(passengerCount).fill().map(() => ({
      firstName: '',
      lastName: '',
      title: 'Mr',
      email: '',
      phoneNumber: '',
      phoneCode: '',
      streetName: '',
      houseNumber: '',
      postalCode: '',
      city: '',
      country: '',
      countryCode: '',
      state: '',
      stateCode: '',
      nationality: '',
      nationalityCode: '',
      termsChecked: false,
      bookingFor: '',
      travelPurpose: '',
      sameNationality: false,
    })), [passengerCount]);

  const initialValues = useMemo(() => ({
    passengers: savedPassengerValues?.passengers || defaultPassengerValues,
  }), [savedPassengerValues, defaultPassengerValues]);

  // Handlers
  const blockRoom = useCallback(async () => {
    const requiredFields = [
      bookingDetails?.hotelDetails?.id,
      bookingDetails?.selectedRoom?.roomTypeCode,
      bookingDetails?.selectedRoom?.allocationDetails,
    ];
    if (requiredFields.some(field => !field)) {
      toast.error('Invalid room selection. Please select a valid room.');
      setRoomBlocked(false);
      return false;
    }

    const requestBody = {
      fromDate: formatDate(bookingDetails.searchCriteria.dateRange?.startDate),
      toDate: formatDate(bookingDetails.searchCriteria.dateRange?.endDate),
      currencyCode: '394',
      hotelId: parseInt(bookingDetails.hotelDetails.id),
      roomCount: bookingDetails.searchCriteria.rooms || 1,
      roomRequests: [
        {
          adultsCount: bookingDetails.searchCriteria.adults || 2,
          childCount: bookingDetails.searchCriteria.children || 0,
          passengerNationalityCode: bookingDetails.searchCriteria.countryCode
            ? parseInt(bookingDetails.searchCriteria.countryCode)
            : 81,
          passengerCountryOfResidenceCode: bookingDetails.searchCriteria.countryCode
            ? parseInt(bookingDetails.searchCriteria.countryCode)
            : 72,
          rateBaseId: parseInt(bookingDetails.selectedRoom.rateBaseId) || 0,
          roomTypeCode: bookingDetails.selectedRoom.roomTypeCode,
          allocationDetails: bookingDetails.selectedRoom.allocationDetails,
        },
      ],
    };

    try {
      const response = await dispatch(getRoomsWithBlocking(requestBody)).unwrap();
      const hasBookableRate = response.some((room) =>
        room.rateBases?.some((rate) => rate.isBookable !== false)
      );
      if (!hasBookableRate) throw new Error('No bookable rates available.');
      setRoomBlocked(true);
      toast.success('Room blocked successfully.');
      return true;
    } catch (error) {
      toast.error(`Failed to block room: ${error.message}. Please try again.`);
      setRoomBlocked(false);
      return false;
    }
  }, [bookingDetails, dispatch]);

  const saveBookingHandler = useCallback(async (values) => {
    if (!bookingDetails.searchCriteria?.dateRange?.startDate || !bookingDetails.searchCriteria?.dateRange?.endDate) {
      toast.error('Please select valid check-in and check-out dates.');
      return { success: false, bookingCode: null, serviceCode: null };
    }
    if (!roomBlocked || !values.passengers?.length) {
      toast.error('Please complete all required guest details and ensure room is blocked.');
      return { success: false, bookingCode: null, serviceCode: null };
    }

    setBookingStatus((prev) => ({ ...prev, saving: true, error: null }));
    try {
      const titleToIdMap = Object.fromEntries(titleOptions.map(opt => [opt.name, opt.id]));
      const requestBody = {
        fromDate: formatDate(bookingDetails.searchCriteria.dateRange?.startDate),
        toDate: formatDate(bookingDetails.searchCriteria.dateRange?.endDate),
        currencyCode: '394',
        hotelId: parseInt(bookingDetails.hotelDetails.id),
        roomCount: bookingDetails.searchCriteria.rooms || 1,
        roomRequests: [
          {
            adultsCount: bookingDetails.searchCriteria.adults || 2,
            childCount: bookingDetails.searchCriteria.children || 0,
            passengerNationalityCode: bookingDetails.searchCriteria.countryCode
              ? parseInt(bookingDetails.searchCriteria.countryCode)
              : 81,
            passengerCountryOfResidenceCode: bookingDetails.searchCriteria.countryCode
              ? parseInt(bookingDetails.searchCriteria.countryCode)
              : 72,
            selectedRateBasis: 0,
            adultsCode: bookingDetails.searchCriteria.adults || 2,
            roomTypeCode: bookingDetails.selectedRoom.roomTypeCode,
            allocationDetails: bookingDetails.selectedRoom.allocationDetails,
            passengersDetails: values.passengers.map((passenger) => ({
              isLeading: passenger === values.passengers[0],
              salutation: parseInt(titleToIdMap[passenger.title]),
              firstName: passenger.firstName,
              lastName: passenger.lastName,
            })),
          },
        ],
      };
      const response = await dispatch(saveBooking(requestBody)).unwrap();
      if (!response?.[0]?.successful || !response[0]?.returnedCode || !response[0]?.returnedServices?.[0]?.code) {
        throw new Error('The allocation has expired');
      }
      setBookingCode(response[0].returnedCode);
      setServiceCode(response[0].returnedServices[0].code);
      setBookingStatus((prev) => ({ ...prev, saving: false, saved: true }));
      toast.success('Booking saved successfully.');
      return {
        success: true,
        bookingCode: response[0].returnedCode,
        serviceCode: response[0].returnedServices[0].code,
      };
    } catch (error) {
      toast.error(`Failed to save booking: ${error.message}`);
      setBookingStatus((prev) => ({ ...prev, saving: false, error: error.message }));
      return { success: false, bookingCode: null, serviceCode: null };
    }
  }, [bookingDetails, roomBlocked, dispatch]);

  const bookItineraryNoConfirmHandler = useCallback(async (values, bookingCode, serviceCode) => {
    if (!bookingCode || !serviceCode || !values.passengers[0].email) {
      toast.error('Please ensure booking is saved and email is provided.');
      return false;
    }

    setBookingStatus((prev) => ({ ...prev, confirmingNo: true, error: null }));
    try {
      const requestBody = {
        fromDate: formatDate(bookingDetails.searchCriteria.dateRange?.startDate),
        toDate: formatDate(bookingDetails.searchCriteria.dateRange?.endDate),
        currencyCode: '394',
        hotelId: parseInt(bookingDetails.hotelDetails.id),
        roomCount: bookingDetails.searchCriteria.rooms || 1,
        bookingType: 2,
        roomRequests: [
          {
            adultsCount: bookingDetails.searchCriteria.adults || 2,
            childCount: bookingDetails.searchCriteria.children || 0,
            passengerNationalityCode: bookingDetails.searchCriteria.countryCode
              ? parseInt(bookingDetails.searchCriteria.countryCode)
              : 81,
            passengerCountryOfResidenceCode: bookingDetails.searchCriteria.countryCode
              ? parseInt(bookingDetails.searchCriteria.countryCode)
              : 72,
            allocationDetails: bookingDetails.selectedRoom.allocationDetails,
            bookingCode,
            serviceCode,
            confirmBooking: 'no',
            sendCommunicationToEmail: values.passengers[0].email,
          },
        ],
      };
      const response = await dispatch(bookItineraryWithConfirmNo(requestBody)).unwrap();
      setBookingStatus((prev) => ({ ...prev, confirmingNo: false, confirmedNo: true }));
      toast.success('Booking prepared successfully.');
      return true;
    } catch (error) {
      toast.error(`Failed to prepare booking: ${error.message}`);
      setBookingStatus((prev) => ({ ...prev, confirmingNo: false, error: error.message }));
      return false;
    }
  }, [bookingDetails, dispatch]);

  const executePhaseOne = useCallback(async (values) => {
    if (!roomBlocked || !values.passengers?.length || !values.passengers[0].email || !values.passengers[0].countryCode) {
      toast.error('Please complete all required guest details and ensure room is blocked.');
      return false;
    }

    setBookingStatus((prev) => ({ ...prev, saving: true, error: null }));
    try {
      const { success, bookingCode: newBookingCode, serviceCode: newServiceCode } = await saveBookingHandler(values);
      if (!success) return false;

      return new Promise((resolve) => {
        setTimeout(async () => {
          const confirmNoSuccess = await bookItineraryNoConfirmHandler(values, newBookingCode, newServiceCode);
          if (confirmNoSuccess) setSavedPassengerValues(values);
          resolve(confirmNoSuccess);
        }, 0);
      });
    } catch (error) {
      toast.error(`Booking process failed: ${error.message}`);
      setBookingStatus((prev) => ({ ...prev, saving: false, error: error.message }));
      return false;
    }
  }, [roomBlocked, saveBookingHandler, bookItineraryNoConfirmHandler]);

  const getMissingFields = useCallback((values, errors) => {
    const missingFields = [];
    values.passengers.forEach((passenger, index) => {
      const passengerErrors = errors.passengers?.[index] || {};
      const isMainGuest = index === 0;
      const requiredFields = [
        { field: 'firstName', label: 'First Name' },
        { field: 'lastName', label: 'Last Name' },
        { field: 'title', label: 'Title' },
      ];

      if (isMainGuest) {
        requiredFields.push(
          { field: 'email', label: 'Email' },
          { field: 'phoneNumber', label: 'Phone Number' },
          { field: 'phoneCode', label: 'Phone Country Code' },
          { field: 'streetName', label: 'Street Name' },
          { field: 'houseNumber', label: 'House Number' },
          { field: 'postalCode', label: 'Postal Code' },
          { field: 'city', label: 'City' },
          { field: 'country', label: 'Country' },
          { field: 'countryCode', label: 'Country Code' },
        );
        if (!passenger.sameNationality) {
          requiredFields.push(
            { field: 'nationality', label: 'Nationality' },
            { field: 'nationalityCode', label: 'Nationality Code' }
          );
        }
      }

      requiredFields.forEach(({ field, label }) => {
        if (passengerErrors[field]) {
          missingFields.push(`Guest ${index + 1} - ${label}: ${passengerErrors[field]}`);
        } else if (!passenger[field] || (field === 'title' && passenger[field] === 'Select here')) {
          missingFields.push(`Guest ${index + 1} - ${label}: Required field is missing`);
        }
      });

      if (isMainGuest && passenger.phoneCode && passenger.phoneNumber) {
        const phoneNumber = parsePhoneNumberFromString(`+${passenger.phoneCode}${passenger.phoneNumber}`);
        if (!phoneNumber || !phoneNumber.isValid()) {
          missingFields.push(`Guest 1 - Phone Number: Invalid phone number for the selected country`);
        }
      }
    });
    return missingFields;
  }, []);

  const handleChange = useCallback((field) => {
    setSelectChecked((prev) => ({ ...prev, [field]: !prev[field] }));
  }, []);

  const handlePaymentMethodChange = useCallback((method) => {
    setSelectedPaymentMethod(method);
  }, []);

  const handleSubmit = useCallback(async (values, { setSubmitting }) => {
  try {
    // Validate booking details
    if (!bookingDetails.hotelDetails?.id || !bookingDetails.selectedRoom?.roomTypeName) {
      toast.error('Invalid booking details. Please try again.');
      setSubmitting(false);
      return;
    }
    if (!bookingStatus.saved || !bookingStatus.confirmedNo) {
      toast.error('Please complete guest details and save booking first.');
      setSubmitting(false);
      return;
    }
    if (!selectedPaymentMethod) {
      toast.error('Please select a payment method.');
      setSubmitting(false);
      return;
    }

    setBookingStatus((prev) => ({ ...prev, confirmingYes: true, error: null }));

    const requestBody = {
      fromDate: formatDate(bookingDetails.searchCriteria.dateRange?.startDate),
      toDate: formatDate(bookingDetails.searchCriteria.dateRange?.endDate),
      currencyCode: '394',
      hotelId: parseInt(bookingDetails.hotelDetails.id),
      roomCount: bookingDetails.searchCriteria.rooms || 1,
      bookingType: 2,
      roomRequests: [
        {
          adultsCount: bookingDetails.searchCriteria.adults || 2,
          childCount: bookingDetails.searchCriteria.children || 0,
          passengerNationalityCode: bookingDetails.searchCriteria.countryCode
            ? parseInt(bookingDetails.searchCriteria.countryCode)
            : 81,
          passengerCountryOfResidenceCode: bookingDetails.searchCriteria.countryCode
            ? parseInt(bookingDetails.searchCriteria.countryCode)
            : 72,
          allocationDetails: bookingDetails.selectedRoom.allocationDetails,
          bookingCode,
          serviceCode,
          confirmBooking: 'yes',
          sendCommunicationToEmail: values.passengers[0].email,
        },
      ],
    };

    // Debug: Log the request body
    console.log('FinalDetails - bookItineraryWithConfirmYes Request:', requestBody);

    const response = await dispatch(bookItineraryWithConfirmYes(requestBody)).unwrap();

    // Debug: Log the API response
    console.log('FinalDetails - bookItineraryWithConfirmYes Response:', response);

    // Validate API response
    const isSuccessful = response.successful || (response.html && response.html.toLowerCase().includes('confirmed'));
    if (!isSuccessful) {
      throw new Error(response.errorMessage || 'Booking not successful. Please try again.');
    }

    const confirmationDetails = {
      html: response.html || '',
      successful: isSuccessful,
      totalPrice: response.totalPrice || bookingDetails.selectedRoom.totalCharge || 'N/A',
      currencyCode: response.currencyCode || bookingDetails.selectedRoom.currencyCode || 'CHF',
      roomPricePerDayDetailsList: response.roomPricePerDayDetailsList || bookingDetails.selectedRoom.availableDates || [],
      bookingCompletedAt: new Date().toLocaleString(),
      itineraryNumber: response.itineraryNumber || 'N/A',
      returnedCode: response.returnedCode || 'N/A',
      issueDeadline: response.issueDeadline || new Date(new Date().setDate(new Date().getDate() + 7)).toLocaleString(),
    };

    setBookingStatus((prev) => ({ ...prev, confirmingYes: false, confirmedYes: true }));

    const navigationState = {
      bookingDetails: {
        ...bookingDetails,
        guestDetails: values,
        html: response.html || '',
        addOns: selectChecked,
        selectedPaymentMethod,
        blockedRoomData,
        bookingCode,
        serviceCode,
        confirmationDetails,
      },
    };

    // Debug: Log navigation state
    console.log('FinalDetails - Navigation State:', navigationState);

    if (onNext) {
      onNext(navigationState);
    } else {
      navigate('/confirmation', { state: navigationState });
    }
  } catch (error) {
    // Improved error message for users
    const errorMessage = error.message.includes('Booking not successful') || error.message.includes('Details Hidden')
      ? 'We couldn’t confirm your booking due to a system issue. Please try again or contact <NAME_EMAIL>.'
      : `Error finalizing booking: ${error.message}. Please try again or contact support.`;

    toast.error(errorMessage, {
      position: 'top-center',
      autoClose: 7000,
      hideProgressBar: false,
      closeOnClick: true,
      pauseOnHover: true,
      draggable: true,
      style: {
        background: '#FFF5F5',
        color: '#B91C1C',
        border: '1px solid #FECACA',
        borderRadius: '8px',
        padding: '16px',
        fontSize: '14px',
      },
    });

    // Debug: Log the error
    console.error('FinalDetails - Booking Error:', error);

    setBookingStatus((prev) => ({ ...prev, confirmingYes: false, error: error.message }));
  } finally {
    setSubmitting(false);
  }
}, [bookingDetails, bookingStatus, selectedPaymentMethod, bookingCode, serviceCode, blockedRoomData, dispatch, navigate, onNext]);

  // Effects
  useEffect(() => {
    if (
      bookingDetails?.hotelDetails?.id &&
      bookingDetails?.selectedRoom?.roomTypeCode &&
      bookingDetails?.selectedRoom?.allocationDetails
    ) {
      blockRoom();
    } else {
      toast.error('Invalid room selection. Please select a valid room and try again.');
    }
  }, [blockRoom, bookingDetails]);

  // Render Error States
  if (
    !bookingDetails?.hotelDetails?.id ||
    !bookingDetails?.selectedRoom?.roomTypeCode ||
    !bookingDetails?.selectedRoom?.allocationDetails
  ) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-offWhite">
        <div className="text-center text-gray">
          <h3 className="text-lg font-semibold">Booking Details Unavailable</h3>
          <p className="text-sm mt-2">
            Please select a valid room from the hotel page to proceed with your booking.
          </p>
          <Link to="/" className="mt-4 inline-block bg-darkBlue text-white py-2 px-4 rounded-[8px]">
            Return to Home
          </Link>
        </div>
      </div>
    );
  }

  if (
    blockedRoomError || bookingError || itineraryNoConfirmError || itineraryConfirmError
  ) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-offWhite">
        <div className="text-center text-red">
          <h3 className="text-lg font-semibold">Error Processing Booking</h3>
          <p className="text-sm mt-2">
            {blockedRoomError || bookingError || itineraryNoConfirmError || itineraryConfirmError}
          </p>
          {blockedRoomError && (
            <ButtonCom variant="secondary" size="md" width="fixed" onClick={() => blockRoom()} className="mt-4 ml-6">
              Retry Room Blocking
            </ButtonCom>
          )}
          {bookingError && (
            <ButtonCom
              variant="secondary"
              size="md"
              width="fixed"
              onClick={() => saveBookingHandler(initialValues)}
              className="mt-4 ml-4"
            >
              Retry Saving Booking
            </ButtonCom>
          )}
          {itineraryNoConfirmError && (
            <ButtonCom
              variant="secondary"
              size="md"
              width="fixed"
              onClick={() => bookItineraryNoConfirmHandler(initialValues, bookingCode, serviceCode)}
              className="mt-4 ml-4"
            >
              Retry Preparing Booking
            </ButtonCom>
          )}
          <Link to="/" className="mt-4 inline-block bg-darkBlue text-white py-2 px-4 rounded-[8px] ml-4">
            Return to Home
          </Link>
        </div>
      </div>
    );
  }

  const isLoading = blockedRoomLoading || bookingStatus.confirmingYes;

  if (isLoading) {
    return (
      <div className="w-full h-screen flex items-center justify-center bg-offWhite">
        <div className="text-center text-darkBlue">
          <h3 className="text-lg font-semibold">
            {blockedRoomLoading ? 'Blocking Room...' : 'Finalizing Booking...'}
          </h3>
          <p className="text-sm mt-2">Please wait while we process your request.</p>
        </div>
      </div>
    );
  }

  // Main Render
  return (
    <>
      <ToastContainer position="top-right" autoClose={5000} hideProgressBar={false} closeOnClick pauseOnHover />
      <Formik
        initialValues={initialValues}
        enableReinitialize={true}
        validationSchema={VALIDATION_SCHEMA}
        validateOnBlur
        validateOnChange
        onSubmit={handleSubmit}
        context={{ isMainGuest: (index) => index === 0 }}
      >
        {({ isSubmitting, values, errors, touched, handleChange, setFieldValue }) => {
          const handleSaveClick = () => {
            const missingFields = getMissingFields(values, errors);
            setFieldErrors(missingFields.length > 0 ? missingFields : null);
            if (missingFields.length === 0) {
              executePhaseOne(values);
            }
          };

          return (
            <Form>
              <TrackingStepper currentStep={2} />
              <div className="max-w-[1100px] w-full mx-auto p-4">
                <div className="mb-6">
                  <div className="flex flex-col lg:flex-row gap-6">
                    <div className="w-full lg:w-[650px] space-y-3">
                      <SignInPrompt />
                      <div className="lg:hidden">
                        <HotelDetails bookingDetails={bookingDetails} />
                      </div>
                      <Persondetails
                        formikProps={{ values, errors, touched, handleChange, setFieldValue }}
                        passengerCount={passengerCount}
                      />
                      <div className="w-full p-6 bg-white rounded-2xl border border-darkBlue">
                        <h2 className="font-medium text-lg">Complete Guest Details</h2>
                        <p className="text-sm text-smokyGray mt-2">
                          Please complete and save guest details to proceed to payment. Required fields are marked with
                          an asterisk (*).
                        </p>
                        <ButtonCom
                          variant="primary"
                          size="md"
                          width="fixed"
                          onClick={handleSaveClick}
                          disabled={isSubmitting || bookingStatus.saving}
                          className="mt-4"
                        >
                          {isSubmitting || bookingStatus.saving ? 'Saving...' : 'Save Guest Details'}
                        </ButtonCom>
                        {fieldErrors && (
                          <div className="mt-4 text-red text-sm bg-red/15 p-4 rounded-lg">
                            <p className="font-semibold">Please correct the following errors:</p>
                            <ul className="list-disc pl-5 mt-2">
                              {fieldErrors.map((error, idx) => (
                                <li key={idx}>{error}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                      </div>
                      {bookingStatus.saved && bookingStatus.confirmedNo && (
                        <PaymentMethods onChange={handlePaymentMethodChange} />
                      )}
                      <GoodToKnow bookingDetails={bookingDetails} />
                      <div className="lg:hidden">
                        <PriceSummary bookingDetails={bookingDetails} />
                      </div>
                      <div className="flex justify-end mt-4 px-4 sm:px-0">
                        <ButtonCom
                          variant="primary"
                          size="md"
                          width="fixed"
                          icon="chevronRight"
                          iconPosition="right"
                          type="submit"
                          disabled={
                            isSubmitting ||
                            !roomBlocked ||
                            !bookingStatus.saved ||
                            !bookingStatus.confirmedNo ||
                            !selectedPaymentMethod ||
                            Object.keys(errors).length > 0
                          }
                        >
                          Confirm Booking
                        </ButtonCom>
                      </div>
                    </div>
                    <div className="hidden lg:block lg:order-2 w-full lg:w-[480px] space-y-4 sticky top-2 max-h-[calc(100vh-10px)] overflow-y-auto no-scrollbar">
                      <HotelDetails bookingDetails={bookingDetails} />
                      <PriceSummary bookingDetails={bookingDetails} />
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          );
        }}
      </Formik>
    </>
  );
};

export default FinalDetails;