import React, { useState, useEffect } from 'react';
import { Radio, RadioGroup, FormControlLabel } from '@mui/material';
import { Clipboard } from 'lucide-react';
import { toast } from 'react-toastify';

const PaymentForm = ({
  title = 'Payment Methods',
  paymentMethods = [],
  logoMethods = [],
  onChange,
}) => {
  const [paymentMethod, setPaymentMethod] = useState('');
  const [cardNumber, setCardNumber] = useState('');
  const [expirationDate, setExpirationDate] = useState('');
  const [cvv, setCvv] = useState('');
  const [errors, setErrors] = useState({});
  const [selectedCard, setSelectedCard] = useState('');

  // Luhn Algorithm for card number validation
  const isValidCardNumber = (number) => {
    const digits = number.replace(/\D/g, '');
    if (!/^\d{15,16}$/.test(digits)) return false;
    let sum = 0;
    let isEven = false;
    for (let i = digits.length - 1; i >= 0; i--) {
      let digit = parseInt(digits[i]);
      if (isEven) {
        digit *= 2;
        if (digit > 9) digit -= 9;
      }
      sum += digit;
      isEven = !isEven;
    }
    return sum % 10 === 0;
  };

  // Validate expiration date (MM/YY must be in the future)
  const isValidExpirationDate = (value) => {
    if (!/^\d{2}\/\d{2}$/.test(value)) return false;
    const [month, year] = value.split('/').map(Number);
    if (month < 1 || month > 12) return false;
    const currentDate = new Date();
    const currentYear = currentDate.getFullYear() % 100;
    const currentMonth = currentDate.getMonth() + 1;
    return year > currentYear || (year === currentYear && month >= currentMonth);
  };

  // Validate CVV (3 or 4 digits based on card type)
  const isValidCvv = (value, cardNumber) => {
    const digits = cardNumber.replace(/\D/g, '');
    const isAmex = digits.startsWith('34') || digits.startsWith('37');
    return isAmex ? /^\d{4}$/.test(value) : /^\d{3}$/.test(value);
  };

  const formatCardNumber = (value) => {
  const digits = value.replace(/\D/g, '');

  let maxLength = 16;
  if (selectedCard === 'Amex') maxLength = 15;

  const limited = digits.slice(0, maxLength);
  const groups = selectedCard === 'Amex'
    ? [limited.slice(0, 4), limited.slice(4, 10), limited.slice(10)].filter(Boolean)
    : limited.match(/.{1,4}/g) || [];

  return groups.join(' ');
};

  const handleChange = (e) => {
    const formattedValue = formatCardNumber(e.target.value);
    setCardNumber(formattedValue);
    setErrors((prev) => ({ ...prev, cardNumber: '' }));
  };

  const handleExpirationChange = (e) => {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 4) value = value.slice(0, 4);
    if (value.length >= 3) value = `${value.slice(0, 2)}/${value.slice(2)}`;
    setExpirationDate(value);
    setErrors((prev) => ({ ...prev, expirationDate: '' }));
  };

  const handleCvvChange = (e) => {
    const maxLength = selectedCard === 'Amex' ? 4 : 3;
    const value = e.target.value.replace(/\D/g, '').slice(0, maxLength);
    setCvv(value);
    setErrors((prev) => ({ ...prev, cvv: '' }));
  };

  const validateFields = () => {
    const newErrors = {};
    if (paymentMethod === 'creditCard') {
      if (!cardNumber || !isValidCardNumber(cardNumber)) {
        newErrors.cardNumber = 'Invalid card number';
      }
      if (!expirationDate || !isValidExpirationDate(expirationDate)) {
        newErrors.expirationDate = 'Invalid or expired date (MM/YY)';
      }
      if (!cvv || !isValidCvv(cvv, cardNumber)) {
        newErrors.cvv = 'Invalid CVV';
      }
      if (!selectedCard) {
        newErrors.cardType = 'Please select a card type';
      }
    }
    setErrors(newErrors);
    if (Object.keys(newErrors).length > 0) {
      toast.error('Please correct payment details.', { autoClose: 5000 });
      return false;
    }
    return true;
  };

  const handlePaymentMethodChange = (value) => {
    setPaymentMethod(value);
    if (value !== 'creditCard') {
      setCardNumber('');
      setExpirationDate('');
      setCvv('');
      setSelectedCard('');
      setErrors({});
      onChange({
        paymentMethod: value,
        cardNumber: '',
        expirationDate: '',
        cvv: '',
        cardType: '',
      });
    } else {
      setErrors({});
      onChange({
        paymentMethod: value,
        cardNumber,
        expirationDate,
        cvv,
        cardType: selectedCard,
      });
    }
  };

  const handleCardSelection = (cardType) => {
    setSelectedCard(cardType);
    setErrors((prev) => ({ ...prev, cardType: '' }));
    toast.info(`Selected card: ${cardType}`, { autoClose: 2000 });
    onChange({
      paymentMethod: 'creditCard',
      cardNumber,
      expirationDate,
      cvv,
      cardType,
    });
  };

  // Mock integration for Post Finance and Digital Payments
  const handleConnectPayment = (method) => {
    toast.info(`Connecting to ${method}...`, { autoClose: 2000 });
    setTimeout(() => {
      toast.success(`${method} connected successfully!`, { autoClose: 5000 });
      onChange({
        paymentMethod: method.toLowerCase(),
        cardNumber: '',
        expirationDate: '',
        cvv: '',
        cardType: '',
      });
    }, 1000);
  };

  useEffect(() => {
    if (paymentMethod === 'creditCard' && validateFields()) {
      onChange({
        paymentMethod,
        cardNumber,
        expirationDate,
        cvv,
        cardType: selectedCard,
      });
    }
  }, [cardNumber, expirationDate, cvv, paymentMethod, selectedCard]);

  return (
    <div className="w-full mx-auto p-6 bg-white rounded-2xl border border-darkBlue space-y-6">
      <h2 className="font-medium text-lg">{title}</h2>

      {/* Invoice Option */}
      <div className="border border-border rounded-xl overflow-hidden mb-4">
        <div
          className={`flex items-center justify-between p-4 cursor-pointer ${paymentMethod === 'invoice' ? 'bg-gray-50' : ''}`}
          onClick={() => handlePaymentMethodChange('invoice')}
        >
          <RadioGroup
            name="paymentMethod"
            value={paymentMethod}
            onChange={(e) => handlePaymentMethodChange(e.target.value)}
          >
            <FormControlLabel
              value="invoice"
              control={<Radio sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }} />}
              label={
                <div className="flex items-center justify-between w-[500px]">
                  <span className="text-base font-medium text-smokyGray">Invoice</span>
                  <Clipboard className="h-5 w-5" />
                </div>
              }
              className="m-0 w-full"
            />
          </RadioGroup>
        </div>
      </div>

      {/* Post Finance Option */}
      <div className="border border-border rounded-xl overflow-hidden mb-4">
        <div
          className={`flex items-center justify-between p-4 cursor-pointer ${paymentMethod === 'postFinance' ? 'bg-gray-50' : ''}`}
          onClick={() => handlePaymentMethodChange('postFinance')}
        >
          <RadioGroup
            name="paymentMethod"
            value={paymentMethod}
            onChange={(e) => handlePaymentMethodChange(e.target.value)}
          >
            <FormControlLabel
              value="postFinance"
              control={<Radio sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }} />}
              label={
                <div className="flex items-center justify-between w-[500px]">
                  <span className="text-base font-medium text-smokyGray">Post Finance</span>
                  <img src={paymentMethods.find(m => m.alt === 'Post Finance')?.src} alt="Post Finance" className="h-5 w-14" />
                </div>
              }
              className="m-0 w-full"
            />
          </RadioGroup>
        </div>
        {paymentMethod === 'postFinance' && (
          <div className="p-4 border-t border-border">
            <div className="flex items-center justify-between mb-4">
              <p className="text-sm text-smokyGray">Connect your Post Finance account to complete the payment.</p>
            </div>
            <button
              type="button"
              className="bg-blue-50 text-darkBlue rounded-lg py-2 px-4 text-sm font-medium"
              onClick={() => handleConnectPayment('Post Finance')}
            >
              Connect Post Finance
            </button>
          </div>
        )}
      </div>

      {/* Credit Card Option */}
      <div className="border border-border rounded-xl overflow-hidden mb-4">
        <div
          className={`flex items-center justify-between p-4 cursor-pointer ${paymentMethod === 'creditCard' ? 'bg-gray-50' : ''}`}
          onClick={() => handlePaymentMethodChange('creditCard')}
        >
          <RadioGroup
            name="paymentMethod"
            value={paymentMethod}
            onChange={(e) => handlePaymentMethodChange(e.target.value)}
          >
            <FormControlLabel
              value="creditCard"
              control={<Radio sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }} />}
              label={
                <div className="flex items-center justify-between w-[500px]">
                  <span className="text-base font-medium text-smokyGray">Credit Card Details</span>
                  <div className="flex items-center gap-2">
                    {logoMethods
                      .filter((method) => ['Visa Logo', 'MasterCard Logo', 'Amex Logo'].includes(method.alt))
                      .map((method, index) => (
                        <img key={index} src={method.src} alt={method.alt} className="h-3" />
                      ))}
                  </div>
                </div>
              }
              className="m-0 w-full"
            />
          </RadioGroup>
        </div>
        {paymentMethod === 'creditCard' && (
          <div className="p-4 border-t border-border">
            <div className="flex flex-wrap items-center gap-4 mb-4">
              {paymentMethods
                .filter((method) => ['Visa', 'MasterCard', 'Amex'].includes(method.alt))
                .map((method, index) => (
                  <button
                    key={index}
                    type="button"
                    className={`border rounded-lg p-2 flex items-center justify-center ${selectedCard === method.value ? 'border-darkBlue bg-blue-50' : 'border-gray-300'
                      }`}
                    onClick={() => handleCardSelection(method.value)}
                  >
                    <img
                      src={method.src}
                      alt={method.alt}
                      className="h-6 w-8 object-contain"
                    />
                  </button>
                ))}
              <button type="button" className="text-orange text-sm font-medium ml-auto flex items-center gap-2">
                <img src={paymentMethods.find(m => m.alt === 'Scan Icon')?.src} alt="Scan Icon" className="w-4 h-4 object-contain" />
                Scan Card
              </button>
            </div>
            {errors.cardType && <p className="text-red-500 text-xs mb-2">{errors.cardType}</p>}
            <div className="grid grid-cols-1">
              <div className="border border-darkBlue rounded-2xl p-3">
                <label className="text-xs text-smokyGray font-light">Card Number</label>
                <input
                  type="text"
                  placeholder="XXXX XXXX XXXX XXXX"
                  className={`w-full text-smokyGray text-base focus:outline-none ${errors.cardNumber ? 'border-red-500' : ''}`}
                  value={cardNumber}
                  onChange={handleChange}
                />
                {errors.cardNumber && <p className="text-red-500 text-xs mt-1">{errors.cardNumber}</p>}
              </div>
            </div>
            <div className="grid grid-cols-2 mt-2 space-x-3">
              <div className="border border-darkBlue rounded-2xl p-3 flex flex-col gap-1">
                <label className="text-xs text-smokyGray font-light" htmlFor="expiration">
                  Expiration Date
                </label>
                <input
                  id="expiration"
                  type="text"
                  inputMode="numeric"
                  maxLength={5}
                  placeholder="MM/YY"
                  className={`w-full text-smokyGray text-base focus:outline-none placeholder-gray-400 ${errors.expirationDate ? 'border-red-500' : ''}`}
                  value={expirationDate}
                  onChange={handleExpirationChange}
                  aria-label="Enter expiration date in MM/YY format"
                  autoComplete="cc-exp"
                />
                {errors.expirationDate && <p className="text-red-500 text-xs mt-1">{errors.expirationDate}</p>}
              </div>
              <div className="border border-darkBlue rounded-2xl p-3 flex flex-col gap-1">
                <label className="text-xs text-smokyGray font-light" htmlFor="cvv">
                  CVV
                </label>
                <input
                  id="cvv"
                  type="password"
                  inputMode="numeric"
                  maxLength={4}
                  placeholder="XXX"
                  className={`w-full text-smokyGray text-base focus:outline-none placeholder-gray-400 ${errors.cvv ? 'border-red-500' : ''}`}
                  value={cvv}
                  onChange={handleCvvChange}
                  aria-label="Enter your CVV security code"
                  autoComplete="cc-csc"
                />
                {errors.cvv && <p className="text-red-500 text-xs mt-1">{errors.cvv}</p>}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Digital Payment Methods */}
      <div className="border border-border rounded-xl overflow-hidden w-full">
        <div
          className={`flex items-center justify-between p-4 cursor-pointer ${paymentMethod === 'digitalPayment' ? 'bg-gray-50' : ''}`}
          onClick={() => handlePaymentMethodChange('digitalPayment')}
        >
          <RadioGroup
            name="paymentMethod"
            value={paymentMethod}
            onChange={(e) => handlePaymentMethodChange(e.target.value)}
          >
            <FormControlLabel
              value="digitalPayment"
              control={<Radio sx={{ color: '#5A5A5A', '&.Mui-checked': { color: '#024575' } }} />}
              label={
                <div className="flex items-center justify-between w-[500px]">
                  <span className="text-base font-medium text-smokyGray">Digital Payment Methods</span>
                  <div className="flex items-center gap-2">
                    {logoMethods
                      .filter((method) => ['PayPal Logo', 'Google Pay Logo', 'Apple Pay Logo', 'Twint Logo'].includes(method.alt))
                      .map((method, index) => (
                        <img key={index} src={method.src} alt={method.alt} className="h-5" />
                      ))}
                  </div>
                </div>
              }
              className="m-0 w-full"
            />
          </RadioGroup>
        </div>
        {paymentMethod === 'digitalPayment' && (
          <div className="p-4 border-t border-border">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              {paymentMethods
                .filter((method) => ['PayPal', 'Google Pay', 'Apple Pay', 'Twint'].includes(method.alt))
                .map((method, index) => (
                  <button
                    type="button"
                    key={index}
                    className="border border-darkBlue rounded-xl p-3 flex items-center justify-center"
                    onClick={() => handleConnectPayment(method.alt)}
                  >
                    <img src={method.src} alt={method.alt} className="h-8" />
                  </button>
                ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PaymentForm;