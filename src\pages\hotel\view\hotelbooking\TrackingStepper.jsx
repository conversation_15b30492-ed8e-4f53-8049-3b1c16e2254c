import React from 'react';
import { useNavigate } from 'react-router-dom';
import StepperWrapper from '../../../../components/ui/StepperWrapper';

const TrackingStepper = ({ currentStep, pageContent }) => {
  const steps = [
    { number: 1, title: 'Your selection' },
    { number: 2, title: 'Your details' },
    { number: 3, title: 'Finish booking' },
  ];

  const navigate = useNavigate();

  return (
    <StepperWrapper
      steps={steps}
      currentStep={currentStep}
      onBack={() => navigate(-1)}
    >
      {pageContent}
    </StepperWrapper>
  );
};

export default TrackingStepper;