import { Ch<PERSON>ron<PERSON><PERSON><PERSON>, ChevronR<PERSON>, <PERSON> } from "lucide-react";
import React, { useState } from "react";

const PeopleReview = ({ reviews }) => {
  const [expandedReviews, setExpandedReviews] = useState({}); // Track expanded state for each review
  const [showAllReviews, setShowAllReviews] = useState(false); // Track whether to show all reviews

  const toggleReadMore = (index) => {
    setExpandedReviews((prev) => ({
      ...prev,
      [index]: !prev[index],
    }));
  };

  // Determine how many reviews to display
  const displayedReviews = showAllReviews ? reviews : reviews.slice(0, 3);

  return (
    <>
      {displayedReviews.map((review, index) => (
        <div key={index} className="p-4 border border-border rounded-xl w-full flex flex-col space-y-10 text-darkBlue">
          <div className="flex flex-row justify-between gap-x-8">
            <div className="flex items-end">
              <img
                src={review?.url}
                alt={review?.name}
                className="rounded-full w-16 border border-darkBlue object-cover"
              />
            </div>
            <div className="flex flex-col space-y-1 w-full items-end">
              {/* rating */}
              <div className="flex flex-row space-x-1">
                {[...Array(5)].map((_, idx) => (
                  <svg
                    key={idx}
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    fill={idx < review.rating ? "orange" : "white"}
                    stroke="orange"
                    strokeWidth="1.5"
                    className={`transition-all duration-300 transform ${idx < review.rating ? "scale-110" : ""
                      }`}
                    viewBox="0 0 24 24"
                  >
                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                  </svg>
                ))}
              </div>
              {/* date */}
              <div className="font-thin">{review?.date}</div>
              <div className="text-left font-medium text-xl w-full">
                {review?.name}
              </div>
            </div>
          </div>
          <div className="">
            <h6 className="font-medium text-lg">{review?.topic} </h6>
            <div>
              {expandedReviews[index] ? (
                <>
                  <div className="text-lightGray font-thin">
                    {review?.review}
                  </div>
                  {/* Review count section */}
                  <div className="flex flex-col space-y-4 text-smokyGray md:w-2/3 mt-4">
                    {review?.reviewCount?.map((reviewItem, idx) => (
                      <div
                        key={idx}
                        className="px-4 py-1 rounded-lg text-sm border border-borderGray text-nowrap w-full flex flex-row justify-between items-center"
                      >
                        <div className="flex gap-2 items-center">
                          <span>{reviewItem.label}</span>
                        </div>
                        <div className="flex flex-row space-x-1">
                          {[...Array(5)].map((_, starIdx) => (
                            <svg
                              key={starIdx}
                              xmlns="http://www.w3.org/2000/svg"
                              width="20"
                              height="20"
                              fill={
                                starIdx < reviewItem.value ? "orange" : "white"
                              }
                              stroke="orange"
                              strokeWidth="1.5"
                              className={`transition-all duration-300 transform ${starIdx < reviewItem.value ? "scale-110" : ""
                                }`}
                              viewBox="0 0 24 24"
                            >
                              <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z" />
                            </svg>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </>
              ) : (
                <div className="text-lightGray font-thin">
                  {review?.review.slice(0, 450)}...
                </div>
              )}
              <button
                onClick={() => toggleReadMore(index)}
                className="text-darkBlue hover:underline flex flex-row justify-end items-center w-full"
              >
                {expandedReviews[index] ? (
                  <>
                    <ChevronLeft className="w-4 h-4" />
                    <span>Show Less</span>
                  </>
                ) : (
                  <>
                    <span>Read More</span>
                    <ChevronRight className="w-4 h-4" />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      ))}

      {/* Show "See all reviews" button if there are more than 3 reviews and not all are showing */}
      {reviews.length > 3 && (
        <div className="flex justify-start mt-6">
          {showAllReviews ? (
            <button
              onClick={() => setShowAllReviews(false)}
              className="px-6 py-2 border border-darkBlue text-darkBlue rounded-[2px] hover:bg-opacity-90 transition-all"
            >
              Hide reviews
            </button>
          ) : (
            <button
              onClick={() => setShowAllReviews(true)}
              className="px-6 py-2 border border-darkBlue text-darkBlue rounded-[2px] hover:bg-opacity-90 transition-all"
            >
              See all reviews
            </button>
          )}
        </div>
      )}
    </>
  );
};

export default PeopleReview;