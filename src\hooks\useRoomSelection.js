import { useState, useCallback, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { calculateNightsAndTravelers } from '../utils/hotelUtils';

export const useRoomSelection = (hotelDetails, searchCriteria, roomTypes) => {
  const navigate = useNavigate();
  const [selectedRooms, setSelectedRooms] = useState({});
  const [travelerConfig, setTravelerConfig] = useState({
    rooms: searchCriteria?.rooms || 1,
    adults: searchCriteria?.adults || 2,
    children: searchCriteria?.children || 0,
  });
  const [dateRange, setDateRange] = useState({
    startDate: searchCriteria?.dateRange?.startDate || new Date('2025-05-25'),
    endDate: searchCriteria?.dateRange?.endDate || new Date('2025-05-28'),
    key: 'selection',
  });
  const [flexibleDays, setFlexibleDays] = useState(searchCriteria?.flexibleDays || 0);

  const { nights, displayText, dateRangeText } = useMemo(() =>
    calculateNightsAndTravelers(dateRange, travelerConfig), [dateRange, travelerConfig]
  );

  const handleRoomSelect = useCallback((roomTypeIndex, configIndex, newCount) => {
    const roomId = `${roomTypeIndex}-${configIndex}`;
    setSelectedRooms((prev) => {
      const updated = { ...prev };
      if (newCount <= 0) {
        delete updated[roomId];
      } else if (newCount <= travelerConfig.rooms) {
        updated[roomId] = newCount;
      }
      return updated;
    });
  }, [travelerConfig.rooms]);

  const calculateTotalPrice = useCallback(() => {
    if (!roomTypes || !Array.isArray(roomTypes)) return '0.00';
    let total = 0;
    Object.entries(selectedRooms).forEach(([roomId, count]) => {
      const [roomTypeIndex, configIndex] = roomId.split('-').map(Number);
      const config = roomTypes[roomTypeIndex]?.configurations[configIndex];
      if (config && config.totalPrice !== 'N/A') {
        const price = parseFloat(config.totalPrice.replace('CHF ', ''));
        total += price * count;
      }
    });
    return total.toFixed(2);
  }, [selectedRooms, roomTypes]);

  const handleReserve = useCallback((roomTypeName, config, roomTypeCode) => {
    const totalPrice = calculateTotalPrice();
    if (!totalPrice || totalPrice === '0.00') {
      alert('Please select a valid room with pricing.');
      return;
    }
    const selectedRoom = {
      roomTypeName,
      totalCharge: parseFloat(totalPrice) || 0,
      rateBaseId: config.rateBaseId || 0,
      roomTypeCode,
      allocationDetails: config.allocationDetails || '',
      isBookable: config.isBookable !== undefined ? config.isBookable : true,
      benefits: config.benefits || [],
      tariffNotes: config.tariffNotes || '',
      totalTaxes: config.totalTaxes || '0.00',
      selectedRoomCounts: selectedRooms,
      cancellationRules: config.cancellationRules || [],
      availableDates: config.availableDates || [],
      rateBaseName: config.rateBaseName || 'Room Only',
      currencyCode: config.currencyCode || 'USD',
    };
    const bookingDetails = {
      hotelDetails,
      searchCriteria: {
        ...searchCriteria,
        dateRange,
        adults: travelerConfig.adults,
        children: travelerConfig.children,
        rooms: travelerConfig.rooms,
      },
      selectedRoom,
      nights,
      dateRangeText,
    };
    navigate('/finaldetails', { state: { bookingDetails } });
  }, [calculateTotalPrice, dateRange, travelerConfig, selectedRooms, hotelDetails, searchCriteria, navigate, nights, dateRangeText]);

  const handleTravelerChange = useCallback((field, value) => {
    setTravelerConfig((prev) => ({ ...prev, [field]: value }));
  }, []);

  const handleChangeSearch = useCallback(() => {
    const updatedSearchCriteria = {
      ...searchCriteria,
      dateRange,
      adults: travelerConfig.adults,
      children: travelerConfig.children,
      rooms: travelerConfig.rooms,
      flexibleDays,
    };
    navigate(`/hotel-and-flight/${hotelDetails.id}`, {
      state: { formData: updatedSearchCriteria, previousSearch: searchCriteria },
    });
  }, [navigate, hotelDetails.id, searchCriteria, dateRange, travelerConfig, flexibleDays]);

  return {
    selectedRooms,
    setSelectedRooms,
    travelerConfig,
    setTravelerConfig,
    dateRange,
    setDateRange,
    flexibleDays,
    setFlexibleDays,
    nights,
    displayText,
    dateRangeText,
    handleRoomSelect,
    calculateTotalPrice,
    handleReserve,
    handleTravelerChange,
    handleChangeSearch,
  };
};