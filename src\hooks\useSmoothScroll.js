import { useCallback, useRef, useEffect } from 'react';
import { motion, useAnimation } from 'framer-motion';

export const useSmoothScroll = (roomTypes, setSelectedHeader) => {
  const roomRefs = useRef({});
  const controls = useAnimation();

  const scrollToRoom = useCallback((roomId) => {
    const roomElement = roomRefs.current[roomId];
    if (roomElement) {
      const isMobile = window.innerWidth < 768;
      const headerHeight = isMobile ? 120 : 80;
      const tabsHeight = isMobile ? 60 : 50;
      const totalOffset = headerHeight + tabsHeight + 20;
      const elementRect = roomElement.getBoundingClientRect();
      const absoluteElementTop = elementRect.top + window.pageYOffset;
      const targetPosition = absoluteElementTop - totalOffset;

      controls.start({
        y: targetPosition,
        transition: { duration: 0.8, ease: 'easeInOut' },
      }).then(() => {
        setSelectedHeader(roomId);
        roomElement.style.transform = 'scale(1.02)';
        roomElement.style.transition = 'transform 0.3s ease';
        setTimeout(() => {
          roomElement.style.transform = 'scale(1)';
          setTimeout(() => {
            roomElement.style.transition = '';
          }, 300);
        }, 200);
      });
    }
  }, [controls, setSelectedHeader]);

  useEffect(() => {
    const observerOptions = {
      root: null,
      rootMargin: '-20% 0px -70% 0px',
      threshold: 0.1,
    };

    const observerCallback = (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const roomId = entry.target.id.replace('room-', '');
          setSelectedHeader(roomId);
        }
      });
    };

    const observer = new IntersectionObserver(observerCallback, observerOptions);
    Object.values(roomRefs.current).forEach((roomElement) => {
      if (roomElement) observer.observe(roomElement);
    });

    return () => observer.disconnect();
  }, [roomTypes, setSelectedHeader]);

  return { roomRefs, scrollToRoom, controls };
};